version: '3.8'

services:
  # PHP/Laravel Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: company-crawler-app
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - ./:/var/www
      - ./docker/php/local.ini:/usr/local/etc/php/conf.d/local.ini
    networks:
      - company-crawler
    depends_on:
      - database
      - redis
    environment:
      - APP_ENV=production
      - APP_DEBUG=false
      - DB_HOST=database
      - DB_DATABASE=company_crawler
      - DB_USERNAME=crawler_user
      - DB_PASSWORD=crawler_password
      - REDIS_HOST=redis
      - QUEUE_CONNECTION=redis

  # Nginx Web Server
  webserver:
    image: nginx:alpine
    container_name: company-crawler-nginx
    restart: unless-stopped
    ports:
      - "8000:80"
    volumes:
      - ./:/var/www
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/sites/:/etc/nginx/sites-available
      - ./docker/nginx/sites/default.conf:/etc/nginx/sites-enabled/default
    depends_on:
      - app
    networks:
      - company-crawler

  # MySQL Database
  database:
    image: mysql:8.0
    container_name: company-crawler-mysql
    restart: unless-stopped
    tty: true
    ports:
      - "3306:3306"
    environment:
      MYSQL_DATABASE: company_crawler
      MYSQL_USER: crawler_user
      MYSQL_PASSWORD: crawler_password
      MYSQL_ROOT_PASSWORD: root_password
    volumes:
      - dbdata:/var/lib/mysql
      - ./docker/mysql/my.cnf:/etc/mysql/my.cnf
    networks:
      - company-crawler

  # Redis Cache & Queue
  redis:
    image: redis:7-alpine
    container_name: company-crawler-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redisdata:/data
    networks:
      - company-crawler

  # Queue Worker
  queue:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: company-crawler-queue
    restart: unless-stopped
    command: php artisan queue:work redis --sleep=3 --tries=3 --max-time=7200 --timeout=7200
    working_dir: /var/www
    volumes:
      - ./:/var/www
    depends_on:
      - app
      - database
      - redis
    environment:
      - APP_ENV=production
      - DB_HOST=database
      - DB_DATABASE=company_crawler
      - DB_USERNAME=crawler_user
      - DB_PASSWORD=crawler_password
      - REDIS_HOST=redis
      - QUEUE_CONNECTION=redis
    networks:
      - company-crawler

  # Node.js Crawling Service
  crawler:
    build:
      context: ./scripts
      dockerfile: Dockerfile.node
    container_name: company-crawler-scripts
    restart: unless-stopped
    volumes:
      - ./scripts:/app
      - /dev/shm:/dev/shm # Shared memory for Chrome
    environment:
      - NODE_ENV=production
      - PUPPETEER_EXECUTABLE_PATH=/usr/bin/google-chrome
    networks:
      - company-crawler
    # Chrome needs these capabilities
    cap_add:
      - SYS_ADMIN
    security_opt:
      - seccomp:unconfined

  # Python Processing Service
  python-processor:
    build:
      context: ./crawl-executives-scripts
      dockerfile: Dockerfile.python
    container_name: company-crawler-python
    restart: unless-stopped
    volumes:
      - ./crawl-executives-scripts:/app
    environment:
      - PYTHONPATH=/app
    networks:
      - company-crawler

# Docker Networks
networks:
  company-crawler:
    driver: bridge

# Persistent Volumes
volumes:
  dbdata:
    driver: local
  redisdata:
    driver: local 