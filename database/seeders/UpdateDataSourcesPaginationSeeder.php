<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class UpdateDataSourcesPaginationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Update existing data sources with default pagination format
        \App\Models\DataSource::whereNotNull('id')
            ->update([
                'pagination_format' => 'query_param'
            ]);
            
        $this->command->info('Updated existing data sources with default pagination format: query_param');
    }
}
