<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Department;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Đảm bảo các phòng ban đã được tạo trước
        $departments = Department::all();
        if ($departments->count() == 0) {
            $this->call(DepartmentSeeder::class);
            $departments = Department::all();
        }

        // Tạo tài khoản admin nếu chưa tồn tại
        if (!User::where('email', '<EMAIL>')->exists()) {
            $admin = User::create([
                'name' => 'Admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('B9R3A5o3bAXl'),
                'role' => 'admin',
            ]);
            // Admin không thuộc phòng ban nào mặc định
        }
    }
}
