<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Category;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Technology',
                'description' => 'Companies in the technology sector including software, hardware, and IT services',
                'color' => '#3B82F6',
                'is_active' => true,
            ],
            [
                'name' => 'Finance',
                'description' => 'Financial institutions including banks, insurance, and investment companies',
                'color' => '#10B981',
                'is_active' => true,
            ],
            [
                'name' => 'Healthcare',
                'description' => 'Healthcare providers, pharmaceutical companies, and medical device manufacturers',
                'color' => '#EF4444',
                'is_active' => true,
            ],
            [
                'name' => 'Manufacturing',
                'description' => 'Manufacturing companies across various industries',
                'color' => '#F59E0B',
                'is_active' => true,
            ],
            [
                'name' => 'E-commerce',
                'description' => 'Online retail and e-commerce platforms',
                'color' => '#8B5CF6',
                'is_active' => true,
            ],
            [
                'name' => 'Education',
                'description' => 'Educational institutions and e-learning platforms',
                'color' => '#06B6D4',
                'is_active' => true,
            ],
            [
                'name' => 'Energy',
                'description' => 'Energy companies including renewable and traditional energy sources',
                'color' => '#84CC16',
                'is_active' => true,
            ],
            [
                'name' => 'Real Estate',
                'description' => 'Real estate companies and property management firms',
                'color' => '#EC4899',
                'is_active' => true,
            ],
            [
                'name' => 'Consulting',
                'description' => 'Professional consulting services and advisory firms',
                'color' => '#6366F1',
                'is_active' => true,
            ],
            [
                'name' => 'Government',
                'description' => 'Government agencies and public sector organizations',
                'color' => '#64748B',
                'is_active' => true,
            ],
        ];

        foreach ($categories as $categoryData) {
            Category::firstOrCreate(
                ['name' => $categoryData['name']],
                $categoryData
            );
        }
    }
}
