<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Keyword;

class KeywordSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $keywords = [
            // Company Info Page Keywords
            ['type' => 'company_info_page', 'keyword' => 'company', 'position_level' => null, 'is_active' => true],
            ['type' => 'company_info_page', 'keyword' => 'company/outline', 'position_level' => null, 'is_active' => true],
            ['type' => 'company_info_page', 'keyword' => 'company/profile', 'position_level' => null, 'is_active' => true],
            ['type' => 'company_info_page', 'keyword' => 'company-info', 'position_level' => null, 'is_active' => true],
            ['type' => 'company_info_page', 'keyword' => 'company/index', 'position_level' => null, 'is_active' => true],
            ['type' => 'company_info_page', 'keyword' => 'about', 'position_level' => null, 'is_active' => true],
            ['type' => 'company_info_page', 'keyword' => 'about_us', 'position_level' => null, 'is_active' => true],
            ['type' => 'company_info_page', 'keyword' => 'aboutus', 'position_level' => null, 'is_active' => true],
            ['type' => 'company_info_page', 'keyword' => 'about-us', 'position_level' => null, 'is_active' => true],
            ['type' => 'company_info_page', 'keyword' => 'profile', 'position_level' => null, 'is_active' => true],
            ['type' => 'company_info_page', 'keyword' => 'company/company_overview', 'position_level' => null, 'is_active' => true],
            
            // Executive Keywords
            ['type' => 'executive', 'keyword' => '代表取締役', 'position_level' => 'c_level', 'is_active' => true],
            ['type' => 'executive', 'keyword' => '取締役社長', 'position_level' => 'c_level', 'is_active' => true],
            ['type' => 'executive', 'keyword' => '代表者', 'position_level' => 'c_level', 'is_active' => true],
            ['type' => 'executive', 'keyword' => '取締役', 'position_level' => 'c_level', 'is_active' => true],
            ['type' => 'executive', 'keyword' => '代表取締役社長', 'position_level' => 'c_level', 'is_active' => true],
            ['type' => 'executive', 'keyword' => '専務取締役', 'position_level' => 'c_level', 'is_active' => true],
            ['type' => 'executive', 'keyword' => '常務取締役', 'position_level' => 'c_level', 'is_active' => true],
            ['type' => 'executive', 'keyword' => '執行役員', 'position_level' => 'c_level', 'is_active' => true],
            ['type' => 'executive', 'keyword' => '最高経営責任者', 'position_level' => 'c_level', 'is_active' => true],
            ['type' => 'executive', 'keyword' => '最高執行責任者', 'position_level' => 'c_level', 'is_active' => true],
            ['type' => 'executive', 'keyword' => '最高財務責任者', 'position_level' => 'c_level', 'is_active' => true],
            ['type' => 'executive', 'keyword' => '最高技術責任者', 'position_level' => 'c_level', 'is_active' => true],
            ['type' => 'executive', 'keyword' => '最高情報責任者', 'position_level' => 'c_level', 'is_active' => true],
            ['type' => 'executive', 'keyword' => 'CEO', 'position_level' => 'c_level', 'is_active' => true],
            ['type' => 'executive', 'keyword' => 'CTO', 'position_level' => 'c_level', 'is_active' => true],
            ['type' => 'executive', 'keyword' => 'COO', 'position_level' => 'c_level', 'is_active' => true],
            ['type' => 'executive', 'keyword' => 'CIO', 'position_level' => 'c_level', 'is_active' => true],
        ];

        foreach ($keywords as $keyword) {
            Keyword::updateOrCreate(
                [
                    'keyword' => $keyword['keyword'],
                    'type' => $keyword['type'],
                ],
                $keyword
            );
        }

        $this->command->info('Keywords seeded successfully!');
    }
}
