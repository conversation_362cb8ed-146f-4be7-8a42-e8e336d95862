<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Department;

class DepartmentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $departments = [
            [
                'name' => 'HR',
                'code' => 'HR-DEPT',
                'description' => 'Phòng Nhân sự',
                'manager_name' => 'Trưởng phòng HR',
                'manager_id' => null, // Sẽ được cập nhật sau
                'is_active' => true
            ],
            [
                'name' => 'IT',
                'code' => 'IT-DEPT',
                'description' => 'Phòng Công nghệ thông tin',
                'manager_name' => 'Trưởng phòng IT',
                'manager_id' => null,
                'is_active' => true
            ],
            [
                'name' => 'Marketing',
                'code' => 'MKT-DEPT',
                'description' => 'Phòng Marketing',
                'manager_name' => 'Trưởng phòng Marketing',
                'manager_id' => null,
                'is_active' => true
            ],
            [
                'name' => 'Finance',
                'code' => 'FIN-DEPT',
                'description' => 'Phòng Tài chính',
                'manager_name' => 'Trưởng phòng Tài chính',
                'manager_id' => null,
                'is_active' => true
            ],
            [
                'name' => 'Operations',
                'code' => 'OPS-DEPT',
                'description' => 'Phòng Vận hành',
                'manager_name' => 'Trưởng phòng Vận hành',
                'manager_id' => null,
                'is_active' => true
            ],
            [
                'name' => 'Sales',
                'code' => 'SALES-DEPT',
                'description' => 'Phòng Bán hàng',
                'manager_name' => 'Trưởng phòng Bán hàng',
                'manager_id' => null,
                'is_active' => true
            ],
            [
                'name' => 'Research',
                'code' => 'RND-DEPT',
                'description' => 'Phòng Nghiên cứu & Phát triển',
                'manager_name' => 'Trưởng phòng R&D',
                'manager_id' => null,
                'is_active' => true
            ]
        ];

        foreach ($departments as $departmentData) {
            // Kiểm tra xem phòng ban với mã code đã tồn tại chưa
            if (!Department::where('code', $departmentData['code'])->exists()) {
                Department::create($departmentData);
            }
        }
    }
}
