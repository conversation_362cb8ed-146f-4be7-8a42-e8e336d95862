<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Kiểm tra nếu cột keycloak_id chưa tồn tại thì thêm vào
            if (!Schema::hasColumn('users', 'keycloak_id')) {
                $table->string('keycloak_id')->nullable()->after('id');
            }
            
            // Kiểm tra nếu cột is_keycloak_user chưa tồn tại thì thêm vào
            if (!Schema::hasColumn('users', 'is_keycloak_user')) {
                $table->boolean('is_keycloak_user')->default(false)->after('department_id');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // <PERSON><PERSON>a các cột liên quan đến Keycloak nếu tồn tại
            if (Schema::hasColumn('users', 'keycloak_id')) {
                $table->dropColumn('keycloak_id');
            }
            
            if (Schema::hasColumn('users', 'is_keycloak_user')) {
                $table->dropColumn('is_keycloak_user');
            }
        });
    }
};
