<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('companies', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('website')->nullable();
            $table->string('source_url');
            $table->string('info_page_url')->nullable();
            $table->string('industry')->nullable();
            $table->text('description')->nullable();
            $table->text('address')->nullable();
            $table->string('phone')->nullable();
            $table->string('email')->nullable();
            $table->string('employee_count')->nullable();
            $table->integer('founded_year')->nullable();
            $table->enum('status', ['pending', 'processing', 'completed', 'failed'])->default('pending');
            $table->integer('crawl_attempts')->default(0);
            $table->timestamp('last_crawled_at')->nullable();
            $table->json('executives')->nullable();
            $table->json('crawl_metadata')->nullable();
            $table->foreignId('data_source_id')->constrained()->onDelete('cascade');
            $table->timestamps();
            
            $table->index(['status', 'last_crawled_at']);
            $table->index('data_source_id');
            $table->index('name');
            $table->index('website');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('companies');
    }
};
