<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('keywords', function (Blueprint $table) {
            // Drop index first for SQLite compatibility
            $table->dropIndex('keywords_type_is_active_priority_index');
        });
        
        Schema::table('keywords', function (Blueprint $table) {
            $table->dropColumn('priority');
        });
        
        // Recreate index without priority column
        Schema::table('keywords', function (Blueprint $table) {
            $table->index(['type', 'is_active'], 'keywords_type_is_active_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('keywords', function (Blueprint $table) {
            // Drop the new index
            $table->dropIndex('keywords_type_is_active_index');
        });
        
        Schema::table('keywords', function (Blueprint $table) {
            $table->tinyInteger('priority')->default(5)->after('position_level');
        });
        
        // Recreate original index with priority
        Schema::table('keywords', function (Blueprint $table) {
            $table->index(['type', 'is_active', 'priority'], 'keywords_type_is_active_priority_index');
        });
    }
};
