<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('blacklists', function (Blueprint $table) {
            // Add last_update column
            $table->timestamp('last_update')->nullable()->after('notes');
            
            // Rename notes to note
            $table->renameColumn('notes', 'note');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('blacklists', function (Blueprint $table) {
            // Rename note back to notes
            $table->renameColumn('note', 'notes');
            
            // Drop last_update column
            $table->dropColumn('last_update');
        });
    }
};
