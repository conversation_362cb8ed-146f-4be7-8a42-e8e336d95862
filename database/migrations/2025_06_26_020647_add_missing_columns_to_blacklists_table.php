<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('blacklists', function (Blueprint $table) {
            // Check and add missing columns
            if (!Schema::hasColumn('blacklists', 'code')) {
                $table->string('code')->nullable()->after('id');
            }
            
            if (!Schema::hasColumn('blacklists', 'name')) {
                $table->string('name')->nullable()->after('code');
            }
            
            if (!Schema::hasColumn('blacklists', 'email')) {
                $table->string('email')->nullable()->after('name');
            }
            
            if (!Schema::hasColumn('blacklists', 'associated_company')) {
                $table->string('associated_company')->nullable()->after('email');
            }
            
            if (!Schema::hasColumn('blacklists', 'website')) {
                $table->string('website')->nullable()->after('associated_company');
            }
        });
        
        // Add indexes after all columns are created
        Schema::table('blacklists', function (Blueprint $table) {
            // Check and add indexes for commonly queried columns
            try {
                $table->index('code');
            } catch (\Exception $e) {
                // Index might already exist
            }
            
            try {
                $table->index('email');
            } catch (\Exception $e) {
                // Index might already exist
            }
            
            try {
                $table->index('associated_company');
            } catch (\Exception $e) {
                // Index might already exist
            }
            
            try {
                $table->index('website');
            } catch (\Exception $e) {
                // Index might already exist
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('blacklists', function (Blueprint $table) {
            // Drop indexes first (if they exist)
            try {
                $table->dropIndex(['code']);
            } catch (\Exception $e) {
                // Index might not exist
            }
            
            try {
                $table->dropIndex(['email']);
            } catch (\Exception $e) {
                // Index might not exist
            }
            
            try {
                $table->dropIndex(['associated_company']);
            } catch (\Exception $e) {
                // Index might not exist
            }
            
            try {
                $table->dropIndex(['website']);
            } catch (\Exception $e) {
                // Index might not exist
            }
            
            // Drop columns (if they exist)
            $columnsToCheck = ['code', 'name', 'email', 'associated_company', 'website'];
            foreach ($columnsToCheck as $column) {
                if (Schema::hasColumn('blacklists', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
};
