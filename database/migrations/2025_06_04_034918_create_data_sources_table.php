<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('data_sources', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('source_url');
            $table->boolean('has_pagination')->default(false);
            $table->boolean('has_company_links')->default(false);
            $table->string('company_name_selector');
            $table->string('company_link_selector')->nullable();
            $table->string('pagination_selector')->nullable();
            $table->integer('max_pages')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamp('last_crawled_at')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();
            
            $table->index(['is_active', 'last_crawled_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('data_sources');
    }
};
