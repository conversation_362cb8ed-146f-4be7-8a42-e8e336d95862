<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('data_sources', function (Blueprint $table) {
            $table->boolean('enable_javascript')->default(false)->after('is_active');
            $table->integer('javascript_wait_time')->default(3)->after('enable_javascript');
            $table->json('javascript_config')->nullable()->after('javascript_wait_time');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('data_sources', function (Blueprint $table) {
            $table->dropColumn(['enable_javascript', 'javascript_wait_time', 'javascript_config']);
        });
    }
};
