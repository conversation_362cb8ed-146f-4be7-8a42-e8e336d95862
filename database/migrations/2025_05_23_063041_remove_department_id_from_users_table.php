<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Lưu dữ liệu hiện có vào bảng trung gian trước khi xóa department_id
            $this->migrateExistingData();
            
            // Xóa trường department_id
            $table->dropForeign(['department_id']);
            $table->dropColumn('department_id');
        });
    }

    /**
     * Lưu dữ liệu hiện có từ quan hệ 1-1 sang quan hệ nhiều-nhiều
     */
    private function migrateExistingData(): void
    {
        // Use Laravel's Carbon helper for cross-database compatibility
        $now = now();
        
        \DB::statement('
            INSERT INTO department_user (user_id, department_id, created_at, updated_at)
            SELECT id, department_id, ?, ? 
            FROM users 
            WHERE department_id IS NOT NULL
        ', [$now, $now]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->foreignId('department_id')->nullable()->constrained();
        });
    }
};
