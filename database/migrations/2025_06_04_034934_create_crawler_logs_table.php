<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('crawler_logs', function (Blueprint $table) {
            $table->id();
            $table->string('type'); // 'company_list', 'company_details', 'info_page_search'
            $table->string('status'); // 'started', 'completed', 'failed'
            $table->string('target_url');
            $table->morphs('crawlable'); // polymorphic relationship
            $table->json('metadata')->nullable();
            $table->text('error_message')->nullable();
            $table->integer('duration_seconds')->nullable();
            $table->timestamp('started_at');
            $table->timestamp('completed_at')->nullable();
            $table->timestamps();
            
            $table->index(['type', 'status']);
            $table->index('started_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('crawler_logs');
    }
};
