<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('campaigns', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->foreignId('data_source_id')->constrained()->onDelete('cascade');
            $table->enum('type', ['full', 'pagination_range'])->default('full');
            $table->integer('from_page')->nullable();
            $table->integer('to_page')->nullable();
            $table->enum('status', ['pending', 'running', 'completed', 'failed', 'cancelled'])->default('pending');
            $table->integer('total_pages')->nullable();
            $table->integer('completed_pages')->default(0);
            $table->integer('companies_found')->default(0);
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->json('crawl_metadata')->nullable();
            $table->text('error_message')->nullable();
            $table->timestamps();
            
            $table->index(['data_source_id', 'status']);
            $table->index(['status', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('campaigns');
    }
};
