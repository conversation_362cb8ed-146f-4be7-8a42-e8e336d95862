<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('data_sources', function (Blueprint $table) {
            $table->string('pagination_format')->default('query_param')->after('pagination_selector');
            $table->string('pagination_base_url')->nullable()->after('pagination_format');
            
            // Remove JavaScript configuration columns if they exist
            if (Schema::hasColumn('data_sources', 'javascript_wait_time')) {
                $table->dropColumn('javascript_wait_time');
            }
            if (Schema::hasColumn('data_sources', 'javascript_config')) {
                $table->dropColumn('javascript_config');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('data_sources', function (Blueprint $table) {
            $table->dropColumn(['pagination_format', 'pagination_base_url']);
            
            // Add back JavaScript configuration columns
            $table->integer('javascript_wait_time')->default(3)->after('is_active');
            $table->json('javascript_config')->nullable()->after('javascript_wait_time');
        });
    }
};
