<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\DataSource>
 */
class DataSourceFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->company() . ' Directory',
            'source_url' => $this->faker->url(),
            'has_pagination' => $this->faker->boolean(),
            'has_company_links' => $this->faker->boolean(),
            'company_name_selector' => '.company-name',
            'company_link_selector' => '.company-link a',

            'pagination_format' => $this->faker->randomElement(['query_param', 'path_segment', 'query_p']),
            'pagination_base_url' => null,
            'max_pages' => $this->faker->optional()->numberBetween(1, 20),
            'is_active' => true,
            'last_crawled_at' => $this->faker->optional()->dateTimeThisMonth(),
            'notes' => $this->faker->optional()->sentence(),
        ];
    }
}
