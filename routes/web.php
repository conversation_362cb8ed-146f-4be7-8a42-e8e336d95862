<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\KeycloakController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Chuyển hướng từ trang chủ về trang admin
Route::redirect('/', '/admin');

// Đường dẫn cho đăng nhập Keycloak
Route::get('/auth/keycloak', [KeycloakController::class, 'redirectToKeycloak'])->name('login.keycloak');
Route::get('/auth/keycloak/callback', [KeycloakController::class, 'handleKeycloakCallback']);

