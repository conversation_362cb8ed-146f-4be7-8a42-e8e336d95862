# Project Brief - Company Crawler System

## Tổng quan dự án
**Company Crawler** là hệ thống thu thập thông tin công ty tự động được xây dựng bằng Laravel với Filament admin interface. Hệ thống được thiết kế để tự động crawl và thu thập thông tin chi tiết về các công ty từ nhiều nguồn dữ liệu trên internet.

## Mục tiêu chính
1. **Thu thập thông tin công ty**: Tự động crawl danh sách công ty từ các website/directory
2. **Trích xuất thông tin chi tiết**: Thu thập thông tin cơ bản (tên, website, địa chỉ, ngành nghề, số nhân viên)
3. **Phát hiện ban lãnh đạo**: Tự động nhận diện và trích xuất thông tin executives/leadership
4. **Tìm kiếm thông tin bổ sung**: Sử dụng Google Search để tìm website và thông tin thiếu
5. **Quản lý quy mô lớn**: Xử lý hàng nghìn công ty với background jobs

## Phạm vi dự án

### Trong phạm vi
- Web crawling với JavaScript rendering support
- Multi-source data collection
- Executive information extraction
- Google Search integration
- Anti-detection mechanisms
- Background job processing
- Admin interface với Filament
- Progress tracking và notifications

### Ngoài phạm vi
- Real-time data updates
- API endpoints cho external systems
- Advanced analytics/reporting
- Email marketing integration
- CRM integration

## Đối tượng sử dụng
- **Sales teams**: Lead generation và contact discovery
- **Marketing teams**: Market research và competitor analysis
- **Business analysts**: Company database building
- **Data researchers**: Business intelligence gathering

## Yêu cầu kỹ thuật chính
- **Platform**: Laravel 12.x
- **Admin UI**: Filament 3.x
- **Web Crawling**: Guzzle + Symfony DomCrawler + Panther
- **Browser Automation**: ChromeDriver
- **Queue System**: Laravel Queue
- **Database**: MySQL/PostgreSQL
- **Caching**: Redis (optional)

## Tiêu chí thành công
1. Tự động crawl được 1000+ công ty mỗi ngày
2. Độ chính xác thông tin executives > 80%
3. Tỷ lệ thành công crawl > 85%
4. Thời gian xử lý < 30 giây/công ty
5. Zero manual intervention cho 90% cases

## Rủi ro và giới hạn
- **Bot detection**: Websites có thể block crawling bots
- **Rate limiting**: Cần throttling để tránh IP ban
- **Data quality**: Thông tin có thể không chính xác hoặc lỗi thời
- **Legal compliance**: Cần tuân thủ robots.txt và terms of service
- **Resource intensive**: Browser automation tiêu tốn CPU/memory cao 