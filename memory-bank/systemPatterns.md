# System Patterns - Company Crawler Architecture

## Kiến trúc tổng quan

### Layer Architecture
```
┌─────────────────────────────────────┐
│        Presentation Layer           │
│     (Filament Admin Interface)      │
├─────────────────────────────────────┤
│          Application Layer          │
│    (Controllers, Jobs, Commands)    │
├─────────────────────────────────────┤
│           Service Layer             │
│  (WebCrawler, GoogleSearch, etc.)   │
├─────────────────────────────────────┤
│            Domain Layer             │
│      (Models, Repositories)         │
├─────────────────────────────────────┤
│       Infrastructure Layer          │
│   (Database, Queue, Cache, HTTP)    │
└─────────────────────────────────────┘
```

## Core Design Patterns

### 1. Service Pattern
**Location**: `app/Services/`

**Purpose**: Encapsulate business logic và external integrations

**Key Services**:
- `WebCrawlerService`: Core web crawling functionality
- `EnhancedWebCrawlerService`: Advanced crawling với JavaScript support
- `ExecutiveEmailService`: Email prediction algorithms

**JavaScript Services**:
- `google-search.js`: Google search integration using browser automation

**Pattern Implementation**:
```php
class WebCrawlerService
{
    private Client $httpClient;
    private ?PantherClient $pantherClient = null;
    
    public function crawlCompanyList(DataSource $dataSource): array
    public function crawlCompanyDetails(Company $company): array
    protected function extractExecutives(Crawler $crawler): array
}
```

### 2. Job Pattern (Queue)
**Location**: `app/Jobs/`

**Purpose**: Background processing cho heavy operations

**Key Jobs**:
- `CrawlCompanyListJob`: Crawl danh sách công ty từ data sources
- `CrawlCompanyExecutivesJob`: Extract executive information
- `BatchGoogleSearchJob`: Batch Google searches

**Pattern Implementation**:
```php
class CrawlCompanyListJob implements ShouldQueue
{
    use Queueable, InteractsWithQueue, SerializesModels;
    
    public function handle(EnhancedWebCrawlerService $crawlerService): void
    public function failed(\Throwable $exception): void
}
```

### 3. Strategy Pattern
**Location**: Crawling strategies trong services

**Purpose**: Different approaches cho data extraction

**Strategies**:
- **HTTP Client**: Simple requests cho static content
- **Browser Automation**: JavaScript rendering cho SPAs
- **Hybrid Approach**: Fallback mechanism

**Implementation**:
```php
// Smart detection và fallback
private function getPageContent(string $url): ?string
{
    $content = $this->getPageContentSmart($url);
    if ($content) return $content;
    
    return $this->getPageContentWithWebDriver($url);
}
```

### 4. Repository Pattern (Implicit)
**Location**: Eloquent Models với scopes

**Purpose**: Data access abstraction

**Implementation**:
```php
class Company extends Model
{
    public function scopePending($query)
    public function scopeCompleted($query)
    public function scopeReadyForDetailCrawl($query)
}
```

## Component Architecture

### 1. Data Models
```
DataSource (1) ──→ (many) Company
Company (1) ──→ (many) CrawlerLog
Keyword ──→ Used by Services
Blacklist ──→ Filtering logic
```

### 2. Crawling Pipeline
```
DataSource → CrawlCompanyListJob → WebCrawlerService → Companies
     ↓
Company → CrawlCompanyExecutivesJob → EnhancedWebCrawlerService → Executives
```

### 3. Browser Automation Stack
```
PantherClient → ChromeDriver → Real Browser
     ↓
Stealth Scripts → Anti-Detection
     ↓
DOM Manipulation → Data Extraction
```

## Key Technical Patterns

### 1. Anti-Detection Pattern
**Location**: `google-search.js`, `WebCrawlerService`

**Techniques**:
- User Agent rotation
- Request timing randomization
- Tab management và reuse
- Stealth script injection
- Human behavior simulation

```php
private function addAntiDetectionDelay(): void
{
    // Intelligent delays based on request count
    // Exponential backoff for heavy usage
}

private function simulateHumanBehavior(Client $client): void
{
    // Random mouse movements, scrolling
}
```

### 2. Resilience Pattern
**Retry mechanisms với exponential backoff**:
```php
public $tries = 2;
public $backoff = [60, 120];
public $timeout = 600;
```

**Circuit breaker cho external services**:
- Connection error detection
- Automatic fallback strategies
- Recovery mechanisms

### 3. Data Extraction Pattern
**Multi-stage extraction**:
1. **Structured HTML**: CSS selectors cho organized content
2. **Text-based**: NLP techniques cho unstructured content
3. **Keyword matching**: Pattern recognition cho executives

```php
private function extractExecutivesDirectly(Crawler $crawler): array
{
    // 1. Try structured HTML first
    $executives = $this->extractFromStructuredHtml($crawler, $keywords);
    
    // 2. Fallback to text extraction
    if (count($executives) < 3) {
        $textExecutives = $this->extractExecutivesFromText($fullText, $keywords);
        $executives = array_merge($executives, $textExecutives);
    }
    
    return $this->removeDuplicateExecutives($executives);
}
```

### 4. Progress Tracking Pattern
**Real-time updates với caching**:
```php
private function updateProgress(string $cacheKey, float $percent, string $message): void
{
    Cache::put($cacheKey, [
        'percent' => $percent,
        'message' => $message,
        'updated_at' => now(),
    ], 3600);
}
```

## Data Flow Architecture

### 1. Company List Crawling Flow
```
DataSource → URL Builder → HTTP/Browser Request → HTML Parser → Company Extractor → Database
                                    ↓
                            Google Search (if needed) → Website Discovery
```

### 2. Executive Extraction Flow
```
Company → Info Page Discovery → Content Fetching → Multiple Extractors → Data Cleaning → Email Prediction → Database
```

### 3. Error Handling Flow
```
Exception → Error Classifier → Retry Strategy → Fallback Method → Logging → Notification
```

## Performance Patterns

### 1. Resource Management
- **Connection pooling**: Reuse HTTP connections
- **Browser tab management**: Reuse browser instances
- **Memory cleanup**: Proper resource disposal

### 2. Caching Strategy
- **Google Search results**: 24-hour cache
- **Page content**: Session-based cache
- **Progress tracking**: Real-time cache updates

### 3. Queue Optimization
- **Job batching**: Process multiple items together
- **Priority queues**: Critical jobs first
- **Timeout protection**: Prevent infinite jobs

## Security Patterns

### 1. Input Validation
- URL sanitization
- CSS selector validation
- XSS prevention trong admin interface

### 2. Rate Limiting
- Request throttling
- IP rotation (future)
- Respectful crawling practices

### 3. Data Protection
- Sensitive data encryption
- Audit logging
- Access control với Filament 