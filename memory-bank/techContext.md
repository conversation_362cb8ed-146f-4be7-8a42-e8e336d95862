# Technical Context - Company Crawler

## Technology Stack

### Backend Framework
- **<PERSON><PERSON> 12.x**: Main PHP framework
- **PHP 8.2+**: Required PHP version
- **Composer**: Dependency management

### Admin Interface
- **Filament 3.2**: Modern admin panel
  - `filament/filament`: Core admin functionality
  - `bezhansalleh/filament-language-switch`: Multi-language support
  - `ariaieboy/filament-currency`: Currency formatting

### Web Crawling Stack
- **Guzzle HTTP 7.9**: HTTP client cho basic requests
- **Symfony DomCrawler 7.3**: HTML parsing và manipulation
- **Symfony CSS Selector 7.3**: CSS selector engine
- **Symfony Panther 2.2**: Browser automation
- **ChromeDriver**: Headless browser cho JavaScript rendering

### Database & Storage
- **MySQL/PostgreSQL**: Primary database
- **Laravel Migrations**: Database schema management
- **Eloquent ORM**: Database interactions

### Queue & Background Processing
- **<PERSON>vel Queue**: Background job processing
- **Redis** (recommended): Queue driver và caching
- **Database Queue** (fallback): Queue tables trong database

### Authentication & Authorization
- **Laravel Socialite 5.20**: Social authentication
- **Keycloak Provider**: Enterprise SSO integration
  - `socialiteproviders/keycloak`: Keycloak integration

### Development Tools
- **Laravel Sail 1.41**: Docker development environment
- **Laravel Pint 1.13**: Code style fixing
- **Laravel Pail 1.2**: Real-time log monitoring
- **PHPUnit 11.5**: Testing framework
- **Faker**: Test data generation
- **Mockery**: Mocking framework

### Browser Automation
- **BDI (dbrekelmans/bdi) 1.4**: Browser driver installer
- **ChromeDriver**: Browser automation driver
- **Chrome/Chromium**: Target browser

## Development Environment Setup

### System Requirements
```bash
- PHP 8.2+
- Composer 2.x
- Node.js 18+ & NPM
- MySQL 8.0+ or PostgreSQL 13+
- Redis 6.0+ (recommended)
- Chrome/Chromium browser
```

### Installation Steps
```bash
# Clone repository
git clone <repository-url>
cd company-crawler

# Install PHP dependencies
composer install

# Install Node dependencies
npm install

# Environment setup
cp .env.example .env
php artisan key:generate

# Database setup
php artisan migrate
php artisan db:seed

# Install ChromeDriver
./vendor/bin/bdi detect drivers
```

### Development Commands
```bash
# Start development environment
composer run dev

# Individual components
php artisan serve          # Laravel server
php artisan queue:listen    # Queue worker
php artisan pail           # Log monitoring
npm run dev                # Vite asset compilation

# Testing
composer run test
php artisan test
```

## Key Dependencies Analysis

### Core Crawling Dependencies
```json
{
    "guzzlehttp/guzzle": "^7.9",           // HTTP client
    "symfony/css-selector": "^7.3",        // CSS selectors
    "symfony/dom-crawler": "^7.3",         // HTML parsing
    "symfony/panther": "^2.2"              // Browser automation
}
```

### Admin Interface Dependencies
```json
{
    "filament/filament": "^3.2",                        // Admin panel
    "bezhansalleh/filament-language-switch": "^3.1",   // Multi-language
    "ariaieboy/filament-currency": "^1.13"              // Currency support
}
```

### Authentication Dependencies
```json
{
    "laravel/socialite": "^5.20",           // Social auth
    "socialiteproviders/keycloak": "^5.3"   // Keycloak integration
}
```

## Configuration Files

### Environment Variables
```env
# Database
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=company_crawler
DB_USERNAME=root
DB_PASSWORD=

# Queue
QUEUE_CONNECTION=redis
REDIS_HOST=127.0.0.1
REDIS_PORT=6379

# Browser Automation
PANTHER_CHROME_DRIVER_BINARY=/path/to/chromedriver
PANTHER_NO_SANDBOX=1

# Crawling Settings
CRAWL_DELAY=1
CRAWL_TIMEOUT=30
MAX_RETRY_ATTEMPTS=3
```

### Important Config Files
- `config/queue.php`: Queue configuration
- `config/cache.php`: Caching setup
- `config/services.php`: External services
- `config/app.php`: Application settings

## Browser Automation Setup

### ChromeDriver Configuration
```php
// Chrome options used in services
$chromeOptions = [
    '--no-sandbox',
    '--disable-dev-shm-usage',
    '--disable-gpu',
    '--headless',
    '--window-size=1920,1080',
    '--disable-web-security',
    '--disable-features=VizDisplayCompositor',
    '--user-agent=Mozilla/5.0...'
];
```

### Anti-Detection Features
- Rotating User Agents
- Random window sizes
- Human behavior simulation
- Request timing randomization
- Stealth script injection

## Performance Configuration

### Queue Workers
```bash
# Supervisor configuration cho production
[program:company-crawler-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/artisan queue:work redis --sleep=3 --tries=3 --max-time=3600
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=4
redirect_stderr=true
stdout_logfile=/var/log/company-crawler-worker.log
```

### Memory & Resource Limits
```php
// Job configuration
public int $timeout = 600;      // 10 minutes per job
public int $tries = 2;          // 2 retry attempts
public int $maxExceptions = 2;  // Max exceptions before failing
public array $backoff = [60, 120]; // Backoff delays
```

## Monitoring & Logging

### Logging Configuration
- **Laravel Log**: Application logs
- **CrawlerLog Model**: Crawl-specific logging
- **Queue Failed Jobs**: Failed job tracking
- **Browser Logs**: ChromeDriver logs

### Performance Monitoring
- **Queue Metrics**: Job success/failure rates
- **Crawl Statistics**: Companies processed per hour
- **Resource Usage**: Memory và CPU monitoring
- **Error Tracking**: Exception monitoring

## Security Considerations

### Data Protection
- Input sanitization
- SQL injection prevention
- XSS protection trong admin
- Rate limiting

### Crawling Ethics
- Respect robots.txt
- Implement delays
- User-Agent identification
- Terms of service compliance

## Deployment Architecture

### Production Requirements
```
- PHP-FPM 8.2+
- Nginx/Apache
- MySQL/PostgreSQL
- Redis
- Supervisor (queue workers)
- Chrome/Chromium (headless)
```

### Recommended Server Specs
```
- CPU: 4+ cores
- RAM: 8GB+ (browser automation is memory intensive)
- Storage: 50GB+ SSD
- Network: Stable internet connection
```

## External Integrations

### Current Integrations
- **Google Search**: Website discovery
- **ChromeDriver**: Browser automation
- **Filament Admin**: Management interface

### Future Integration Points
- **CRM APIs**: Salesforce, HubSpot
- **Email Services**: SMTP, API-based
- **Proxy Services**: IP rotation
- **Monitoring Tools**: Sentry, New Relic 