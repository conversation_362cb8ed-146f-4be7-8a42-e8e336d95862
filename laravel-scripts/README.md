# Laravel Project Scripts

Bộ script shell tự động hóa các tác vụ phổ biến cho dự án <PERSON>. Chỉ cần clone về và sử dụng ngay!

## Cài đặt

1. Copy thư mục `laravel-scripts` vào root của dự án <PERSON>vel
2. Cấp quyền thực thi cho các script:
```bash
chmod +x laravel-scripts/*.sh
```

## Các Script có sẵn

### 🚀 Setup & Installation
- `setup.sh` - Thiết lập dự án từ đầu (clone mới)
- `install.sh` - Cài đặt dependencies và cấu hình cơ bản
- `fresh-install.sh` - Cài đặt hoàn toàn mới (xóa vendor, node_modules)

### 🗄️ Database
- `db-setup.sh` - Thiết lập database (migrate, seed)
- `db-fresh.sh` - Fresh migrate với seed
- `db-backup.sh` - Backup database
- `db-restore.sh` - Restore database từ backup

### 🔧 Development
- `dev.sh` - Chạy development server (Laravel + Vite + Queue)
- `build.sh` - Build assets cho production
- `test.sh` - Chạy test suite
- `lint.sh` - Chạy code linting (Pint)

### 🚢 Deployment
- `deploy-staging.sh` - Deploy lên staging
- `deploy-production.sh` - Deploy lên production
- `optimize.sh` - Optimize cho production

### 🧹 Maintenance
- `clear-cache.sh` - Clear tất cả cache
- `logs.sh` - Xem logs realtime
- `queue-work.sh` - Chạy queue worker
- `maintenance.sh` - Bật/tắt maintenance mode

## Sử dụng

```bash
# Thiết lập dự án mới
./laravel-scripts/setup.sh

# Chạy development
./laravel-scripts/dev.sh

# Chạy test
./laravel-scripts/test.sh

# Deploy production
./laravel-scripts/deploy-production.sh
```

## Cấu hình

Các script sẽ đọc cấu hình từ file `.env` và có thể override bằng các biến môi trường:

- `APP_ENV` - Environment (local, staging, production)
- `DB_CONNECTION` - Database connection
- `QUEUE_CONNECTION` - Queue connection
- `NODE_PACKAGE_MANAGER` - npm/yarn/pnpm (mặc định: npm)

## Tính năng

- ✅ Tự động detect package manager (npm/yarn/pnpm)
- ✅ Đọc cấu hình từ .env
- ✅ Logging và error handling
- ✅ Backup tự động trước khi deploy
- ✅ Health check sau deploy
- ✅ Cross-platform support (Linux/macOS)
- ✅ Colored output
