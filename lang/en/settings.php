<?php

return [
    'navigation_label' => 'System Settings',
    'model_label' => 'Setting',
    'plural_model_label' => 'System Settings',
    'navigation_group' => 'System',
    
    'form' => [
        'section_title' => 'Setting Information',
        'key' => [
            'label' => 'Setting Key',
            'helper_text' => 'Only use letters, numbers, underscores and hyphens',
        ],
        'type' => [
            'label' => 'Data Type',
        ],
        'value' => [
            'label' => 'Value',
            'helper_text' => [
                'number' => 'Enter a number (e.g. 30, 25.5)',
                'boolean' => 'Enter true or false',
                'json' => 'Enter valid JSON string',
                'string' => 'Enter text string',
            ],
        ],
        'description' => [
            'label' => 'Description',
            'helper_text' => 'Brief description of what this setting does',
        ],
    ],
    
    'table' => [
        'key' => 'Setting Key',
        'value' => 'Value',
        'type' => 'Type',
        'description' => 'Description',
        'updated_at' => 'Last Updated',
        'boolean_yes' => 'Yes',
        'boolean_no' => 'No',
    ],
    
    'filters' => [
        'type' => 'Data Type',
    ],
    
    'types' => [
        'string' => 'String',
        'number' => 'Number',
        'boolean' => 'Boolean',
        'json' => 'JSON',
    ],
    
    'pages' => [
        'list' => [
            'title' => 'Settings List',
        ],
        'create' => [
            'title' => 'Create New Setting',
        ],
        'edit' => [
            'title' => 'Edit Setting',
        ],
    ],
];
