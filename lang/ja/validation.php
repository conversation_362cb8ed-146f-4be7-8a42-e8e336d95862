<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines contain the default error messages used by
    | the validator class. Some of these rules have multiple versions such
    | as the size rules. Feel free to tweak each of these messages here.
    |
    */

    'accepted' => ':attribute フィールドを承認する必要があります。',
    'accepted_if' => ':other が :value の場合、:attribute フィールドを承認する必要があります。',
    'active_url' => ':attribute フィールドは有効なURLでなければなりません。',
    'after' => ':attribute フィールドは :date より後の日付でなければなりません。',
    'after_or_equal' => ':attribute フィールドは :date と同じかそれより後の日付でなければなりません。',
    'alpha' => ':attribute フィールドは文字のみを含む必要があります。',
    'alpha_dash' => ':attribute フィールドは文字、数字、ダッシュ、アンダースコアのみを含む必要があります。',
    'alpha_num' => ':attribute フィールドは文字と数字のみを含む必要があります。',
    'array' => ':attribute フィールドは配列でなければなりません。',
    'before' => ':attribute フィールドは :date より前の日付でなければなりません。',
    'before_or_equal' => ':attribute フィールドは :date と同じかそれより前の日付でなければなりません。',
    'between' => [
        'numeric' => ':attribute フィールドは :min から :max の間でなければなりません。',
        'file' => ':attribute フィールドは :min から :max キロバイトの間でなければなりません。',
        'string' => ':attribute フィールドは :min から :max 文字の間でなければなりません。',
        'array' => ':attribute フィールドは :min から :max 個のアイテムを含む必要があります。',
    ],
    'boolean' => ':attribute フィールドは true または false でなければなりません。',
    'confirmed' => ':attribute フィールドの確認が一致しません。',
    'current_password' => 'パスワードが正しくありません。',
    'date' => ':attribute フィールドは有効な日付でなければなりません。',
    'date_equals' => ':attribute フィールドは :date と同じ日付でなければなりません。',
    'date_format' => ':attribute フィールドは :format 形式と一致する必要があります。',
    'declined' => ':attribute フィールドを拒否する必要があります。',
    'declined_if' => ':other が :value の場合、:attribute フィールドを拒否する必要があります。',
    'different' => ':attribute フィールドと :other は異なる必要があります。',
    'digits' => ':attribute フィールドは :digits 桁でなければなりません。',
    'digits_between' => ':attribute フィールドは :min から :max 桁の間でなければなりません。',
    'dimensions' => ':attribute フィールドの画像サイズが無効です。',
    'distinct' => ':attribute フィールドに重複した値があります。',
    'email' => ':attribute フィールドは有効なメールアドレスでなければなりません。',
    'ends_with' => ':attribute フィールドは次のいずれかで終わる必要があります: :values。',
    'file' => ':attribute フィールドはファイルでなければなりません。',
    'filled' => ':attribute フィールドは値を持つ必要があります。',
    'gt' => [
        'numeric' => ':attribute フィールドは :value より大きい必要があります。',
        'file' => ':attribute フィールドは :value キロバイトより大きい必要があります。',
        'string' => ':attribute フィールドは :value 文字より多い必要があります。',
        'array' => ':attribute フィールドは :value 個より多いアイテムを含む必要があります。',
    ],
    'gte' => [
        'numeric' => ':attribute フィールドは :value 以上である必要があります。',
        'file' => ':attribute フィールドは :value キロバイト以上である必要があります。',
        'string' => ':attribute フィールドは :value 文字以上である必要があります。',
        'array' => ':attribute フィールドは :value 個以上のアイテムを含む必要があります。',
    ],
    'image' => ':attribute フィールドは画像でなければなりません。',
    'in' => '選択された :attribute は無効です。',
    'in_array' => ':attribute フィールドは :other に存在しません。',
    'integer' => ':attribute フィールドは整数でなければなりません。',
    'ip' => ':attribute フィールドは有効なIPアドレスでなければなりません。',
    'ipv4' => ':attribute フィールドは有効なIPv4アドレスでなければなりません。',
    'ipv6' => ':attribute フィールドは有効なIPv6アドレスでなければなりません。',
    'json' => ':attribute フィールドは有効なJSON文字列でなければなりません。',
    'lt' => [
        'numeric' => ':attribute フィールドは :value より小さい必要があります。',
        'file' => ':attribute フィールドは :value キロバイトより小さい必要があります。',
        'string' => ':attribute フィールドは :value 文字より少ない必要があります。',
        'array' => ':attribute フィールドは :value 個より少ないアイテムを含む必要があります。',
    ],
    'lte' => [
        'numeric' => ':attribute フィールドは :value 以下である必要があります。',
        'file' => ':attribute フィールドは :value キロバイト以下である必要があります。',
        'string' => ':attribute フィールドは :value 文字以下である必要があります。',
        'array' => ':attribute フィールドは :value 個以下のアイテムを含む必要があります。',
    ],
    'max' => [
        'numeric' => ':attribute フィールドは :max より大きくてはいけません。',
        'file' => ':attribute フィールドは :max キロバイトより大きくてはいけません。',
        'string' => ':attribute フィールドは :max 文字より多くてはいけません。',
        'array' => ':attribute フィールドは :max 個より多いアイテムを含んではいけません。',
    ],
    'mimes' => ':attribute フィールドは次のタイプのファイルでなければなりません: :values。',
    'mimetypes' => ':attribute フィールドは次のタイプのファイルでなければなりません: :values。',
    'min' => [
        'numeric' => ':attribute フィールドは少なくとも :min である必要があります。',
        'file' => ':attribute フィールドは少なくとも :min キロバイトである必要があります。',
        'string' => ':attribute フィールドは少なくとも :min 文字である必要があります。',
        'array' => ':attribute フィールドは少なくとも :min 個のアイテムを含む必要があります。',
    ],
    'not_in' => '選択された :attribute は無効です。',
    'not_regex' => ':attribute フィールドの形式が無効です。',
    'numeric' => ':attribute フィールドは数値でなければなりません。',
    'password' => 'パスワードが正しくありません。',
    'present' => ':attribute フィールドが存在する必要があります。',
    'regex' => ':attribute フィールドの形式が無効です。',
    'required' => ':attribute フィールドは必須です。',
    'required_if' => ':other が :value の場合、:attribute フィールドは必須です。',
    'required_unless' => ':other が :values にない場合、:attribute フィールドは必須です。',
    'required_with' => ':values が存在する場合、:attribute フィールドは必須です。',
    'required_with_all' => ':values が存在する場合、:attribute フィールドは必須です。',
    'required_without' => ':values が存在しない場合、:attribute フィールドは必須です。',
    'required_without_all' => ':values のいずれも存在しない場合、:attribute フィールドは必須です。',
    'same' => ':attribute と :other は一致する必要があります。',
    'size' => [
        'numeric' => ':attribute フィールドは :size である必要があります。',
        'file' => ':attribute フィールドは :size キロバイトである必要があります。',
        'string' => ':attribute フィールドは :size 文字である必要があります。',
        'array' => ':attribute フィールドは :size 個のアイテムを含む必要があります。',
    ],
    'starts_with' => ':attribute フィールドは次のいずれかで始まる必要があります: :values。',
    'string' => ':attribute フィールドは文字列でなければなりません。',
    'timezone' => ':attribute フィールドは有効なタイムゾーンでなければなりません。',
    'unique' => ':attribute は既に使用されています。',
    'uploaded' => ':attribute のアップロードに失敗しました。',
    'url' => ':attribute フィールドは有効なURLでなければなりません。',
    'uuid' => ':attribute フィールドは有効なUUIDでなければなりません。',

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | Here you may specify custom validation messages for attributes using the
    | convention "attribute.rule" to name the lines. This makes it quick to
    | specify a specific custom language line for a given attribute rule.
    |
    */

    'custom' => [
        'attribute-name' => [
            'rule-name' => 'custom-message',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Attributes
    |--------------------------------------------------------------------------
    |
    | The following language lines are used to swap our attribute placeholder
    | with something more reader friendly such as "E-Mail Address" instead
    | of "email". This simply helps us make our message more expressive.
    |
    */

    'attributes' => [],

];
