# Company Crawler - Docker Setup Guide

## Tổng quan Docker Architecture

Company Crawler sử dụng **multi-container architecture** với Docker Compose để orchestrate các services:

```mermaid
graph TB
    subgraph "Docker Compose Environment"
        A[Nginx<br/>Port 8000] --> B[PHP-FPM<br/>Laravel App]
        B --> C[MySQL<br/>Database]
        B --> D[Redis<br/>Cache & Queue]
        
        E[Queue Worker<br/>Background Jobs] --> D
        E --> B
        
        F[Node.js Container<br/>Crawling Scripts] --> G[Chrome Browser]
        H[Python Container<br/>Japanese Processing] --> I[pykakasi]
        
        B -.->|Execute Scripts| F
        B -.->|Execute Scripts| H
    end
```

## Prerequisites

### System Requirements
- **Docker**: 20.x hoặc cao hơn
- **Docker Compose**: 2.x hoặc cao hơn  
- **RAM**: Minimum 4GB (recommended 8GB+ cho crawling workloads)
- **Storage**: 20GB+ available space
- **OS**: Linux, macOS, hoặc Windows với WSL2

### Install Docker
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install docker.io docker-compose-plugin
sudo systemctl start docker
sudo systemctl enable docker
sudo usermod -aG docker $USER

# macOS (using Homebrew)
brew install --cask docker

# Windows
# Download Docker Desktop từ https://docker.com
```

## Quick Start

### 1. Clone và Setup
```bash
git clone <repository-url>
cd company-crawler

# Copy environment file
cp .env.example .env.docker
```

### 2. Configure Environment (.env.docker)
```env
# App Configuration
APP_NAME="Company Crawler"
APP_ENV=production
APP_KEY=base64:your-generated-key-here
APP_DEBUG=false
APP_URL=http://localhost:8000

# Docker Database Configuration
DB_CONNECTION=mysql
DB_HOST=database
DB_PORT=3306
DB_DATABASE=company_crawler
DB_USERNAME=crawler_user
DB_PASSWORD=crawler_password

# Docker Redis Configuration
QUEUE_CONNECTION=redis
REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379

# Browser Configuration cho Docker
PUPPETEER_EXECUTABLE_PATH=/usr/bin/google-chrome

# Crawling Configuration
CRAWL_DELAY=2
CRAWL_TIMEOUT=60
MAX_RETRY_ATTEMPTS=3

# File Storage
FILESYSTEM_DISK=local

# Logging
LOG_CHANNEL=stack
LOG_LEVEL=info
```

### 3. Build và Start Services
```bash
# Build tất cả containers
docker-compose build

# Start all services
docker-compose up -d

# Check status
docker-compose ps
```

### 4. Initialize Application
```bash
# Generate application key
docker-compose exec app php artisan key:generate

# Run database migrations
docker-compose exec app php artisan migrate

# Seed database
docker-compose exec app php artisan db:seed

# Create admin user
docker-compose exec app php artisan tinker
>>> $user = \App\Models\User::create([
...     'name' => 'Admin User',
...     'email' => '<EMAIL>',
...     'password' => bcrypt('password123'),
...     'role' => 'admin'
... ]);
```

## Container Architecture

### Service Breakdown

#### 1. App Container (PHP/Laravel)
```yaml
app:
  build: .
  volumes:
    - ./:/var/www
  environment:
    - DB_HOST=database
    - REDIS_HOST=redis
```
- **Base**: PHP 8.2-FPM với all extensions
- **Includes**: Composer, Node.js, Python, Chrome
- **Purpose**: Main Laravel application

#### 2. Nginx Container
```yaml
webserver:
  image: nginx:alpine
  ports:
    - "8000:80"
  volumes:
    - ./docker/nginx/:/etc/nginx/
```
- **Purpose**: Web server for serving application
- **Access**: http://localhost:8000

#### 3. MySQL Container
```yaml
database:
  image: mysql:8.0
  environment:
    MYSQL_DATABASE: company_crawler
    MYSQL_USER: crawler_user
```
- **Data**: Persistent volume `dbdata`
- **Access**: Port 3306 (internal)

#### 4. Redis Container
```yaml
redis:
  image: redis:7-alpine
  volumes:
    - redisdata:/data
```
- **Purpose**: Cache và queue backend
- **Data**: Persistent volume `redisdata`

#### 5. Queue Worker Container
```yaml
queue:
  build: .
  command: php artisan queue:work redis
  depends_on:
    - app
    - database
    - redis
```
- **Purpose**: Background job processing
- **Auto-restart**: Yes với unless-stopped policy

#### 6. Node.js Crawler Container
```yaml
crawler:
  build:
    context: ./scripts
    dockerfile: Dockerfile.node
  cap_add:
    - SYS_ADMIN  # For Chrome
```
- **Purpose**: Execute crawling scripts
- **Chrome**: Pre-installed và configured
- **Security**: Runs as non-root user

#### 7. Python Processor Container
```yaml
python-processor:
  build:
    context: ./crawl-executives-scripts
    dockerfile: Dockerfile.python
```
- **Purpose**: Japanese name processing
- **Library**: pykakasi pre-installed

## Development với Docker

### Common Commands
```bash
# View logs
docker-compose logs -f app
docker-compose logs -f queue
docker-compose logs -f crawler

# Execute commands trong containers
docker-compose exec app php artisan migrate
docker-compose exec app php artisan queue:work --once
docker-compose exec crawler node crawl-companies.js --help
docker-compose exec python-processor python3 kanji_to_romaji.py "田中太郎"

# Restart services
docker-compose restart app
docker-compose restart queue

# Access container shell
docker-compose exec app bash
docker-compose exec crawler bash
docker-compose exec python-processor bash
```

### File Monitoring
```bash
# Monitor crawling logs
docker-compose exec app tail -f storage/logs/laravel.log

# Monitor queue processing
docker-compose logs -f queue

# Monitor all services
docker-compose logs -f
```

## Production Deployment

### Environment Configuration
```bash
# Copy production environment
cp .env.example .env.production

# Update production values
nano .env.production
```

### Production-specific settings (.env.production)
```env
APP_ENV=production
APP_DEBUG=false
LOG_LEVEL=warning

# Stronger database credentials
DB_PASSWORD=strong-production-password
MYSQL_ROOT_PASSWORD=strong-root-password

# Performance optimization
REDIS_CLUSTER=false
QUEUE_CONNECTION=redis

# Security
SESSION_SECURE_COOKIE=true
SANCTUM_STATEFUL_DOMAINS=your-domain.com
```

### SSL/HTTPS Setup
```yaml
# docker-compose.prod.yml
webserver:
  ports:
    - "80:80"
    - "443:443"
  volumes:
    - ./docker/nginx/ssl:/etc/nginx/ssl
    - ./docker/nginx/nginx.prod.conf:/etc/nginx/nginx.conf
```

### Resource Limits
```yaml
app:
  deploy:
    resources:
      limits:
        cpus: '2.0'
        memory: 2G
      reservations:
        cpus: '1.0'
        memory: 1G

crawler:
  deploy:
    resources:
      limits:
        cpus: '4.0'
        memory: 4G  # Chrome needs more memory
```

## Monitoring & Maintenance

### Health Checks
```bash
# Check all services health
docker-compose ps

# Check application health
curl http://localhost:8000/health

# Check database connection
docker-compose exec app php artisan tinker
>>> DB::connection()->getPdo();
```

### Performance Monitoring
```bash
# Container resource usage
docker stats

# Disk usage
docker system df

# Network usage
docker-compose exec app netstat -tuln
```

### Backup & Restore
```bash
# Database backup
docker-compose exec database mysqldump -u crawler_user -p company_crawler > backup.sql

# Volume backup
docker run --rm -v company-crawler_dbdata:/data -v $(pwd):/backup alpine tar czf /backup/dbdata.tar.gz -C /data .

# Restore
docker run --rm -v company-crawler_dbdata:/data -v $(pwd):/backup alpine tar xzf /backup/dbdata.tar.gz -C /data
```

## Troubleshooting

### Common Issues

#### Container Build Errors
```bash
# Clear build cache
docker-compose build --no-cache

# Check individual container builds
docker build -t test-app .
docker build -t test-node scripts/
docker build -t test-python crawl-executives-scripts/
```

#### Chrome/Puppeteer Issues trong Container
```bash
# Check Chrome installation
docker-compose exec crawler google-chrome --version

# Test Puppeteer
docker-compose exec crawler node -e "
const puppeteer = require('puppeteer');
(async () => {
  const browser = await puppeteer.launch({
    headless: 'new',
    args: ['--no-sandbox', '--disable-dev-shm-usage']
  });
  console.log('✅ Chrome launched successfully');
  await browser.close();
})();
"
```

#### Database Connection Issues
```bash
# Check database status
docker-compose exec database mysql -u root -p -e "SELECT 1"

# Reset database
docker-compose down
docker volume rm company-crawler_dbdata
docker-compose up -d database
# Wait for database to start, then run migrations
```

#### Queue Worker Issues
```bash
# Restart queue worker
docker-compose restart queue

# Check queue status
docker-compose exec app php artisan queue:monitor

# Clear failed jobs
docker-compose exec app php artisan queue:clear
```

#### Memory Issues
```bash
# Check memory usage
docker stats --no-stream

# Increase Docker memory limits (Docker Desktop)
# Settings > Resources > Memory > Increase to 8GB+

# Or add to docker-compose.yml
services:
  app:
    mem_limit: 2g
  crawler:
    mem_limit: 4g
```

### Log Debugging
```bash
# All logs
docker-compose logs

# Specific service logs
docker-compose logs app
docker-compose logs queue
docker-compose logs crawler

# Follow logs in real-time
docker-compose logs -f --tail=100 app
```

## Scaling & Optimization

### Horizontal Scaling
```yaml
# docker-compose.scale.yml
queue:
  scale: 3  # Run 3 queue workers

crawler:
  scale: 2  # Run 2 crawler instances
```

### Performance Tuning
```yaml
app:
  environment:
    - PHP_MEMORY_LIMIT=512M
    - PHP_MAX_EXECUTION_TIME=300

crawler:
  environment:
    - NODE_OPTIONS=--max-old-space-size=4096
```

## Security Considerations

### Container Security
```yaml
app:
  user: "1000:1000"  # Non-root user
  read_only: true
  tmpfs:
    - /tmp
    - /var/tmp

crawler:
  cap_drop:
    - ALL
  cap_add:
    - SYS_ADMIN  # Only for Chrome
  security_opt:
    - no-new-privileges:true
```

### Network Security
```yaml
networks:
  company-crawler:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
```

## Kết luận

Docker setup provides:

✅ **Consistent Environment**: Same environment across dev/staging/production  
✅ **Easy Deployment**: Single command deployment với docker-compose  
✅ **Isolation**: Each service isolated trong own container  
✅ **Scalability**: Easy horizontal scaling của queue workers và crawlers  
✅ **Resource Management**: Container-level resource limits và monitoring  
✅ **Multi-Language Support**: PHP, Node.js, Python all working together  
✅ **Browser Automation**: Chrome pre-configured với proper permissions  
✅ **Japanese Processing**: pykakasi ready to use  

Hệ thống Docker này cung cấp production-ready environment cho Company Crawler với all dependencies được properly configured và isolated. 