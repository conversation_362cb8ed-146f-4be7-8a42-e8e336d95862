<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use App\Models\DataSource;
use App\Models\User;
use App\Jobs\CrawlCompanyListJob;

class PaginationCrawlTest extends TestCase
{
    use RefreshDatabase;

    public function test_pagination_url_building()
    {
        $dataSource = DataSource::factory()->create([
            'name' => 'Test Source',
            'source_url' => 'https://example.com/companies',
            'has_pagination' => true,
            'pagination_format' => 'query_param',
            'pagination_base_url' => null,
            'max_pages' => 5,
        ]);

        // Test query parameter format
        $this->assertEquals(
            'https://example.com/companies',
            $dataSource->buildPaginationUrl(1)
        );
        
        $this->assertEquals(
            'https://example.com/companies?page=2',
            $dataSource->buildPaginationUrl(2)
        );

        // Test path segment format
        $dataSource->update(['pagination_format' => 'path_segment']);
        $this->assertEquals(
            'https://example.com/companies/page/2',
            $dataSource->buildPaginationUrl(2)
        );

        // Test with custom base URL
        $dataSource->update([
            'pagination_format' => 'query_param',
            'pagination_base_url' => 'https://api.example.com/list'
        ]);
        $this->assertEquals(
            'https://api.example.com/list?page=3',
            $dataSource->buildPaginationUrl(3)
        );
    }

    public function test_max_pages_determination()
    {
        $user = User::factory()->create();
        
        // Test with pagination disabled
        $dataSource = DataSource::factory()->create([
            'has_pagination' => false,
            'max_pages' => 10,
        ]);
        
        $job = new CrawlCompanyListJob($dataSource, 0, 0, $user->id);
        
        // Use reflection to test private method
        $reflection = new \ReflectionClass($job);
        $method = $reflection->getMethod('determineMaxPagesToCrawl');
        $method->setAccessible(true);
        
        $this->assertEquals(1, $method->invokeArgs($job, []));
        
        // Test with pagination enabled and job parameter
        $dataSource->update(['has_pagination' => true]);
        $job = new CrawlCompanyListJob($dataSource, 3, 0, $user->id);
        
        $this->assertEquals(3, $method->invokeArgs($job, []));
        
        // Test with DataSource max_pages
        $job = new CrawlCompanyListJob($dataSource, 0, 0, $user->id);
        
        $this->assertEquals(10, $method->invokeArgs($job, []));
        
        // Test default fallback
        $dataSource->update(['max_pages' => null]);
        
        $this->assertEquals(50, $method->invokeArgs($job, []));
    }

    public function test_all_pagination_formats()
    {
        $formats = [
            'query_param' => 'https://example.com?page=2',
            'path_segment' => 'https://example.com/page/2',
            'query_p' => 'https://example.com?p=2',
            'query_start' => 'https://example.com?start=20',
            'offset' => 'https://example.com?offset=20',
            'custom_offset' => 'https://example.com?from=20',
        ];

        foreach ($formats as $format => $expectedUrl) {
            $dataSource = DataSource::factory()->create([
                'source_url' => 'https://example.com',
                'has_pagination' => true,
                'pagination_format' => $format,
            ]);

            $actualUrl = $dataSource->buildPaginationUrl(2);
            $this->assertEquals($expectedUrl, $actualUrl, "Failed for format: {$format}");
        }
    }
}
