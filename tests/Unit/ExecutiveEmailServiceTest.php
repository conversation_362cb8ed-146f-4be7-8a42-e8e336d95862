<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\ExecutiveEmailService;

class ExecutiveEmailServiceTest extends TestCase
{
    private ExecutiveEmailService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new ExecutiveEmailService();
    }

    public function test_converts_japanese_names_to_romaji()
    {
        // Test Hiragana - Python script will convert and format properly
        $result = $this->service->convertToRomaji('たなか');
        $this->assertNotEmpty($result);
        $this->assertIsString($result);

        // Test Katakana - Python script will convert and format properly
        $result = $this->service->convertToRomaji('タナカ');
        $this->assertNotEmpty($result);
        $this->assertIsString($result);

        // Test Kanji conversion - should be properly formatted with space
        $result = $this->service->convertToRomaji('田中太郎');
        $this->assertEquals('Tanaka Tarou', $result);

        // Test mixed Hiragana with Kanji
        $result = $this->service->convertTo<PERSON><PERSON>ji('たなか太郎');
        $this->assertNotEmpty($result);
        $this->assertIsString($result);
    }

    public function test_removes_common_titles()
    {
        // Test that titles are removed and names are properly formatted
        $names = [
            '代表取締役社長 田中太郎' => 'Tanaka Tarou',
            'CEO John Smith' => 'John Smith', 
            '取締役部長 佐藤次郎' => 'Sato Jirou',
        ];

        foreach ($names as $input => $expected) {
            $result = $this->service->convertToRomaji($input);
            // For names with titles, we expect proper formatting
            $this->assertNotEmpty($result);
            $this->assertIsString($result);
        }
    }

    public function test_generates_email_predictions()
    {
        // Test with Latin name
        $predictions = $this->service->generateEmailPredictions('John Smith', 'example.com');
        
        $this->assertIsArray($predictions);
        $this->assertNotEmpty($predictions);
        
        // Should contain various email formats
        $this->assertContains('<EMAIL>', $predictions);
        
        // Test with Japanese name (should work after romaji conversion)
        $predictions = $this->service->generateEmailPredictions('tanaka taro', 'example.com');
        $this->assertNotEmpty($predictions);
        $this->assertContains('<EMAIL>', $predictions);
    }

    public function test_cleans_domain_properly()
    {
        $testCases = [
            'https://www.example.com' => 'example.com',
            'http://example.com/' => 'example.com',
            'www.example.com:8080' => 'example.com',
            'example.com/path/to/page' => 'example.com',
        ];

        foreach ($testCases as $input => $expected) {
            $predictions = $this->service->generateEmailPredictions('test', $input);
            
            foreach ($predictions as $email) {
                $this->assertStringContainsString("@{$expected}", $email);
            }
        }
    }

    public function test_processes_executive_with_all_fields()
    {
        $executive = [
            'name' => 'たなか たろう',
            'position' => 'CEO',
            'level' => 'c_level',
            'email' => '',
            'phone' => '',
            'bio' => 'Test bio'
        ];

        $result = $this->service->processExecutive($executive, 'example.com');

        $this->assertArrayHasKey('name_romaji', $result);
        $this->assertArrayHasKey('email_predictions', $result);
        $this->assertIsArray($result['email_predictions']);
        $this->assertNotEmpty($result['email_predictions']);
        
        // Original fields should remain
        $this->assertEquals($executive['name'], $result['name']);
        $this->assertEquals($executive['position'], $result['position']);
        $this->assertEquals($executive['level'], $result['level']);
    }

    public function test_handles_empty_or_invalid_input()
    {
        // Empty name
        $result = $this->service->generateEmailPredictions('', 'example.com');
        $this->assertEmpty($result);

        // Empty domain
        $result = $this->service->generateEmailPredictions('test', '');
        $this->assertEmpty($result);

        // Invalid executive array
        $result = $this->service->processExecutive([], 'example.com');
        $this->assertArrayHasKey('name_romaji', $result);
        $this->assertArrayHasKey('email_predictions', $result);
    }

    public function test_processes_multiple_executives()
    {
        $executives = [
            ['name' => 'たなか たろう', 'position' => 'CEO'],
            ['name' => 'やまだ はなこ', 'position' => 'CTO'],
            ['name' => 'さとう じろう', 'position' => 'CFO'],
        ];

        $result = $this->service->processCompanyExecutives($executives, 'example.com');

        $this->assertCount(3, $result);
        
        foreach ($result as $executive) {
            $this->assertArrayHasKey('name_romaji', $executive);
            $this->assertArrayHasKey('email_predictions', $executive);
            $this->assertIsArray($executive['email_predictions']);
        }
    }

    public function test_email_prediction_formats()
    {
        $predictions = $this->service->generateEmailPredictions('John Smith', 'example.com');
        
        // Should contain various common formats
        $expectedFormats = [
            '<EMAIL>',
            '<EMAIL>', 
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ];

        foreach ($expectedFormats as $format) {
            $this->assertContains($format, $predictions, "Missing format: {$format}");
        }
    }

    public function test_email_validation_requires_minimum_3_characters_before_at()
    {
        // Test the private isValidEmail method indirectly through generateEmailPredictions
        // by creating test cases that would generate short email prefixes
        
        // These should be filtered out due to short prefixes
        $shortNamePredictions = $this->service->generateEmailPredictions('A B', 'example.com');
        
        // Check that no email in predictions has less than 3 characters before @
        foreach ($shortNamePredictions as $email) {
            $atPosition = strpos($email, '@');
            $this->assertGreaterThanOrEqual(3, $atPosition, "Email '{$email}' has less than 3 characters before @ symbol");
        }
        
        // Valid emails with 3+ characters should be included
        $validPredictions = $this->service->generateEmailPredictions('John Smith', 'example.com');
        $this->assertNotEmpty($validPredictions);
        
        // All valid predictions should have at least 3 characters before @
        foreach ($validPredictions as $email) {
            $atPosition = strpos($email, '@');
            $this->assertGreaterThanOrEqual(3, $atPosition, "Email '{$email}' should have at least 3 characters before @ symbol");
        }
    }
}
