#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Kanji to Romaji Converter Script
Uses pykakasi library to convert Japanese kanji text to romaji (Latin alphabet)
"""

import pykakasi
import argparse
import sys

class KanjiToRomajiConverter:
    def __init__(self):
        """Initialize the converter with pykakasi"""
        self.kks = pykakasi.kakasi()
        
    def convert_to_romaji(self, text, capitalize=False):
        """
        Convert kanji text to romaji
        
        Args:
            text (str): Japanese text containing kanji
            capitalize (bool): Whether to capitalize the first letter of each word
            
        Returns:
            str: Romaji conversion of the input text
        """
        result = self.kks.convert(text)
        romaji_parts = []
        
        for item in result:
            romaji_parts.append(item['hepburn'])
        
        romaji_text = ''.join(romaji_parts)
        
        if capitalize:
            # Capitalize first letter of each word (assuming spaces separate words)
            words = romaji_text.split(' ')
            capitalized_words = [word.capitalize() for word in words]
            romaji_text = ' '.join(capitalized_words)
        
        return romaji_text
    
    def convert_names_list(self, names_list, capitalize=True):
        """
        Convert a list of kanji names to romaji
        
        Args:
            names_list (list): List of Japanese names in kanji
            capitalize (bool): Whether to capitalize names
            
        Returns:
            list: List of tuples (original_kanji, romaji_conversion)
        """
        converted_names = []
        for name in names_list:
            romaji = self.convert_to_romaji(name, capitalize)
            converted_names.append((name, romaji))
        return converted_names

def run_interactive_mode():
    """Run the interactive mode"""
    converter = KanjiToRomajiConverter()
    
    print("=== Kanji to Romaji Converter ===")
    print()
    
    # Example Japanese names
    sample_names = [
        "田中太郎",    # Tanaka Tarou
        "佐藤花子",    # Satou Hanako  
        "山田一郎",    # Yamada Ichirou
        "鈴木美咲",    # Suzuki Misaki
        "高橋健太",    # Takahashi Kenta
        "渡辺由美",    # Watanabe Yumi
        "伊藤正義",    # Itou Masayoshi
        "中村愛",      # Nakamura Ai
    ]
    
    print("Sample conversions:")
    print("-" * 40)
    
    converted_names = converter.convert_names_list(sample_names)
    for original, romaji in converted_names:
        print(f"{original:8} → {romaji}")
    
    print()
    print("-" * 40)
    
    # Interactive mode
    print("Interactive mode (press Enter with empty input to exit):")
    while True:
        try:
            user_input = input("\nEnter kanji text: ").strip()
            if not user_input:
                break
            
            romaji = converter.convert_to_romaji(user_input, capitalize=True)
            print(f"Romaji: {romaji}")
            
        except KeyboardInterrupt:
            print("\nExiting...")
            break
        except Exception as e:
            print(f"Error: {e}")

def main():
    """Main function with argument parsing"""
    parser = argparse.ArgumentParser(description='Convert kanji names to romaji')
    parser.add_argument('name', nargs='?', help='Kanji name to convert to romaji')
    parser.add_argument('-c', '--capitalize', action='store_true', 
                       help='Capitalize the romaji output')
    
    args = parser.parse_args()
    
    converter = KanjiToRomajiConverter()
    
    # If name argument is provided, convert and print result only
    if args.name:
        try:
            romaji = converter.convert_to_romaji(args.name, args.capitalize)
            print(romaji)
        except Exception as e:
            print(f"Error: {e}", file=sys.stderr)
            sys.exit(1)
    else:
        # Run interactive mode if no arguments
        run_interactive_mode()

if __name__ == "__main__":
    main() 