#!/usr/bin/env python3
"""
Email Predictions Generator using MailScout with Custom API Validation

This script generates email predictions for executives using the mailscout library
and validates them using a custom API service instead of SMTP.

Usage:
    python3 generate_email_predictions.py "Executive Name" "company-domain.com"
    python3 generate_email_predictions.py "田中太郎" "example.co.jp"
    python3 generate_email_predictions.py blacklist list
    python3 generate_email_predictions.py blacklist add domain.com

Features:
- Advanced email pattern generation using mailscout
- Custom API validation service (replacing SMTP)
- Japanese name support with Romaji conversion
- Name normalization for email formats
- Catch-all domain detection and filtering
- Automatic blacklist management
"""

import sys
import json
import argparse
import logging
from typing import List, Dict, Optional, Tuple
import re
import os
import subprocess
import random
import string
from datetime import datetime

try:
    from mailscout import Scout
except ImportError:
    print("Error: mailscout library not installed. Run: pip install mailscout")
    sys.exit(1)

try:
    import pykakasi
except ImportError:
    print("Error: pykakasi library not installed. Run: pip install pykakasi")
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EmailPredictor:
    """Advanced email prediction and validation using MailScout + Custom API Service"""

    def __init__(self, check_deliverability: bool = True):
        """
        Initialize EmailPredictor with MailScout configuration

        Args:
            check_deliverability: Whether to check email deliverability via API
        """
        # Initialize MailScout with optimized settings (NO SMTP)
        self.scout = Scout(
            check_variants=True,      # Generate email variants
            check_prefixes=False,     # Don't check common prefixes for executives
            check_catchall=False,     # Skip catch-all checks to avoid SMTP
            normalize=True,           # Normalize names
            num_threads=3,            # Moderate threading for stability
            smtp_timeout=1            # Minimal timeout since we skip SMTP
        )

        # Initialize Japanese processor
        self.kks = pykakasi.kakasi()

        self.check_deliverability = check_deliverability
        self.domain_cache = {}  # Cache for domain information

        # API Configuration
        self.api_url = "http://**************:3472/api/validate-emails"
        self.api_key = "SrmnTK1itOO8vJzEVFQiEG9kltXHxG9B"

        # Load catch-all domains blacklist
        self.catch_all_domains = self._load_catch_all_domains()

        logger.info("EmailPredictor initialized with MailScout + Custom API Service + Catch-All Detection")

    def _load_catch_all_domains(self) -> set:
        """Load catch-all domains from blacklist file"""
        try:
            blacklist_file = os.path.join(os.path.dirname(__file__), 'catch_all_domains.json')
            if os.path.exists(blacklist_file):
                with open(blacklist_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    domains = set(data.get('catch_all_domains', []))
                    logger.info(f"Loaded {len(domains)} catch-all domains from blacklist")
                    return domains
        except Exception as e:
            logger.warning(f"Failed to load catch-all domains: {e}")

        # Return empty set if file doesn't exist - will be populated when domains are detected
        logger.info("No catch-all domains file found, starting with empty blacklist")
        return set()

    def _save_catch_all_domains(self):
        """Save updated catch-all domains to blacklist file"""
        try:
            blacklist_file = os.path.join(os.path.dirname(__file__), 'catch_all_domains.json')

            # Load existing data to preserve other fields
            existing_data = {}
            if os.path.exists(blacklist_file):
                try:
                    with open(blacklist_file, 'r', encoding='utf-8') as f:
                        existing_data = json.load(f)
                except:
                    pass

            # Update domains list
            updated_data = {
                **existing_data,
                'catch_all_domains': sorted(list(self.catch_all_domains)),
                'last_updated': datetime.now().isoformat(),
                'auto_detected_count': len(self.catch_all_domains) - len(existing_data.get('catch_all_domains', []))
            }

            with open(blacklist_file, 'w', encoding='utf-8') as f:
                json.dump(updated_data, f, ensure_ascii=False, indent=2)

            logger.info(f"Updated catch-all domains file with {len(self.catch_all_domains)} domains")
        except Exception as e:
            logger.error(f"Failed to save catch-all domains: {e}")

    def _add_to_catch_all_blacklist(self, domain: str, reason: str = "Auto-detected"):
        """Add domain to catch-all blacklist and save to file"""
        if domain.lower() not in self.catch_all_domains:
            self.catch_all_domains.add(domain.lower())
            logger.warning(f"🚨 Added {domain} to catch-all blacklist: {reason}")

            # Save to file immediately
            self._save_catch_all_domains()

            return True
        return False

    def _is_catch_all_domain(self, domain: str) -> bool:
        """Check if domain is known to have catch-all behavior"""
        if domain.lower() in self.catch_all_domains:
            logger.warning(f"🚨 Catch-all domain detected: {domain}")
            return True
        return False

    def _detect_catch_all_behavior(self, domain: str) -> bool:
        """
        Automatically detect catch-all behavior by testing random emails

        Args:
            domain: Domain to test

        Returns:
            True if catch-all behavior detected
        """
        try:
            # Skip detection if already known
            if domain.lower() in self.catch_all_domains:
                return True

            # Generate 5 random email addresses for better accuracy
            random_emails = []
            for _ in range(5):
                random_prefix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=12))
                random_emails.append(f'{random_prefix}@{domain}')

            logger.info(f"🔍 Testing catch-all behavior for {domain} with {len(random_emails)} random emails")
            results = self.validate_emails_via_api(random_emails)

            # Count valid random emails
            valid_count = sum(1 for r in results if r.get('is_valid') == True)
            total_tested = len(results)

            # Improved detection logic:
            # - If 3+ out of 5 random emails are valid → likely catch-all
            # - If 80%+ are valid → definitely catch-all
            is_catch_all = valid_count >= 3 or (total_tested > 0 and valid_count / total_tested >= 0.8)

            if is_catch_all:
                reason = f"Auto-detected: {valid_count}/{total_tested} random emails valid"
                logger.warning(f"🚨 Catch-all behavior detected for {domain} ({reason})")

                # Add to blacklist automatically
                self._add_to_catch_all_blacklist(domain, reason)

                return True
            else:
                logger.info(f"✅ Normal domain behavior for {domain} ({valid_count}/{total_tested} random emails valid)")
                return False

        except Exception as e:
            logger.error(f"Error detecting catch-all behavior for {domain}: {e}")
            return False

    def _check_suspicious_validation_rate(self, validated_results: List[Dict], domain: str) -> bool:
        """
        Check if validation results show suspiciously high success rate
        indicating possible catch-all behavior

        Args:
            validated_results: Results from email validation
            domain: Domain being tested

        Returns:
            True if suspicious pattern detected
        """
        if not validated_results:
            return False

        valid_count = sum(1 for r in validated_results if r.get('is_valid') == True)
        total_count = len(validated_results)

        if total_count == 0:
            return False

        success_rate = valid_count / total_count

        # Suspicious patterns:
        # 1. More than 70% success rate with 5+ emails
        # 2. More than 90% success rate with any number of emails
        # 3. All emails valid (100% success rate)

        is_suspicious = (
            (success_rate > 0.7 and total_count >= 5) or
            (success_rate > 0.9) or
            (success_rate == 1.0 and total_count >= 3)
        )

        if is_suspicious:
            logger.warning(f"🔍 Suspicious validation rate for {domain}: {valid_count}/{total_count} ({success_rate:.1%}) valid")
            return True

        return False

    def convert_to_romaji(self, japanese_name: str) -> Optional[str]:
        """
        Convert Japanese name to Romaji using pykakasi

        Args:
            japanese_name: Japanese name in Kanji/Hiragana/Katakana

        Returns:
            Romaji conversion or None if conversion fails
        """
        try:
            if not japanese_name or not japanese_name.strip():
                return None

            # Clean the name - remove titles and common business terms
            cleaned_name = self._clean_japanese_name(japanese_name)

            if not cleaned_name:
                return None

            # Convert to Romaji
            result = self.kks.convert(cleaned_name)
            romaji_parts = []

            for item in result:
                romaji = item.get('hepburn', '').strip()
                if romaji and romaji.replace(' ', '').isalpha():
                    romaji_parts.append(romaji)

            if not romaji_parts:
                return None

            # Join and format properly
            romaji_name = ' '.join(romaji_parts)

            # Format Japanese names properly (Family Given)
            return self._format_japanese_name(romaji_name)

        except Exception as e:
            logger.warning(f"Failed to convert Japanese name '{japanese_name}': {e}")
            return None

    def _clean_japanese_name(self, name: str) -> str:
        """Remove Japanese business titles and honorifics"""
        # Common Japanese business titles and honorifics
        titles_to_remove = [
            '代表取締役社長', '代表取締役', '取締役社長', '取締役', '常務取締役', '専務取締役',
            '執行役員', '社長', '会長', '副社長', '部長', '課長', '係長', '主任',
            '先生', 'さん', 'くん', 'ちゃん', '様', '殿',
            '株式会社', '有限会社', '合同会社', '企業', '会社'
        ]

        cleaned = name
        for title in titles_to_remove:
            cleaned = cleaned.replace(title, ' ')

        # Remove extra whitespace
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()

        return cleaned

    def _format_japanese_name(self, romaji: str) -> str:
        """Format Japanese Romaji name properly"""
        parts = romaji.split()
        if len(parts) >= 2:
            # For Japanese names, typically: Family Given
            # Ensure proper capitalization
            formatted_parts = [part.capitalize() for part in parts if part]
            return ' '.join(formatted_parts)
        return romaji.strip().capitalize()

    def generate_email_patterns(self, name: str, domain: str) -> List[str]:
        """
        Generate email patterns using MailScout + custom logic

        Args:
            name: Executive name (English or Japanese)
            domain: Company domain

        Returns:
            List of email predictions
        """
        try:
            # Clean domain
            clean_domain = self._clean_domain(domain)
            if not clean_domain:
                logger.warning(f"Invalid domain: {domain}")
                return []

            # Process name - convert Japanese if needed
            processed_name = self._process_name(name)
            if not processed_name:
                logger.warning(f"Could not process name: {name}")
                return []

            # Generate email patterns using MailScout (without SMTP)
            mailscout_emails = self._generate_mailscout_patterns(processed_name, clean_domain)

            # Generate additional custom patterns
            custom_emails = self._generate_custom_patterns(processed_name, clean_domain)

            # Combine and deduplicate
            all_emails = list(set(mailscout_emails + custom_emails))

            # Filter valid email formats
            valid_emails = [email for email in all_emails if self._is_valid_email_format(email)]

            logger.info(f"Generated {len(valid_emails)} email patterns for {name}")
            return valid_emails[:15]  # Limit to top 15 predictions

        except Exception as e:
            logger.error(f"Error generating email patterns: {e}")
            return []

    def _clean_domain(self, domain: str) -> str:
        """Clean and validate domain"""
        if not domain:
            return ""

        # Remove protocol and www
        clean = domain.replace('https://', '').replace('http://', '').replace('www.', '')

        # Take only the domain part (remove path)
        clean = clean.split('/')[0].strip()

        # Basic domain validation
        if '.' not in clean or len(clean) < 4:
            return ""

        return clean.lower()

    def _process_name(self, name: str) -> Optional[str]:
        """Process name - convert Japanese to Romaji if needed"""
        if not name or not name.strip():
            return None

        # Check if name contains Japanese characters
        if re.search(r'[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF]', name):
            # Convert Japanese to Romaji
            return self.convert_to_romaji(name)
        else:
            # Clean English name
            return self._clean_english_name(name)

    def _clean_english_name(self, name: str) -> str:
        """Clean English name"""
        # Remove common English titles
        titles = ['Mr.', 'Mrs.', 'Ms.', 'Dr.', 'Prof.', 'CEO', 'CTO', 'CFO', 'COO',
                 'President', 'Director', 'Manager', 'VP', 'Vice President']

        cleaned = name
        for title in titles:
            pattern = r'\b' + re.escape(title) + r'\b'
            cleaned = re.sub(pattern, '', cleaned, flags=re.IGNORECASE)

        # Remove extra whitespace and special characters
        cleaned = re.sub(r'[^\w\s]', ' ', cleaned)
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()

        return cleaned

    def _generate_mailscout_patterns(self, name: str, domain: str) -> List[str]:
        """Generate email patterns using MailScout (without SMTP validation)"""
        try:
            # Generate patterns using MailScout's pattern generation
            patterns = []

            # Split name into parts
            name_parts = name.split()
            if len(name_parts) >= 1:
                # Try different name combinations
                if len(name_parts) >= 2:
                    patterns.extend([
                        [name_parts[0], name_parts[-1]],  # First Last
                        [name_parts[-1], name_parts[0]],  # Last First (Japanese style)
                        name_parts,                        # All parts
                        [name_parts[0]],                   # First only
                        [name_parts[-1]]                   # Last only
                    ])
                else:
                    patterns.append([name_parts[0]])

            # Generate emails for each pattern
            generated_emails = []
            for pattern in patterns:
                try:
                    # Create custom email patterns since MailScout's find_valid_emails requires SMTP
                    emails = self._create_email_combinations(pattern, domain)
                    generated_emails.extend(emails)
                except:
                    continue

            return generated_emails

        except Exception as e:
            logger.warning(f"MailScout pattern generation failed: {e}")
            return []

    def _create_email_combinations(self, name_parts: List[str], domain: str) -> List[str]:
        """Create email combinations from name parts"""
        if not name_parts or not domain:
            return []

        emails = []

        # Normalize name parts
        normalized_parts = [self.scout.normalize_name(part) for part in name_parts if part]
        normalized_parts = [part for part in normalized_parts if part]

        if not normalized_parts:
            return []

        if len(normalized_parts) >= 2:
            first = normalized_parts[0]
            last = normalized_parts[-1]

            # Common email patterns
            patterns = [
                f"{first}.{last}@{domain}",
                f"{last}.{first}@{domain}",
                f"{first}_{last}@{domain}",
                f"{first}-{last}@{domain}",
                f"{first}{last}@{domain}",
                f"{last}{first}@{domain}",
                f"{first[0]}.{last}@{domain}",
                f"{first}.{last[0]}@{domain}",
                f"{first[0]}{last}@{domain}",
                f"{last}{first[0]}@{domain}",
                f"{first[0]}.{last[0]}@{domain}",
                f"{first}@{domain}",
                f"{last}@{domain}"
            ]

            emails.extend(patterns)

        elif len(normalized_parts) == 1:
            name = normalized_parts[0]
            emails.extend([
                f"{name}@{domain}",
                f"{name[0]}@{domain}" if len(name) > 1 else ""
            ])

        # Filter out empty emails
        return [email for email in emails if email and '@' in email]

    def _generate_custom_patterns(self, name: str, domain: str) -> List[str]:
        """Generate additional custom email patterns"""
        emails = []

        try:
            # Split name
            parts = name.split()
            if not parts:
                return []

            # Normalize parts using MailScout
            normalized_parts = []
            for part in parts:
                normalized = self.scout.normalize_name(part)
                if normalized:
                    normalized_parts.append(normalized)

            if not normalized_parts:
                return []

            # Additional patterns not covered by MailScout
            if len(normalized_parts) >= 2:
                first = normalized_parts[0]
                last = normalized_parts[-1]

                # Japanese business style patterns
                additional_patterns = [
                    f"{last}-{first}@{domain}",
                    f"{first}_{last[0]}@{domain}",
                    f"{last[0]}_{first}@{domain}",
                    f"{first[0:2]}.{last}@{domain}",
                    f"{last}.{first[0:2]}@{domain}",
                    f"{''.join(normalized_parts)}@{domain}",
                ]

                emails.extend(additional_patterns)

        except Exception as e:
            logger.warning(f"Custom pattern generation failed: {e}")

        return emails

    def _is_valid_email_format(self, email: str) -> bool:
        """Validate email format"""
        if not email or '@' not in email:
            return False

        # Check if email has at least 2 characters before @ symbol
        local_part = email.split('@')[0]
        if len(local_part) < 2:
            return False

        # Basic email format validation
        pattern = r'^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'

        if not re.match(pattern, email):
            return False

        # Additional checks
        if '..' in email or email.startswith('.') or email.endswith('.'):
            return False

        if len(email) > 254:  # RFC limit
            return False

        return True

    def validate_emails_via_api(self, emails: List[str]) -> List[Dict[str, any]]:
        """
        Validate email deliverability using Custom API Service

        Args:
            emails: List of email addresses to validate

        Returns:
            List of dictionaries with email and validation results
        """
        if not self.check_deliverability or not emails:
            # Return emails without validation
            return [{"email": email, "is_valid": None} for email in emails]

        try:
            # Prepare payload
            payload = {
                "emails": emails
            }

            # Prepare curl command
            curl_command = [
                'curl', '-X', 'POST', self.api_url,
                '-H', 'Content-Type: application/json',
                '-H', f'X-API-Key: {self.api_key}',
                '-d', json.dumps(payload),
                '--connect-timeout', '30',
                '--max-time', '60',
                '--silent'
            ]

            logger.info(f"Validating {len(emails)} emails via API service...")

            # Execute curl command
            result = subprocess.run(curl_command, capture_output=True, text=True, timeout=65)

            if result.returncode != 0:
                logger.error(f"API validation failed: {result.stderr}")
                return [{"email": email, "is_valid": False, "validation_error": "API call failed"} for email in emails]

            # Parse response
            try:
                response = json.loads(result.stdout)

                if 'results' not in response:
                    logger.error("Invalid API response format")
                    return [{"email": email, "is_valid": False, "validation_error": "Invalid API response"} for email in emails]

                # Process results
                validated_emails = []
                for result_item in response['results']:
                    validated_emails.append({
                        "email": result_item.get("email"),
                        "is_valid": result_item.get("is_valid", False),
                        "reason": result_item.get("reason"),
                        "validation_details": result_item.get("validation_details")
                    })

                logger.info(f"API validation completed: {response.get('summary', {}).get('valid', 0)} valid out of {len(emails)}")
                return validated_emails

            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse API response: {e}")
                return [{"email": email, "is_valid": False, "validation_error": "JSON parse error"} for email in emails]

        except subprocess.TimeoutExpired:
            logger.error("API validation timeout")
            return [{"email": email, "is_valid": False, "validation_error": "API timeout"} for email in emails]
        except Exception as e:
            logger.error(f"API validation error: {e}")
            return [{"email": email, "is_valid": False, "validation_error": str(e)} for email in emails]

    def generate_and_validate(self, name: str, domain: str) -> Dict[str, any]:
        """
        Complete email prediction and validation pipeline

        Args:
            name: Executive name
            domain: Company domain

        Returns:
            Dictionary with predictions and validation results
        """
        try:
            logger.info(f"Generating email predictions for {name} at {domain}")

            # Clean domain for catch-all check
            clean_domain = self._clean_domain(domain)

            # Check if domain is known catch-all
            is_known_catch_all = self._is_catch_all_domain(clean_domain)

            # If domain is already known as catch-all, return empty predictions immediately
            if is_known_catch_all:
                logger.warning(f"🚨 Skipping email generation for known catch-all domain: {clean_domain}")
                return {
                    "success": True,
                    "name": name,
                    "domain": domain,
                    "predictions": [],
                    "total_generated": 0,
                    "validation_enabled": self.check_deliverability,
                    "catch_all_detected": True,
                    "skip_reason": "Known catch-all domain"
                }

            # Generate email patterns using MailScout + custom logic
            email_patterns = self.generate_email_patterns(name, domain)

            if not email_patterns:
                return {
                    "success": False,
                    "error": "No email patterns could be generated",
                    "name": name,
                    "domain": domain,
                    "predictions": []
                }

            # Validate emails via API if enabled
            if self.check_deliverability:
                logger.info(f"Validating {len(email_patterns)} email patterns...")
                validated_results = self.validate_emails_via_api(email_patterns)

                # Check for suspicious validation patterns before catch-all detection
                if self._check_suspicious_validation_rate(validated_results, clean_domain):
                    logger.warning(f"Suspicious validation rate detected for {clean_domain}, performing catch-all test...")
                    is_catch_all = self._detect_catch_all_behavior(clean_domain)
                    if is_catch_all:
                        logger.warning(f"🚨 Catch-all detected during validation, returning empty predictions for: {clean_domain}")
                        return {
                            "success": True,
                            "name": name,
                            "domain": domain,
                            "predictions": [],
                            "total_generated": len(email_patterns),
                            "validation_enabled": self.check_deliverability,
                            "catch_all_detected": True,
                            "skip_reason": "Auto-detected catch-all domain"
                        }

                # If not known catch-all but high success rate, do additional check
                if len(validated_results) > 0:
                    valid_count = sum(1 for r in validated_results if r.get('is_valid') == True)
                    # If too many emails are valid (>60%), test for catch-all
                    if valid_count > len(validated_results) * 0.6:
                        logger.warning(f"High validation rate ({valid_count}/{len(validated_results)}), testing catch-all behavior")
                        is_catch_all = self._detect_catch_all_behavior(clean_domain)
                        if is_catch_all:
                            logger.warning(f"🚨 Catch-all detected via high success rate, returning empty predictions for: {clean_domain}")
                            return {
                                "success": True,
                                "name": name,
                                "domain": domain,
                                "predictions": [],
                                "total_generated": len(email_patterns),
                                "validation_enabled": self.check_deliverability,
                                "catch_all_detected": True,
                                "skip_reason": "Auto-detected catch-all domain (high success rate)"
                            }

                # Sort by validation results (valid emails first)
                validated_results.sort(key=lambda x: (
                    x["is_valid"] is True,
                    x["is_valid"] is not False,
                    x["email"]
                ))
            else:
                validated_results = [{"email": email, "is_valid": None}
                                   for email in email_patterns]

            return {
                "success": True,
                "name": name,
                "domain": domain,
                "predictions": validated_results,
                "total_generated": len(email_patterns),
                "validation_enabled": self.check_deliverability,
                "catch_all_detected": False
            }

        except Exception as e:
            logger.error(f"Email prediction failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "name": name,
                "domain": domain,
                "predictions": []
            }

def manage_blacklist(action: str, domain: str = None):
    """Manage catch-all domains blacklist"""
    blacklist_file = os.path.join(os.path.dirname(__file__), 'catch_all_domains.json')

    try:
        # Load existing data
        data = {}
        if os.path.exists(blacklist_file):
            with open(blacklist_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

        domains = set(data.get('catch_all_domains', []))

        if action == 'list':
            print(f"📋 Current catch-all domains ({len(domains)}):")
            for domain in sorted(domains):
                print(f"  - {domain}")

        elif action == 'add' and domain:
            if domain.lower() not in domains:
                domains.add(domain.lower())
                data['catch_all_domains'] = sorted(list(domains))
                data['last_updated'] = datetime.now().isoformat()

                with open(blacklist_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)

                print(f"✅ Added {domain} to catch-all blacklist")
            else:
                print(f"⚠️  {domain} already in blacklist")

        elif action == 'remove' and domain:
            if domain.lower() in domains:
                domains.remove(domain.lower())
                data['catch_all_domains'] = sorted(list(domains))
                data['last_updated'] = datetime.now().isoformat()

                with open(blacklist_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)

                print(f"✅ Removed {domain} from catch-all blacklist")
            else:
                print(f"⚠️  {domain} not found in blacklist")

        elif action == 'test' and domain:
            print(f"🧪 Testing {domain} for catch-all behavior...")
            predictor = EmailPredictor(check_deliverability=True)
            is_catch_all = predictor._detect_catch_all_behavior(domain)

            if is_catch_all:
                print(f"🚨 {domain} shows catch-all behavior")
            else:
                print(f"✅ {domain} appears to be normal")

    except Exception as e:
        print(f"❌ Error managing blacklist: {e}")

def main():
    """Main function for command line usage"""

    # Check if first argument is 'blacklist' for blacklist management
    if len(sys.argv) > 1 and sys.argv[1] == 'blacklist':
        if len(sys.argv) < 3:
            print("❌ Usage: python3 generate_email_predictions.py blacklist <action> [domain]")
            print("Actions: list, add, remove, test")
            sys.exit(1)

        action = sys.argv[2]
        domain = sys.argv[3] if len(sys.argv) > 3 else None

        if action in ['add', 'remove', 'test'] and not domain:
            print(f"❌ Domain required for action '{action}'")
            sys.exit(1)

        manage_blacklist(action, domain)
        return

    # Regular email prediction mode
    parser = argparse.ArgumentParser(description='Generate and validate email predictions for executives')
    parser.add_argument('name', help='Executive name (English or Japanese)')
    parser.add_argument('domain', help='Company domain')
    parser.add_argument('--no-validation', action='store_true',
                       help='Skip email validation (faster but less accurate)')
    parser.add_argument('--timeout', type=int, default=3,
                       help='Legacy timeout parameter (kept for compatibility)')
    parser.add_argument('--output', '-o', help='Output file for JSON results')
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose logging')

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Initialize predictor
    predictor = EmailPredictor(
        check_deliverability=not args.no_validation
    )

    # Generate and validate emails
    result = predictor.generate_and_validate(args.name, args.domain)

    # Output results
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print(f"Results saved to {args.output}")
    else:
        print(json.dumps(result, ensure_ascii=False, indent=2))

    # Return appropriate exit code
    sys.exit(0 if result["success"] else 1)

if __name__ == "__main__":
    main()
