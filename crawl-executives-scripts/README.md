# Kanji to Romaji Converter

A Python script that converts Japanese kanji text to romaji (Latin alphabet) using the pykakasi library.

## Installation

1. Install the required dependency:
```bash
pip install -r requirements.txt
```

Or install pykakasi directly:
```bash
pip install pykakasi
```

## Usage

### Command Line Mode (Direct conversion):
```bash
# Convert a kanji name and return only the romaji result
python kanji_to_romaji.py "田中太郎"
# Output: tanakatarou

# Convert with capitalization
python kanji_to_romaji.py -c "田中太郎"
# Output: Tanakatarou

# Convert any kanji text
python kanji_to_romaji.py "山田花子"
# Output: ya<PERSON><PERSON>ko
```

### Interactive Mode:
Run without arguments to enter interactive mode:
```bash
python kanji_to_romaji.py
```

The script will:
1. Show sample conversions of common Japanese names
2. Enter interactive mode where you can input kanji text for conversion

### Command Line Options:
- `name`: Kanji text to convert (optional)
- `-c, --capitalize`: Capitalize the romaji output
- `-h, --help`: Show help message

## Examples

```bash
# Simple conversion
python kanji_to_romaji.py "佐藤"
# Output: satou

# Multiple conversions using shell
echo "田中太郎" | xargs python kanji_to_romaji.py
# Output: tanakatarou

# Batch conversion
for name in "田中太郎" "佐藤花子" "山田一郎"; do
    python kanji_to_romaji.py "$name"
done
```

## Features

- Convert any kanji text to romaji via command line
- Option to capitalize converted text
- Batch conversion of name lists
- Interactive mode for real-time conversion
- Uses Hepburn romanization system
- Clean output suitable for scripting

## Requirements

- Python 3.6+
- pykakasi library 