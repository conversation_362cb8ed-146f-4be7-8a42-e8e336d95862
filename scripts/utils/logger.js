const winston = require('winston');
const path = require('path');

// Create logger instance
const logger = winston.createLogger({
    level: process.env.LOG_LEVEL || 'info',
    format: winston.format.combine(
        winston.format.timestamp({
            format: 'YYYY-MM-DD HH:mm:ss'
        }),
        winston.format.errors({ stack: true }),
        winston.format.json()
    ),
    defaultMeta: { service: 'crawler-script' },
    transports: [
        // Error log file
        new winston.transports.File({
            filename: path.join(__dirname, '../../storage/logs/crawler-errors.log'),
            level: 'error'
        }),
        // Combined log file
        new winston.transports.File({
            filename: path.join(__dirname, '../../storage/logs/crawler.log')
        })
    ]
});

// Add console transport for development - output to stderr to avoid interfering with JSON stdout
if (process.env.NODE_ENV !== 'production') {
    logger.add(new winston.transports.Console({
        stderrLevels: ['error', 'warn', 'info', 'debug'], // Send all levels to stderr
        format: winston.format.combine(
            winston.format.colorize(),
            winston.format.simple(),
            winston.format.printf(({ timestamp, level, message, ...meta }) => {
                return `${timestamp} [${level}]: ${message} ${Object.keys(meta).length ? JSON.stringify(meta) : ''}`;
            })
        )
    }));
}

module.exports = logger; 