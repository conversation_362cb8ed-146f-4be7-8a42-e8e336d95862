const puppeteer = require('puppeteer-extra');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');
const AdblockerPlugin = require('puppeteer-extra-plugin-adblocker');
const UserAgent = require('user-agents');
const logger = require('./logger');

// Add plugins
puppeteer.use(StealthPlugin());
puppeteer.use(AdblockerPlugin({ blockTrackers: true }));

class BrowserManager {
    constructor() {
        this.browser = null;
        this.pages = [];
        this.maxPages = 5;
        this.userAgent = new UserAgent();
        this.memoryThreshold = 800 * 1024 * 1024; // 800MB threshold
        this.lastMemoryCheck = Date.now();
        this.memoryCheckInterval = 30000; // Check every 30 seconds
        
        // Setup graceful shutdown handlers
        this.setupGracefulShutdown();
    }

    setupGracefulShutdown() {
        const signals = ['SIGINT', 'SIGTERM', 'SIGQUIT'];
        
        signals.forEach(signal => {
            process.on(signal, async () => {
                logger.info(`🛑 Received ${signal}, gracefully shutting down...`);
                try {
                    await this.close();
                } catch (error) {
                    logger.error('Error during graceful shutdown:', error.message);
                }
                process.exit(0);
            });
        });

        // Handle uncaught exceptions
        process.on('uncaughtException', async (error) => {
            logger.error('🚨 Uncaught exception:', error);
            try {
                await this.close();
            } catch (e) {
                // Ignore cleanup errors
            }
            process.exit(1);
        });

        // Handle unhandled promise rejections
        process.on('unhandledRejection', async (reason, promise) => {
            logger.error('🚨 Unhandled promise rejection:', reason);
            try {
                await this.close();
            } catch (e) {
                // Ignore cleanup errors
            }
            process.exit(1);
        });
    }

    async checkMemoryUsage() {
        const now = Date.now();
        if (now - this.lastMemoryCheck < this.memoryCheckInterval) {
            return;
        }
        
        this.lastMemoryCheck = now;
        
        try {
            const memUsage = process.memoryUsage();
            const heapUsedMB = Math.round(memUsage.heapUsed / 1024 / 1024);
            const rssMB = Math.round(memUsage.rss / 1024 / 1024);
            
            logger.debug(`📊 Memory usage: Heap ${heapUsedMB}MB, RSS ${rssMB}MB`);
            
            if (memUsage.heapUsed > this.memoryThreshold) {
                logger.warn(`⚠️ High memory usage detected: ${heapUsedMB}MB, cleaning up...`);
                await this.cleanupMemory();
            }
        } catch (error) {
            logger.debug('Error checking memory usage:', error.message);
        }
    }

    async cleanupMemory() {
        try {
            // Close extra pages
            while (this.pages.length > 1) {
                const page = this.pages.pop();
                try {
                    await page.close();
                } catch (e) {
                    // Ignore close errors
                }
            }
            
            // Force garbage collection if available
            if (global.gc) {
                global.gc();
                logger.debug('🧹 Forced garbage collection');
            }
            
            // Give some time for cleanup
            await new Promise(resolve => setTimeout(resolve, 1000));
            
        } catch (error) {
            logger.debug('Error during memory cleanup:', error.message);
        }
    }

    async getBrowser() {
        if (!this.browser) {
            await this.initBrowser();
        }
        return this.browser;
    }

    async initBrowser() {
        const maxRetries = 3;
        let lastError;

        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                logger.info(`🚀 Initializing browser (attempt ${attempt}/${maxRetries})`);
                
                // Kill any existing Chrome processes that might be hanging
                if (attempt > 1) {
                    await this.killHangingChromeProcesses();
                    await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2s
                }

                const launchOptions = {
                    headless: process.env.HEADLESS !== 'false' ? 'new' : false,
                    timeout: 60000, // Increased to 60 second timeout
                    args: [
                        '--no-sandbox',
                        '--disable-setuid-sandbox',
                        '--disable-dev-shm-usage',
                        '--disable-accelerated-2d-canvas',
                        '--no-first-run',
                        '--no-zygote',
                        '--disable-gpu',
                        '--disable-background-timer-throttling',
                        '--disable-backgrounding-occluded-windows',
                        '--disable-renderer-backgrounding',
                        '--disable-features=TranslateUI',
                        '--disable-ipc-flooding-protection',
                        '--disable-web-security',
                        '--disable-features=VizDisplayCompositor',
                        '--memory-pressure-off', // Disable memory pressure monitoring
                        '--max_old_space_size=1024', // Limit memory usage
                        '--window-size=1920,1080'
                    ],
                    defaultViewport: {
                        width: 1920,
                        height: 1080
                    }
                };

                // On macOS, try to use system Chrome if available
                if (process.platform === 'darwin') {
                    const chromePaths = [
                        '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
                        '/Applications/Chromium.app/Contents/MacOS/Chromium'
                    ];
                    
                    for (const chromePath of chromePaths) {
                        try {
                            const fs = require('fs');
                            if (fs.existsSync(chromePath)) {
                                launchOptions.executablePath = chromePath;
                                logger.info(`🔍 Using Chrome at: ${chromePath}`);
                                break;
                            }
                        } catch (e) {
                            // Continue to next path
                        }
                    }
                }

                this.browser = await puppeteer.launch(launchOptions);
                logger.info('✅ Browser initialized successfully');
                return; // Success, exit retry loop

            } catch (error) {
                lastError = error;
                logger.warn(`❌ Browser initialization attempt ${attempt} failed:`, error.message);
                
                if (attempt < maxRetries) {
                    const delay = attempt * 2000; // Increasing delay
                    logger.info(`⏳ Retrying in ${delay}ms...`);
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }
        }

        // All attempts failed
        logger.error('❌ Failed to initialize browser after all attempts:', lastError);
        throw lastError;
    }

    async killHangingChromeProcesses() {
        try {
            logger.debug('🧹 Cleaning up hanging Chrome processes');
            
            const { exec } = require('child_process');
            const { promisify } = require('util');
            const execAsync = promisify(exec);
            
            if (process.platform === 'darwin') {
                // Kill Chrome processes that might be hanging
                const commands = [
                    'pkill -f "Chrome.*--remote-debugging-port" || true',
                    'pkill -f "Chromium.*--remote-debugging-port" || true',
                    'pkill -f "Google Chrome.*--remote-debugging-port" || true',
                    'pkill -f "chrome.*--headless" || true',
                    'pkill -f "node.*crawl-executives" || true'
                ];
                
                for (const command of commands) {
                    try {
                        await execAsync(command);
                        await new Promise(resolve => setTimeout(resolve, 500)); // Wait between kills
                    } catch (e) {
                        // Ignore kill errors
                    }
                }
                
                logger.debug('✅ Process cleanup completed');
                
            } else if (process.platform === 'linux') {
                // Linux process cleanup
                const commands = [
                    'pkill -f "chrome.*--remote-debugging-port" || true',
                    'pkill -f "chromium.*--remote-debugging-port" || true',
                    'pkill -f "google-chrome.*--remote-debugging-port" || true'
                ];
                
                for (const command of commands) {
                    try {
                        await execAsync(command);
                        await new Promise(resolve => setTimeout(resolve, 500));
                    } catch (e) {
                        // Ignore kill errors
                    }
                }
            }
        } catch (error) {
            logger.debug('⚠️ Error cleaning up processes:', error.message);
        }
    }

    async getPage() {
        // Check memory usage before creating/reusing pages
        await this.checkMemoryUsage();
        
        const browser = await this.getBrowser();
        
        // Check if browser is still connected
        if (!browser.isConnected()) {
            logger.warn('🔄 Browser disconnected, reinitializing...');
            this.browser = null;
            this.pages = [];
            return this.getPage(); // Recursive call to reinitialize
        }
        
        // Reuse existing page if available
        if (this.pages.length > 0) {
            const page = this.pages.pop();
            try {
                // Test if page is still valid with timeout
                await Promise.race([
                    page.evaluate(() => window.location.href),
                    new Promise((_, reject) => 
                        setTimeout(() => reject(new Error('Page validation timeout')), 3000)
                    )
                ]);
                logger.debug('♻️ Reusing existing page');
                return page;
            } catch (error) {
                logger.debug('🗑️ Removing invalid page:', error.message);
                try {
                    await page.close();
                } catch (e) {
                    // Ignore close errors
                }
            }
        }

        // Create new page with timeout protection and retry logic
        const maxRetries = 3;
        let lastError;
        
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                logger.debug(`📄 Creating new page (attempt ${attempt}/${maxRetries})`);
                
                const page = await Promise.race([
                    browser.newPage(),
                    new Promise((_, reject) => 
                        setTimeout(() => reject(new Error('Page creation timeout after 20s')), 20000)
                    )
                ]);
                
                await this.setupPage(page);
                logger.debug('✅ Page setup completed');
                return page;
                
            } catch (error) {
                lastError = error;
                logger.warn(`❌ Page creation attempt ${attempt} failed:`, error.message);
                
                // If it's a critical error or last attempt, throw
                if (attempt === maxRetries || 
                    error.message.includes('Target.createTarget timed out') ||
                    error.message.includes('Target closed') ||
                    error.message.includes('Protocol error')) {
                    
                    logger.error('❌ Failed to create page after all attempts');
                    
                    // Try to restart browser on critical errors
                    if (error.message.includes('Target.createTarget timed out') || 
                        error.message.includes('Target closed')) {
                        logger.warn('🔄 Attempting browser restart due to critical error');
                        try {
                            await this.close();
                            this.browser = null;
                            this.pages = [];
                        } catch (e) {
                            logger.debug('Error during browser restart:', e.message);
                        }
                    }
                    
                    // Throw specific timeout error
                    const timeoutError = new Error('BROWSER_TIMEOUT: ' + error.message);
                    timeoutError.code = 'BROWSER_TIMEOUT';
                    throw timeoutError;
                }
                
                // Wait before retry
                const delay = attempt * 1000;
                logger.debug(`⏳ Retrying page creation in ${delay}ms...`);
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
        
        // This shouldn't be reached, but just in case
        throw lastError;
    }

    async setupPage(page) {
        // Set random user agent
        const userAgent = this.userAgent.random().toString();
        await page.setUserAgent(userAgent);

        // Set extra headers
        await page.setExtraHTTPHeaders({
            'Accept-Language': 'en-US,en;q=0.9,vi;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        });

        // Set viewport
        await page.setViewport({
            width: 1920 + Math.floor(Math.random() * 100),
            height: 1080 + Math.floor(Math.random() * 100)
        });

        // Block unnecessary resources for faster loading
        await page.setRequestInterception(true);
        page.on('request', (req) => {
            const resourceType = req.resourceType();
            if (resourceType === 'image' || resourceType === 'stylesheet' || resourceType === 'font') {
                req.abort();
            } else {
                req.continue();
            }
        });

        // Add stealth scripts
        await page.evaluateOnNewDocument(() => {
            // Remove webdriver property
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });

            // Mock chrome runtime
            window.chrome = {
                runtime: {}
            };

            // Mock permissions
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Notification.permission }) :
                    originalQuery(parameters)
            );

            // Mock plugins
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5]
            });

            // Mock languages
            Object.defineProperty(navigator, 'languages', {
                get: () => ['en-US', 'en']
            });
        });

        logger.debug('🔧 Page setup completed');
    }

    async releasePage(page) {
        // Check memory usage when releasing pages
        await this.checkMemoryUsage();
        
        if (this.pages.length < this.maxPages) {
            // Clear page state before reusing
            try {
                await page.evaluate(() => {
                    localStorage.clear();
                    sessionStorage.clear();
                    // Clear any potential memory leaks
                    if (window.gc) {
                        window.gc();
                    }
                });
                
                // Navigate to blank page to free resources
                await page.goto('about:blank');
                
                this.pages.push(page);
                logger.debug('♻️ Page returned to pool');
            } catch (error) {
                logger.debug('🗑️ Failed to clear page, closing instead:', error.message);
                await this.closePage(page);
            }
        } else {
            logger.debug('🗑️ Pool full, closing page');
            await this.closePage(page);
        }
    }

    async closePage(page) {
        try {
            await page.close();
            logger.debug('🗑️ Page closed');
        } catch (error) {
            logger.debug('⚠️ Error closing page:', error.message);
        }
    }

    async addDelay(min = 1000, max = 3000) {
        const delay = Math.floor(Math.random() * (max - min + 1)) + min;
        logger.debug(`⏳ Adding delay: ${delay}ms`);
        await new Promise(resolve => setTimeout(resolve, delay));
    }

    async simulateHumanBehavior(page) {
        try {
            // Random mouse movements
            await page.mouse.move(
                Math.floor(Math.random() * 1920),
                Math.floor(Math.random() * 1080)
            );

            // Random scroll
            await page.evaluate(() => {
                window.scrollBy(0, Math.floor(Math.random() * 1000));
            });

            await this.addDelay(500, 1500);
        } catch (error) {
            logger.debug('⚠️ Error in human behavior simulation:', error.message);
        }
    }

    async close() {
        if (this.browser) {
            logger.info('🔒 Closing browser');
            
            try {
                // Close all pages
                for (const page of this.pages) {
                    await this.closePage(page);
                }
                this.pages = [];

                // Close browser
                await this.browser.close();
                this.browser = null;
                
                logger.info('✅ Browser closed');
            } catch (error) {
                // Handle EPIPE or other disconnect errors gracefully
                if (error.code === 'EPIPE' || error.message.includes('EPIPE') || 
                    error.message.includes('Target closed') || 
                    error.message.includes('Session closed')) {
                    logger.warn('🔌 Browser already disconnected during close');
                    this.browser = null;
                    this.pages = [];
                } else {
                    logger.error('❌ Error closing browser:', error.message);
                    // Force null the browser reference to prevent hanging
                    this.browser = null;
                    this.pages = [];
                }
            }
        }
    }
}

module.exports = BrowserManager; 