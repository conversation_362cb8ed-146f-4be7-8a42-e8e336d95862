#!/usr/bin/env node

require('dotenv').config();
const { program } = require('commander');
const JSSoup = require('jssoup').default;
const logger = require('./utils/logger');
const BrowserManager = require('./utils/browser');
const { execSync } = require('child_process');
const path = require('path');
// Import deep-email-validator using regular require since it's a CommonJS module
const deepEmailValidatorModule = require('deep-email-validator');
const deepEmailValidator = deepEmailValidatorModule.default;

program
    .option('-u, --url <url>', 'Company info page URL to crawl')
    .option('-n, --company-name <n>', 'Company name for context')
    .option('--executive-keywords <keywords>', 'Executive keywords from PHP (comma-separated)')
    .option('--info-keywords <keywords>', 'Company info keywords from PHP (comma-separated)')
    .option('--output <o>', 'Output file path', './output/executives.json')
    .parse();

const options = program.opts();

class ExecutiveCrawler {
    constructor(options) {
        this.options = options;
        this.browserManager = new BrowserManager();
        this.executives = [];
        this.companyDetails = {};
        
        // Use keywords from PHP - keywords are required, no fallback
        if (!options.executiveKeywords || !options.infoKeywords) {
            throw new Error('Executive keywords and info keywords are required from PHP');
        }
        
        this.executiveKeywords = options.executiveKeywords.split(',').map(k => k.trim());
        this.infoKeywords = options.infoKeywords.split(',').map(k => k.trim());
            
        logger.info('ExecutiveCrawler initialized', {
            executiveKeywords: this.executiveKeywords.length,
            infoKeywords: this.infoKeywords.length
        });
    }

    async crawl() {
        try {
            logger.info('🚀 Starting executive crawl', {
                url: this.options.url,
                companyName: this.options.companyName,
                executiveKeywords: this.executiveKeywords.length,
                infoKeywords: this.infoKeywords.length
            });

            // Try to find a better info page first
            const betterInfoUrl = await this.findBetterInfoPage(this.options.url);
            const targetUrl = betterInfoUrl || this.options.url;
            
            logger.info('🎯 Using target URL', { 
                targetUrl, 
                originalUrl: this.options.url,
                foundBetterPage: !!betterInfoUrl 
            });

            const result = await this.crawlExecutives(targetUrl);
            await this.browserManager.close();

            // Write to output file
            const fs = require('fs');
            const path = require('path');
            
            const outputDir = path.dirname(this.options.output);
            if (!fs.existsSync(outputDir)) {
                fs.mkdirSync(outputDir, { recursive: true });
            }
            
            fs.writeFileSync(this.options.output, JSON.stringify(result, null, 2));

            logger.info('✅ Executive crawl completed', {
                executives_found: result.executives.length,
                output_file: this.options.output
            });

            // Output to stdout for PHP to read
            console.log(JSON.stringify(result));
            
            return result;

        } catch (error) {
            // Check if it's a browser timeout error
            if (error.code === 'BROWSER_TIMEOUT' || 
                error.message.includes('Target.createTarget timed out') ||
                error.message.includes('protocolTimeout') ||
                error.message.includes('timeout') ||
                error.message.includes('Target closed') ||
                error.message.includes('Page crashed') ||
                error.message.includes('Navigation timeout') ||
                error.message.includes('Connection closed') ||
                error.message.includes('Protocol error')) {
                
                logger.error('❌ Browser timeout/error detected - skipping job to prevent queue failure:', error.message);
                
                // Return a graceful skip result instead of crashing
                const skipResult = {
                    success: false,
                    skipped: true,
                    skip_reason: 'BROWSER_TIMEOUT',
                    error: 'Browser timeout/error - job skipped to prevent queue failure',
                    url: this.options.url,
                    company_name: this.options.companyName,
                    executives: [],
                    company_details: {},
                    timestamp: new Date().toISOString(),
                    error_details: error.message
                };

                console.log(JSON.stringify(skipResult));
                process.exit(0); // Exit with success to prevent queue from retrying
            }
            
            logger.error('❌ Executive crawl failed:', error);
            
            const errorResult = {
                success: false,
                error: error.message,
                executives: this.executives,
                company_details: this.companyDetails,
                url: this.options.url,
                company_name: this.options.companyName,
                timestamp: new Date().toISOString()
            };

            console.log(JSON.stringify(errorResult));
            process.exit(1);
        }
    }

    async findBetterInfoPage(baseUrl) {
        let page;
        try {
            page = await this.browserManager.getPage();
        } catch (error) {
            if (error.code === 'BROWSER_TIMEOUT') {
                logger.warn('⚠️ Browser timeout when getting page for info search - skipping');
                return null;
            }
            throw error;
        }
        
        try {
            logger.debug('🔍 Looking for better info page');
            
            // Navigate with timeout protection (reduced timeouts)
            await Promise.race([
                page.goto(baseUrl, {
                    waitUntil: 'networkidle2',
                    timeout: 15000
                }),
                new Promise((_, reject) => 
                    setTimeout(() => reject(new Error('Info page navigation timeout after 15s')), 15000)
                )
            ]);

            await Promise.race([
                page.waitForSelector('body', { timeout: 5000 }),
                new Promise((_, reject) => 
                    setTimeout(() => reject(new Error('Body selector timeout during info search')), 5000)
                )
            ]);

            // First, check if the current page already contains executive information
            const currentPageHasExecutives = await page.evaluate((executiveKeywords) => {
                const text = document.body.textContent.toLowerCase();
                let executiveCount = 0;
                
                // Count potential executives on current page
                executiveKeywords.forEach(keyword => {
                    const regex = new RegExp(keyword.toLowerCase(), 'g');
                    const matches = text.match(regex);
                    if (matches) {
                        executiveCount += matches.length;
                    }
                });
                
                // If we find multiple executive keywords or executive-specific elements, stay on current page
                const hasExecutiveTables = document.querySelectorAll('table').length > 0 &&
                                         document.body.textContent.includes('取締役') ||
                                         document.body.textContent.includes('役員');
                
                return executiveCount >= 3 || hasExecutiveTables;
            }, this.executiveKeywords);

            if (currentPageHasExecutives) {
                logger.info('✅ Current page already contains executive information - staying here', {
                    url: baseUrl
                });
                return null;
            }

            // Look for links that contain info keywords
            const infoLinks = await page.evaluate((infoKeywords, executiveKeywords) => {
                const links = Array.from(document.querySelectorAll('a[href]'));
                const foundLinks = [];

                links.forEach(link => {
                    const href = link.getAttribute('href');
                    const text = link.textContent.toLowerCase().trim();
                    
                    if (href && (href.startsWith('/') || href.includes(window.location.hostname))) {
                        // Skip recruitment/contact pages that don't contain executive info
                        const isRecruitmentOrContact = /(recruit|採用|contact|お問い合わせ|inquiry|form|application|apply)/i.test(href) ||
                                                     /(recruit|採用|contact|お問い合わせ|inquiry|form|application|apply)/i.test(text);
                        
                        if (isRecruitmentOrContact) {
                            return; // Skip these pages
                        }
                        
                        // Check if link text or href contains any info keyword
                        const matchesInfoKeyword = infoKeywords.some(keyword => 
                            text.includes(keyword.toLowerCase()) || 
                            href.toLowerCase().includes(keyword.toLowerCase())
                        );
                        
                        // Prioritize pages that might contain executive info
                        const hasExecutiveHints = executiveKeywords.some(keyword =>
                            text.includes(keyword.toLowerCase()) || 
                            href.toLowerCase().includes(keyword.toLowerCase())
                        ) || /(役員|executive|management|leadership|board|officer)/i.test(text) ||
                             /(役員|executive|management|leadership|board|officer)/i.test(href);
                        
                        if (matchesInfoKeyword || hasExecutiveHints) {
                            foundLinks.push({
                                href: href,
                                text: text,
                                fullUrl: href.startsWith('/') ? 
                                    window.location.origin + href : href,
                                hasExecutiveHints: hasExecutiveHints,
                                priority: hasExecutiveHints ? 1 : 2 // Lower number = higher priority
                            });
                        }
                    }
                });

                // Sort by priority (executive hints first)
                return foundLinks.sort((a, b) => a.priority - b.priority);
            }, this.infoKeywords, this.executiveKeywords);

            if (infoLinks.length > 0) {
                // Choose the highest priority link (executive-focused pages first)
                const bestLink = infoLinks[0];

                logger.info('✅ Found better info page', {
                    url: bestLink.fullUrl,
                    text: bestLink.text,
                    hasExecutiveHints: bestLink.hasExecutiveHints,
                    priority: bestLink.priority,
                    totalOptions: infoLinks.length
                });

                return bestLink.fullUrl;
            }

            logger.debug('ℹ️ No better info page found, using original URL');
            return null;

        } catch (error) {
            logger.warn('⚠️ Error finding better info page:', error.message);
            return null;
        } finally {
            if (page) {
                await this.browserManager.releasePage(page);
            }
        }
    }

    async crawlExecutives(url = this.options.url) {
        let page;
        const maxRetries = 2;
        let lastError;
        
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                logger.debug(`🌐 Loading company page (attempt ${attempt}/${maxRetries}): ${url}`);
                
                try {
                    page = await this.browserManager.getPage();
                } catch (error) {
                    if (error.code === 'BROWSER_TIMEOUT') {
                        logger.error('❌ Browser timeout when getting page for crawling');
                        throw error; // Let main catch block handle timeout
                    }
                    throw error;
                }
                
                try {
                    // Navigate with timeout protection (reduced timeouts)
                    await Promise.race([
                        page.goto(url, {
                            waitUntil: 'networkidle2',
                            timeout: 18000
                        }),
                        new Promise((_, reject) => 
                            setTimeout(() => reject(new Error('Page navigation timeout after 18s')), 18000)
                        )
                    ]);

                    // Wait for body with timeout
                    await Promise.race([
                        page.waitForSelector('body', { timeout: 6000 }),
                        new Promise((_, reject) => 
                            setTimeout(() => reject(new Error('Body selector timeout after 6s')), 6000)
                        )
                    ]);
                    
                    await this.browserManager.addDelay(1000, 2000);
                    await this.browserManager.simulateHumanBehavior(page);

                    const content = await page.content();
                    await this.extractFromHtml(content);

                    return {
                        success: true,
                        url: url,
                        original_url: this.options.url,
                        company_name: this.options.companyName,
                        executives: this.executives,
                        company_details: this.companyDetails,
                        extraction_method: 'jsoup_regex_enhanced',
                        timestamp: new Date().toISOString(),
                        attempt: attempt
                    };

                } catch (error) {
                    lastError = error;
                    
                    // Check if it's a timeout error that we can retry
                    if ((error.message.includes('timeout') || 
                         error.message.includes('Navigation timeout') ||
                         error.message.includes('waiting for selector')) && 
                        attempt < maxRetries) {
                        
                        logger.warn(`⚠️ Page loading timeout on attempt ${attempt}, retrying...`, error.message);
                        
                        // Wait before retry
                        await new Promise(resolve => setTimeout(resolve, 2000));
                        continue;
                    }
                    
                    // For non-timeout errors or final attempt, throw
                    throw error;
                }

            } catch (error) {
                lastError = error;
                logger.error(`❌ Error crawling executives (attempt ${attempt}):`, error.message);
                
                // If it's the final attempt or a critical error, throw
                if (attempt === maxRetries || 
                    error.code === 'BROWSER_TIMEOUT' ||
                    error.message.includes('Target.createTarget timed out')) {
                    throw error;
                }
                
                // Wait before retry
                await new Promise(resolve => setTimeout(resolve, 3000));
                
            } finally {
                if (page) {
                    try {
                        await this.browserManager.releasePage(page);
                    } catch (e) {
                        logger.debug('Error releasing page:', e.message);
                    }
                    page = null; // Reset for next attempt
                }
            }
        }
        
        // This shouldn't be reached, but just in case
        throw lastError;
    }

    async extractFromHtml(html) {
        // Reset executives and company details for fresh extraction
        this.executives = [];
        this.companyDetails = {};
        
        // Simplified extraction approach - only jssoup text conversion + regex matching
        logger.info('🔍 Starting simplified jssoup + regex executive extraction');
        
        // Convert HTML to clean text using jssoup
        const plainText = this.convertHtmlToTextWithJsoup(html);
        
        // Extract executives using regex patterns with database keywords
        this.extractExecutivesWithRegexKeywords(plainText);

        // Extract company details from HTML
        this.extractCompanyDetailsFromHtml(html);
        
        // FINAL VALIDATION: Ensure ALL executives have valid keywords and names
        this.executives = this.executives.filter(exec => {
            const hasValidKeyword = this.containsExecutiveKeyword(exec.position);
            const hasValidName = this.isValidExecutiveName(exec.name);
            
            // Additional validation: position should not be empty or too generic
            const positionIsValid = exec.position && 
                                   exec.position.trim().length > 1 && 
                                   exec.position.trim().length < 50 &&
                                   !exec.position.includes('※') && // Remove footnote markers
                                   !exec.position.includes('(2025年') && // Remove date references
                                   !exec.position.includes('現在'); // Remove "current" markers
            
            const isValid = hasValidKeyword && hasValidName && positionIsValid;
            
            if (!isValid) {
                logger.warn(`🚫 Filtered out invalid executive:`, {
                    name: exec.name,
                    position: exec.position,
                    hasValidKeyword,
                    hasValidName,
                    positionIsValid,
                    extraction_method: exec.extraction_method
                });
            }
            
            return isValid;
        });
        
        // Remove duplicates and enhance with metadata
        this.executives = this.removeDuplicateExecutives(this.executives);
        this.executives = await this.enhanceExecutivesWithEmailPredictions();
        this.executives = this.executives.slice(0, 15); // Limit results
    }

    /**
     * Convert HTML to clean text using jssoup library
     * Remove scripts, styles, and other non-content elements
     * Preserve line breaks for better text structure
     */
    convertHtmlToTextWithJsoup(html) {
        // Create soup from HTML string
        const soup = new JSSoup(html);
        
        // Remove unwanted elements that add noise
        const unwantedElements = [
            'script', 'style', 'noscript', 'nav', 'header', 'footer', 
            'aside', 'iframe', 'form', 'input', 'button'
        ];
        
        unwantedElements.forEach(tagName => {
            const elements = soup.findAll(tagName);
            elements.forEach(element => {
                try {
                    element.extract();
                } catch (e) {
                    // Ignore extraction errors
                }
            });
        });
        
        // Remove elements by common class names
        const unwantedClasses = ['menu', 'navigation', 'sidebar', 'ads', 'advertisement', 'nav'];
        unwantedClasses.forEach(className => {
            try {
                // Find by class attribute
                const elements = soup.findAll('div', {'class': className}) || [];
                elements.forEach(element => {
                    try {
                        element.extract();
                    } catch (e) {
                        // Ignore extraction errors
                    }
                });
            } catch (e) {
                // Ignore if class search fails
            }
        });
        
        // Special handling for table structures to preserve executive info
        let tableText = '';
        try {
            const tables = soup.findAll('table');
            tables.forEach((table, tableIndex) => {
                try {
                    const rows = table.findAll('tr');
                    rows.forEach(row => {
                        const cells = row.findAll(['td', 'th']);
                        if (cells && cells.length >= 2) {
                            const cellTexts = cells.map(cell => {
                                try {
                                    return cell.getText().trim();
                                } catch (e) {
                                    return '';
                                }
                            }).filter(text => text.length > 0);
                            
                            // Check if this looks like executive info 
                            if (cellTexts.length >= 2) {
                                const firstCell = cellTexts[0];
                                const secondCell = cellTexts[1];
                                
                                // CASE 1: First cell contains executive keyword, second contains name
                                const firstCellHasKeyword = this.executiveKeywords.some(keyword => 
                                    firstCell.toLowerCase().includes(keyword.toLowerCase())
                                );
                                
                                // CASE 2: Second cell contains both executive keyword AND name (like BREXA)
                                const secondCellHasKeyword = this.executiveKeywords.some(keyword => 
                                    secondCell.toLowerCase().includes(keyword.toLowerCase())
                                );
                                
                                if (firstCellHasKeyword && secondCell.length >= 2 && secondCell.length <= 50) {
                                    // Standard case: "代表者" -> "山﨑 高之"
                                    tableText += `${firstCell}: ${secondCell}\n`;
                                    logger.debug(`🏠 Found table executive info (standard): "${firstCell}" -> "${secondCell}"`);
                                } else if (secondCellHasKeyword && secondCell.length >= 4 && secondCell.length <= 50) {
                                    // BREXA case: "代表者" -> "代表取締役社長 山﨑 高之"
                                    // Extract both position and name from second cell
                                    tableText += `${firstCell}: ${secondCell}\n`;
                                    logger.debug(`🏠 Found table executive info (compound): "${firstCell}" -> "${secondCell}"`);
                                }
                            }
                        }
                    });
                } catch (e) {
                    // Ignore table processing errors
                }
            });
        } catch (e) {
            logger.warn('Failed to process tables:', e.message);
        }
        
        // Get text content using jssoup's text extraction
        let text = '';
        try {
            // Try to get text from body first
            const body = soup.find('body');
            if (body) {
                text = body.getText('\n'); // Use newline as separator
            } else {
                // Fallback to entire document
                text = soup.getText('\n');
            }
        } catch (e) {
            // Final fallback - try to get string content
            try {
                text = soup.string || '';
            } catch (e2) {
                logger.warn('Failed to extract text from HTML:', e2.message);
                text = '';
            }
        }
        
        // Prepend table text for better processing
        if (tableText) {
            text = tableText + '\n' + text;
        }
        
        // Clean up the text - preserve line breaks for better pattern matching
        text = text
            .replace(/\s\s+/g, ' ')         // Multiple spaces to single space (preserve single spaces)
            .replace(/\n\s*\n/g, '\n')      // Multiple newlines to single newline
            .replace(/^\s+|\s+$/g, '')      // Trim start and end
            .split('\n')                    // Split into lines
            .map(line => line.trim())       // Trim each line
            .filter(line => line.length > 0) // Remove empty lines
            .join('\n');                    // Rejoin with newlines
        
        logger.debug('📄 Converted HTML to text using jssoup', {
            text_length: text.length,
            lines_count: text.split('\n').length,
            table_extractions: tableText ? tableText.split('\n').length - 1 : 0,
            text_preview: text.substring(0, 200) + '...'
        });
        
        return text;
    }

    /**
     * Extract executives using regex patterns with database keywords
     * Simple approach: find patterns where keywords appear near names
     */
    extractExecutivesWithRegexKeywords(text) {
        logger.debug('🔍 Extracting executives using regex + database keywords');
        
        // Split text into lines for easier processing
        const lines = text.split('\n').filter(line => line.trim().length > 5);
        
        lines.forEach((line, lineIndex) => {
            // Skip very long lines (likely not executive info)
            if (line.length > 1000) return;
            
            // For each executive keyword from database
            this.executiveKeywords.forEach(keyword => {
                const keywordLower = keyword.toLowerCase();
                const lineLower = line.toLowerCase();
                
                // Check if line contains this keyword
                if (lineLower.includes(keywordLower)) {
                    logger.debug(`🎯 Found keyword "${keyword}" in line: ${line.substring(0, 100)}...`);
                    
                    // Extract potential executives from this line
                    const executives = this.extractExecutivesFromLine(line, keyword);
                    
                    executives.forEach(exec => {
                        this.executives.push({
                            name: this.cleanName(exec.name),
                            position: exec.position,
                            level: this.determineExecutiveLevel(exec.position),
                            extraction_method: 'jsoup_regex_keyword',
                            confidence_score: 0.7,
                            source_line: line.substring(0, 150),
                            line_number: lineIndex + 1
                        });
                    });
                }
            });
        });
        
        logger.info(`📊 Extracted ${this.executives.length} potential executives using jsoup + regex`);
    }

    /**
     * Extract executives from a single line containing a keyword
     * Use various regex patterns to identify name-position pairs
     */
    extractExecutivesFromLine(line, keyword) {
        const executives = [];
        const cleanLine = line.trim();
        
        // Pattern 1: Position followed by name (Japanese style)
        // Example: "代表取締役社長 田中太郎", "CEO John Smith"  
        // Updated to handle full-width spaces and extended Japanese character ranges
        const positionNamePatterns = [
            // Japanese kanji names with spaces (植草 学) - including full-width space
            new RegExp(`(${this.escapeRegex(keyword)}[^\\s]*?)[\\s　]+([\\u4e00-\\u9fff\\uf900-\\ufaff]{2,4}[\\s　]+[\\u4e00-\\u9fff\\uf900-\\ufaff]{1,4})`, 'gi'),
            // Japanese kanji names without spaces (田中太郎) - extended range for variants
            new RegExp(`(${this.escapeRegex(keyword)}[^\\s]*?)[\\s　]+([\\u4e00-\\u9fff\\uf900-\\ufaff]{2,12})`, 'gi'),
            // Western names (First Last or First Middle Last)
            new RegExp(`(${this.escapeRegex(keyword)}[^\\s]*?)\\s+([A-Z][a-z]+(?:\\s+[A-Z][a-z]+){1,2})`, 'gi'),
        ];
        
        // Pattern 2: Name followed by position
        // Example: "田中太郎 代表取締役社長", "John Smith CEO"
        // Updated to handle full-width spaces and extended Japanese character ranges
        const namePositionPatterns = [
            // Japanese kanji names with spaces followed by position (植草 学 代表取締役)
            new RegExp(`([\\u4e00-\\u9fff\\uf900-\\ufaff]{2,4}[\\s　]+[\\u4e00-\\u9fff\\uf900-\\ufaff]{1,4})[\\s　]+(${this.escapeRegex(keyword)}[^\\s]*)`, 'gi'),
            // Japanese kanji names without spaces followed by position - extended range
            new RegExp(`([\\u4e00-\\u9fff\\uf900-\\ufaff]{2,12})[\\s　]+(${this.escapeRegex(keyword)}[^\\s]*)`, 'gi'),
            // Western names followed by position
            new RegExp(`([A-Z][a-z]+(?:\\s+[A-Z][a-z]+){1,2})\\s+(${this.escapeRegex(keyword)}[^\\s]*)`, 'gi'),
        ];
        
        // Pattern 3: Colon or tab separated
        // Example: "代表取締役社長: 田中太郎", "CEO\tJohn Smith"
        // Updated to handle longer compound strings like "代表取締役社長　山﨑 高之" with extended Unicode ranges
        const separatorPatterns = [
            new RegExp(`(${this.escapeRegex(keyword)}[^:：\\t]*?)[:：\\t]\\s*([\\u4e00-\\u9fff\\uf900-\\ufaff]{2,4}[\\s　]+[\\u4e00-\\u9fff\\uf900-\\ufaff]{1,4}|[\\u4e00-\\u9fff\\uf900-\\ufaff\\s　]{2,25}|[A-Z][a-z]+(?:\\s+[A-Z][a-z]+){1,2})`, 'gi'),
            new RegExp(`([\\u4e00-\\u9fff\\uf900-\\ufaff]{2,4}[\\s　]+[\\u4e00-\\u9fff\\uf900-\\ufaff]{1,4}|[\\u4e00-\\u9fff\\uf900-\\ufaff\\s　]{2,25}|[A-Z][a-z]+(?:\\s+[A-Z][a-z]+){1,2})[:：\\t]\\s*(${this.escapeRegex(keyword)}[^\\s]*)`, 'gi')
        ];
        
        // Apply all patterns
        const allPatterns = [...positionNamePatterns, ...namePositionPatterns, ...separatorPatterns];
        
        allPatterns.forEach(pattern => {
            let match;
            while ((match = pattern.exec(cleanLine)) !== null) {
                const part1 = match[1]?.trim();
                const part2 = match[2]?.trim();
                
                if (!part1 || !part2) continue;
                
                let name = null;
                let position = null;
                
                // Determine which part is name and which is position
                if (this.containsExecutiveKeyword(part1) && !this.containsExecutiveKeyword(part2)) {
                    position = part1;
                    name = part2;
                } else if (this.containsExecutiveKeyword(part2) && !this.containsExecutiveKeyword(part1)) {
                    position = part2;
                    name = part1;
                }
                
                // Clean up name to extract actual person name from compound strings
                if (name) {
                    // Remove common suffixes that shouldn't be part of names
                    name = name.replace(/[　\s]*(会社名|等|他|など|氏|様|さん|殿)[　\s]*.*$/g, '').trim();
                    
                    // Extract person name from position+name compounds using existing logic
                    const positionPrefixPatterns = [
                        /^(代表取締役社長|代表取締役|取締役社長|専務取締役|常務取締役|執行役員|取締役|社長|専務|常務|監査役|役員|会長|副社長)[　\s]+/,
                        /^(CEO|CTO|CFO|COO|President|Chairman|Director|Manager|Executive|Officer)[　\s]+/i,
                    ];
                    
                                            for (const pattern of positionPrefixPatterns) {
                            if (pattern.test(name)) {
                                const extractedName = name.replace(pattern, '').trim();
                                if (extractedName.length > 0) {
                                    name = extractedName;
                                    break;
                                }
                            }
                        }
                }

                // Validate the extracted data
                if (name && position && 
                    this.isValidExecutiveName(name) && 
                    this.containsExecutiveKeyword(position) &&
                    name.length >= 2 && name.length <= 30) {
                    
                    executives.push({
                        name: name,
                        position: position
                    });
                    
                    logger.debug(`✅ Extracted: "${name}" - "${position}" from pattern match`);
                }
            }
        });
        
        return executives;
    }

    /**
     * Escape special regex characters in a string
     */
    escapeRegex(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }

    extractCompanyDetailsFromHtml(html) {
        const details = {};

        // Extract domain from current URL (if available)
        if (this.options.url) {
            try {
                const url = new URL(this.options.url);
                details.domain = url.hostname.replace(/^www\./, '');
            } catch (error) {
                logger.warn('Failed to extract domain from URL:', this.options.url);
            }
        }

        // Use jssoup to extract company details
        try {
            const soup = new JSSoup(html);
            
            // Try to find email addresses
            const emailLinks = soup.findAll('a');
            emailLinks.forEach(link => {
                try {
                    const href = link.attrs && link.attrs.href;
                    if (href && href.startsWith('mailto:')) {
                        details.email = href.replace('mailto:', '');
                    }
                } catch (e) {
                    // Ignore errors
                }
            });
            
            // Try to find phone numbers
            const phoneLinks = soup.findAll('a');
            phoneLinks.forEach(link => {
                try {
                    const href = link.attrs && link.attrs.href;
                    if (href && href.startsWith('tel:')) {
                        details.phone = href.replace('tel:', '');
                    }
                } catch (e) {
                    // Ignore errors
                }
            });
            
        } catch (error) {
            logger.warn('Failed to extract company details from HTML:', error.message);
        }

        this.companyDetails = details;
    }

    containsExecutiveKeyword(text) {
        if (!text || text.trim().length === 0) {
            return false;
        }
        
        const lowerText = text.toLowerCase();
        const hasKeyword = this.executiveKeywords.some(keyword => 
            lowerText.includes(keyword.toLowerCase())
        );
        
        // Optional: Log for debugging which keyword matched
        if (hasKeyword) {
            const matchedKeyword = this.executiveKeywords.find(keyword => 
                lowerText.includes(keyword.toLowerCase())
            );
            logger.debug(`✅ Position "${text}" matches keyword: "${matchedKeyword}"`);
        }
        
        return hasKeyword;
    }

    isValidExecutiveName(name) {
        if (!name || typeof name !== 'string') return false;
        
        const cleanedName = name.trim();
        
        // Check length - should be reasonable for a person's name
        if (cleanedName.length < 2 || cleanedName.length > 30) return false;
        
        // Check if it's not an executive label or common business term
        if (this.isExecutiveLabel(cleanedName)) return false;
        
        // ADDITIONAL: Check if it's a position/title being misidentified as a name
        const executivePositionPatterns = [
            // Japanese executive positions (should never be names by themselves)
            /^(代表取締役|取締役|執行役員|社長|専務|常務|監査役|役員|会長|副社長|部長|課長|所長|支店長|工場長)$/,
            /^(議長|会議長|委員長|理事長|理事|監事|顧問|相談役|名誉|最高|経営|管理|営業|技術|開発|企画|人事)$/,
            /^(総務|財務|法務|広報|情報|システム|品質|安全|環境|購買|製造|生産|販売|マーケティング|研究)$/,
            /^(責任者|担当者|主任|係長|チーフ|リーダー|マネージャー|ディレクター|ゼネラル|エグゼクティブ)$/,
            
            // English executive positions
            /^(CEO|CTO|CFO|COO|President|Chairman|Director|Manager|Executive|Officer)$/i,
            /^(Chief|Senior|Junior|Vice|Assistant|Associate|General|Regional|Global)$/i,
            
            // Generic titles
            /^(Mr|Ms|Mrs|Dr|Prof|Professor|Doctor)\.?$/i,
        ];
        
        const isJustExecutivePosition = executivePositionPatterns.some(pattern => pattern.test(cleanedName));
        if (isJustExecutivePosition) {
            logger.debug(`🚫 Filtered standalone executive position as name: "${cleanedName}"`);
            return false;
        }
        
        // NEW: Handle compound strings like "代表取締役社長 山﨑 高之" 
        // Extract actual name from position+name compounds
        const positionPrefixPatterns = [
            /^(代表取締役社長|代表取締役|取締役社長|専務取締役|常務取締役|執行役員|取締役|社長|専務|常務|監査役|役員|会長|副社長)\s+/,
            /^(CEO|CTO|CFO|COO|President|Chairman|Director|Manager|Executive|Officer)\s+/i,
        ];
        
        let extractedName = cleanedName;
        for (const pattern of positionPrefixPatterns) {
            if (pattern.test(cleanedName)) {
                extractedName = cleanedName.replace(pattern, '').trim();
                logger.debug(`📝 Extracted name "${extractedName}" from compound string "${cleanedName}"`);
                break;
            }
        }
        
        // If we extracted a name from a compound, validate the extracted part
        if (extractedName !== cleanedName && extractedName.length > 0) {
            // Recursively validate the extracted name part
            return this.isValidExecutiveName(extractedName);
        }
        
        // More strict filtering for non-name content
        const invalidPatterns = [
            // Japanese company/business terms
            /会社概要|企業情報|従業員数|役員|会社情報|事業内容|アクセス|お問い合わせ|お知らせ|採用情報/,
            /サービス|システム|ホームページ|ウェブ|サイト|メニュー|ナビゲーション|リンク/,
            /プライバシー|個人情報|著作権|コピーライト|サイトマップ|利用規約/,
            /株式会社|有限会社|合同会社|一般財団法人|協会|組合|グループ/,
            /について|挨拶いたします|ご紹介|新任|当社|弊社|皆様|お客様|業務|技術|開発/,
            /現在|年月|年度|時点|更新|最終|今後|今年|来年|未来|目標|実現/,
            /含む|以上|以下|など|その他|および|または|かつ|ただし|なお|また/,
            
            // English company/business terms  
            /Company|Corporation|Inc\.|Ltd\.|LLC|Group|Association|Foundation/,
            /About|Contact|Service|System|Website|Menu|Navigation|Privacy|Copyright/,
            /Employee|Staff|Member|Team|Department|Division|Office|Headquarters/,
            /Information|Details|Overview|Profile|History|News|Update|Current/,
            
            // Numbers and dates
            /^\d+$|^\d+年|^\d+月|^\d+日|^\d+名|^\d+人/,
            /\d{4}年|\d{1,2}月|\d{1,2}日/,
            
            // Common non-name patterns
            /toggle|menu|navigation|button|link|click|page|site/i,
            /TEL|FAX|Email|Address|Phone|URL|HTTP|WWW/i,
            /^[0-9\-\(\)\s]+$/, // Only numbers, dashes, parentheses, spaces
            /^[\.・\s\-_,，。、]+$/, // Only punctuation
            
            // HTML/XML remnants
            /<[^>]*>|&[a-zA-Z]+;|&\#\d+;/,
            
            // Very short single characters or symbols
            /^[a-zA-Z]$|^[^\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF]$/,
        ];
        
        const hasInvalidPattern = invalidPatterns.some(pattern => pattern.test(cleanedName));
        if (hasInvalidPattern) return false;
        
        // Check for too many non-name characters
        const hasExcessiveNonNameChars = /[:\(\)\[\]{}\/\\@#$%^&*+=|<>?`~;]/.test(cleanedName);
        if (hasExcessiveNonNameChars) return false;
        
        // Check if it looks like a sentence or description (too many words)
        const words = cleanedName.split(/\s+/);
        if (words.length > 4) return false; // More strict: max 4 words for a name
        
        // Japanese name validation: should contain primarily name characters
        // Extended Unicode ranges to include Japanese name variants
        const japaneseNamePattern = /[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FFF\uF900-\uFAFF]/;
        const englishNamePattern = /^[a-zA-Z\s\.\-\']+$/;
        
        if (japaneseNamePattern.test(cleanedName)) {
            // For Japanese names, ensure it's not mixed with too much other content
            // Extended character count to include name variants
            const nameCharCount = (cleanedName.match(/[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FFF\uF900-\uFAFF\s　]/g) || []).length;
            const totalCharCount = cleanedName.length;
            
            // At least 70% of characters should be Japanese name characters (including spaces)
            if (nameCharCount / totalCharCount < 0.7) {
                return false;
            }
            
            // Should be reasonable length for Japanese name (2-12 characters with spaces)
            if (nameCharCount < 2 || nameCharCount > 12) {
                return false;
            }
        } else if (englishNamePattern.test(cleanedName)) {
            // English names are okay if they match the pattern
            return true;
        } else {
            // Mixed or other scripts - be more conservative
            return false;
        }
        
        return true;
    }

    normalizePosition(position) {
        if (!position) return '';
        return position.toLowerCase().replace(/[:：\s]+$/, '').trim();
    }

    cleanName(name) {
        return name.replace(/[\r\n\t]+/g, ' ').replace(/\s+/g, ' ').trim();
    }

    cleanText(text) {
        return text.replace(/[\r\n\t]+/g, ' ').replace(/\s+/g, ' ').trim();
    }

    removeDuplicateExecutives(executives) {
        const nameGroups = new Map();
        
        // Group executives by base name to handle cases like "関口" vs "関口 雅之"
        executives.forEach(exec => {
            const cleanName = exec.name.toLowerCase().replace(/\s+/g, '');
            const baseName = cleanName.split(/[一-龯]{1,2}/)[0] || cleanName.substring(0, 2); // First 2 chars for base matching
            
            if (!nameGroups.has(baseName)) {
                nameGroups.set(baseName, []);
            }
            nameGroups.get(baseName).push(exec);
        });
        
        const uniqueExecutives = [];
        
        // For each name group, prefer the most specific executive position
        nameGroups.forEach((execs, baseName) => {
            if (execs.length === 1) {
                uniqueExecutives.push(execs[0]);
            } else {
                // Sort by position specificity first, then name length, then confidence
                const best = execs.sort((a, b) => {
                    // Get position specificity scores
                    const specificityA = this.getPositionSpecificity(a.position);
                    const specificityB = this.getPositionSpecificity(b.position);
                    
                    // Prioritize more specific positions (higher score)
                    const specificityDiff = specificityB - specificityA;
                    if (specificityDiff !== 0) return specificityDiff;
                    
                    // If same specificity, prefer longer names (more complete)
                    const lengthDiff = b.name.length - a.name.length;
                    if (lengthDiff !== 0) return lengthDiff;
                    
                    // Finally, prefer higher confidence
                    return (b.confidence_score || 0.5) - (a.confidence_score || 0.5);
                })[0];
                
                uniqueExecutives.push(best);
                
                // Log which position was chosen and why
                const positions = execs.map(e => e.position).join(', ');
                logger.debug(`🔄 Kept best executive: "${best.name}" (${best.position}) over ${execs.length - 1} duplicates (${positions})`);
            }
        });
        
        logger.info(`🧹 Processed ${executives.length} executives into ${uniqueExecutives.length} unique entries`);
        return uniqueExecutives;
    }

    /**
     * Get position specificity score - higher scores for more specific positions
     */
    getPositionSpecificity(position) {
        if (!position) return 0;
        
        const cleanPosition = position.toLowerCase().replace(/[:：\s]+/g, '');
        
        // Very specific positions (highest priority)
        if (cleanPosition.includes('代表取締役社長') || cleanPosition.includes('代表取締役ceo')) return 100;
        if (cleanPosition.includes('代表取締役')) return 90;
        if (cleanPosition.includes('取締役社長')) return 85;
        if (cleanPosition.includes('専務取締役')) return 80;
        if (cleanPosition.includes('常務取締役')) return 75;
        if (cleanPosition.includes('執行役員')) return 70;
        if (cleanPosition.includes('取締役')) return 65;
        
        // C-level positions
        if (cleanPosition.includes('最高経営責任者') || cleanPosition.includes('ceo')) return 85;
        if (cleanPosition.includes('最高執行責任者') || cleanPosition.includes('coo')) return 80;
        if (cleanPosition.includes('最高財務責任者') || cleanPosition.includes('cfo')) return 80;
        if (cleanPosition.includes('最高技術責任者') || cleanPosition.includes('cto')) return 80;
        if (cleanPosition.includes('最高情報責任者') || cleanPosition.includes('cio')) return 80;
        
        // General positions (lower priority)
        if (cleanPosition.includes('社長') || cleanPosition.includes('president')) return 60;
        if (cleanPosition.includes('会長') || cleanPosition.includes('chairman')) return 55;
        if (cleanPosition.includes('副社長') || cleanPosition.includes('vicepresident')) return 50;
        if (cleanPosition.includes('代表者') || cleanPosition.includes('representative')) return 30; // Generic representative
        if (cleanPosition.includes('代表') || cleanPosition.includes('representative')) return 25; // Very generic
        
        // Default for any position
        return 10;
    }

    /**
     * Validate emails using deep-email-validator for additional verification
     * This adds another layer of validation without SMTP
     */
    async validateEmailsWithDeepValidator(emails, executiveName) {
        if (!emails || emails.length === 0) {
            return [];
        }

        try {
            logger.info(`🔍 Running deep email validation for "${executiveName}" on ${emails.length} emails`);
            
            const validatedEmails = [];
            
            // Process emails one by one to avoid overwhelming the validator
            for (const email of emails) {
                try {
                    // Apply deep email validation with comprehensive checks (NO SMTP)
                    const result = await deepEmailValidator({
                        email: email,
                        validateRegex: true,        // Basic email format validation
                        validateMx: true,           // Check if MX record exists
                        validateTypo: true,         // Check for common typos
                        validateDisposable: true,   // Check if disposable email
                        validateSMTP: false,        // Skip SMTP validation
                    });
                    
                    // Only keep emails that pass all validations
                    if (result.valid) {
                        validatedEmails.push(email);
                        logger.debug(`✅ Deep validation passed: ${email}`);
                    } else {
                        logger.debug(`❌ Deep validation failed: ${email} (reason: ${result.reason})`);
                    }
                    
                } catch (error) {
                    // If validation fails, skip this email but continue with others
                    logger.warn(`⚠️ Deep validation error for ${email}: ${error.message}`);
                }
                
                // Small delay to avoid rate limiting
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            
            logger.info(`📧 Deep validation completed for "${executiveName}": ${validatedEmails.length}/${emails.length} emails passed`);
            
            return validatedEmails;
            
        } catch (error) {
            logger.error(`❌ Failed to run deep email validator: ${error.message}`);
            logger.warn(`⚠️ Skipping deep validation, returning original emails`);
            return emails; // Return original emails if validation fails
        }
    }

    /**
     * Validate emails using custom API service for final validation
     * This replaces SMTP validation with comprehensive API validation
     */
    async validateEmailsWithAPI(emails, executiveName) {
        if (!emails || emails.length === 0) {
            return [];
        }

        try {
            logger.info(`🔍 Running API email validation for "${executiveName}" on ${emails.length} emails`);
            
            const apiUrl = 'http://**************:3472/api/validate-emails';
            const apiKey = 'SrmnTK1itOO8vJzEVFQiEG9kltXHxG9B';
            
            // Prepare curl command with proper escaping
            const payload = JSON.stringify({ emails }).replace(/"/g, '\\"');
            const curlCommand = `curl -X POST "${apiUrl}" ` +
                `-H "Content-Type: application/json" ` +
                `-H "X-API-Key: ${apiKey}" ` +
                `-d "${payload}" ` +
                `--connect-timeout 30 --max-time 60 --silent`;
            
            // Execute curl command
            const result = execSync(curlCommand, { 
                encoding: 'utf8',
                timeout: 65000,
                stdio: ['pipe', 'pipe', 'pipe']
            });
            
            // Parse API response
            const response = JSON.parse(result.trim());
            
            if (!response.results) {
                logger.error(`❌ Invalid API response format for "${executiveName}"`);
                return [];
            }
            
            // Extract valid emails
            const validatedEmails = response.results
                .filter(result => result.is_valid === true)
                .map(result => result.email);
            
            // Log validation summary
            const summary = response.summary || {};
            logger.info(`📧 API validation completed for "${executiveName}": ${summary.valid || validatedEmails.length}/${summary.total || emails.length} emails passed`);
            
            if (validatedEmails.length > 0) {
                logger.debug(`✅ Valid emails found:`, validatedEmails.slice(0, 3));
            } else {
                logger.warn(`❌ No valid emails found for "${executiveName}"`);
            }
            
            return validatedEmails;
            
        } catch (error) {
            logger.error(`❌ Failed to validate emails via API: ${error.message}`);
            logger.warn(`⚠️ Skipping API validation, returning original emails`);
            return emails; // Return original emails if validation fails
        }
    }

    async enhanceExecutivesWithEmailPredictions() {
        const domain = this.companyDetails.domain || '';
        
        // Use Promise.all to handle async email generation for all executives
        const enhancedExecutives = await Promise.all(
            this.executives.map(async (exec) => {
                // Generate email predictions if domain is available
                if (domain && exec.name) {
                    exec.email_predictions = await this.generateEmailPredictions(exec.name, domain);
                } else {
                    exec.email_predictions = [];
                }
                
                return exec;
            })
        );
        
        return enhancedExecutives;
    }

    async generateEmailPredictions(executiveName, companyDomain) {
        if (!executiveName || !companyDomain) {
            return [];
        }

        try {
            // Clean the name and domain
            const cleanName = executiveName.trim();
            const domain = companyDomain.replace(/^https?:\/\//, '').replace(/^www\./, '').split('/')[0];
            
            // Use the Python email prediction script (without SMTP)
            const emailPredictions = this.generateEmailPredictionsUsingPython(cleanName, domain);
            
            if (!emailPredictions || emailPredictions.length === 0) {
                logger.warn('Failed to generate email predictions:', executiveName);
                return [];
            }

            // Apply additional validation using deep-email-validator (without SMTP)
            const deepValidatedEmails = await this.validateEmailsWithDeepValidator(emailPredictions, executiveName);

            // Apply final validation using custom API service
            const finalValidEmails = await this.validateEmailsWithAPI(deepValidatedEmails, executiveName);

            logger.debug(`Generated ${emailPredictions.length} email predictions for ${executiveName}, ${deepValidatedEmails.length} passed deep validation, ${finalValidEmails.length} passed API validation`, {
                originalName: executiveName,
                domain: domain,
                python_generated: emailPredictions.length,
                deep_valid: deepValidatedEmails.length,
                api_valid: finalValidEmails.length,
                predictions: finalValidEmails.slice(0, 3) // Log first 3 for debugging
            });

            return finalValidEmails;

        } catch (error) {
            logger.error('Error generating email predictions:', {
                executiveName,
                companyDomain,
                error: error.message
            });
            return [];
        }
    }

    generateEmailPredictionsUsingPython(name, domain) {
        try {
            if (!name || !domain || name.trim() === '' || domain.trim() === '') {
                return [];
            }

            // Path to the Python email prediction script
            const scriptPath = path.join(__dirname, '..', 'crawl-executives-scripts', 'generate_email_predictions.py');
            
            // Execute Python script with virtual environment but NO SMTP validation
            const venvPath = path.join(__dirname, '..', 'crawl-executives-scripts', 'venv');
            const pythonPath = path.join(venvPath, 'bin', 'python3');
            
            // Skip SMTP validation in Python script
            const command = `cd "${path.dirname(scriptPath)}" && "${pythonPath}" generate_email_predictions.py "${name.replace(/"/g, '\\"')}" "${domain.replace(/"/g, '\\"')}" --no-validation`;
            
            logger.info(`📧 Generating email patterns for "${name}" at ${domain} using Python (no SMTP)`);
            
            const result = execSync(command, { 
                encoding: 'utf8',
                timeout: 30000, // Reduced timeout since no SMTP
                stdio: ['pipe', 'pipe', 'pipe']
            });
            
            // Parse JSON response
            const jsonResponse = JSON.parse(result.trim());
            
            // Check if script was successful
            if (!jsonResponse.success) {
                logger.warn(`Python email prediction script failed for "${name}": ${jsonResponse.error}`);
                return [];
            }
            
            // Extract all email predictions (no SMTP filtering needed)
            const predictions = jsonResponse.predictions || [];
            const allEmails = predictions
                .map(p => p.email)
                .filter(email => email && email.length > 0);
            
            logger.info(`📧 Python generated ${allEmails.length} email patterns for "${name}"`);
            
            return allEmails;
            
        } catch (error) {
            logger.error(`Python email generation failed for "${name}": ${error.message}`);
            return [];
        }
    }

    calculateConfidenceScore(method) {
        const scores = {
            'json_ld_organization': 0.95,
            'json_ld_person': 0.95,
            'microdata_schema': 0.90,
            'table_label_value_pair': 0.90,  // Very high confidence for label-value pairs like "代表者名"
            'table_th_td_pair': 0.88,        // High confidence for semantic th-td pairs
            'structured_html_li': 0.85,
            'structured_html_div_h_p': 0.80,
            'card_layout': 0.75,
            'table_th_td_reverse': 0.72,     // Slightly lower for reverse th-td
            'table_multi_cell': 0.70,       // Original table method
            'table_multi_cell_reverse': 0.68,
            'definition_list': 0.70,
            'regex_text_pattern': 0.65,     // New regex-based text extraction method
            'grid_layout': 0.65,
            'enhanced_list': 0.60,
            'social_context': 0.55,
            'text_based': 0.50,
            'text_based_enhanced': 0.52,
            'text_based_keyword': 0.48
        };
        
        return scores[method] || 0.50;
    }

    isExecutiveLabel(text) {
        if (!text || typeof text !== 'string') return false;
        
        const cleanedText = text.trim();
        
        // Common executive/company representative labels in Japanese and English
        const executiveLabels = [
            // Japanese labels
            '代表者名', '代表者', '社長名', '社長', '会長名', '会長',
            '代表取締役名', '取締役名', '役員名', '経営者名',
            '代表', '責任者', '担当者', '連絡先',
            
            // English labels  
            'CEO', 'President', 'Chairman', 'Director', 'Executive',
            'Representative', 'Contact Person', 'Manager', 'Leader',
            'Chief Executive', 'Chief Officer', 'Managing Director',
            
            // Common form field labels
            'Name', 'Full Name', 'Contact Name', 'Person in Charge',
            'Representative Name', 'Executive Name', 'Director Name'
        ];
        
        // Check exact matches and partial matches
        return executiveLabels.some(label => 
            cleanedText === label || 
            cleanedText.includes(label) || 
            label.includes(cleanedText)
        );
    }

    determineExecutiveLevel(position) {
        const lowerPosition = position.toLowerCase();
        
        // C-Level positions (English and Japanese)
        if (/ceo|chief executive|president|founder|chairman|managing director/i.test(lowerPosition) ||
            /代表取締役|代表取締役社長|代表者|最高経営責任者|最高執行責任者|最高財務責任者|最高技術責任者|最高情報責任者|代表/i.test(position)) {
            return 'c_level';
        }
        
        // Director level (English and Japanese)
        if (/director|vp|vice president|chief|head of|senior manager/i.test(lowerPosition) ||
            /取締役|常務取締役|執行役員/i.test(position)) {
            return 'director';
        }
        
        // Manager level
        if (/manager|supervisor|lead/i.test(lowerPosition)) {
            return 'manager';
        }
        
        return 'other';
    }

    convertToRomajiUsingPython(name) {
        try {
            if (!name || name.trim() === '') {
                return '';
            }

            // Path to the Python script
            const scriptPath = path.join(__dirname, '..', 'crawl-executives-scripts', 'kanji_to_romaji.py');
            
            // Execute Python script with capitalization option
            const command = `cd "${path.dirname(scriptPath)}" && python kanji_to_romaji.py -c "${name.replace(/"/g, '\\"')}"`;
            
            const result = execSync(command, { 
                encoding: 'utf8',
                timeout: 5000, // 5 seconds timeout
                stdio: ['pipe', 'pipe', 'pipe']
            });
            
            const romaji = result.trim();
            
            // Check if output contains error messages
            if (romaji.includes('error') || romaji.includes('Traceback')) {
                logger.warn(`Python script error for "${name}": ${romaji}`);
                return this.fallbackRomajiConversion(name);
            }
            
            // Format Japanese names properly (add space between surname and given name)
            const formattedRomaji = this.formatJapaneseName(romaji);
            
            logger.debug(`Converted "${name}" to "${formattedRomaji}" using Python script`);
            
            return formattedRomaji.toLowerCase();
            
        } catch (error) {
            logger.warn(`Failed to convert "${name}" using Python script: ${error.message}`);
            return this.fallbackRomajiConversion(name);
        }
    }

    formatJapaneseName(romaji) {
        // Common Japanese surname patterns (first part of name)
        const japaneseSurnames = [
            'tanaka', 'yamada', 'sato', 'suzuki', 'takahashi', 'watanabe', 'ito', 'nakamura',
            'kobayashi', 'saito', 'kato', 'yoshida', 'yamamoto', 'sasaki', 'matsumoto', 'inoue',
            'kimura', 'hayashi', 'shimizu', 'yamazaki', 'mori', 'abe', 'ikeda', 'hashimoto',
            'yamashita', 'ishikawa', 'nakajima', 'maeda', 'fujita', 'ogawa', 'goto', 'okada',
            'hasegawa', 'murai', 'fukuda', 'ota', 'miura', 'fujiwara', 'okamoto', 'matsuda',
            'nakagawa', 'nakano', 'harada', 'ono', 'tamura', 'takeuchi', 'kaneko', 'wada'
        ];
        
        const lowerRomaji = romaji.toLowerCase();
        
        // Try to match common surname patterns and add space
        for (const surname of japaneseSurnames) {
            if (lowerRomaji.startsWith(surname)) {
                const surnameLength = surname.length;
                if (lowerRomaji.length > surnameLength) {
                    const givenName = lowerRomaji.substring(surnameLength);
                    return `${surname} ${givenName}`.trim();
                }
            }
        }
        
        return romaji;
    }

    fallbackRomajiConversion(name) {
        // Simple fallback - if name contains Japanese characters, return empty string
        // Otherwise return the name as-is (assuming it's already in Latin script)
        if (/[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF]/.test(name)) {
            logger.warn(`No romaji conversion available for Japanese name: ${name}`);
            return '';
        }
        
        return name.toLowerCase();
    }
}

async function main() {
    if (!options.url) {
        logger.error('❌ URL is required');
        program.help();
    }

    const crawler = new ExecutiveCrawler(options);
    
    // Add overall timeout to prevent hanging
    const OVERALL_TIMEOUT = 240000; // 4 minutes
    
    try {
        await Promise.race([
            crawler.crawl(),
            new Promise((_, reject) => 
                setTimeout(() => {
                    logger.warn('⏰ Overall script timeout reached - gracefully exiting');
                    reject(new Error('GRACEFUL_TIMEOUT'));
                }, OVERALL_TIMEOUT)
            )
        ]);
        
        // Exit cleanly after successful completion
        process.exit(0);
        
    } catch (error) {
        if (error.message === 'GRACEFUL_TIMEOUT') {
            logger.warn('🔄 Graceful timeout - returning skip result');
            
            const skipResult = {
                success: false,
                skipped: true,
                skip_reason: 'OVERALL_TIMEOUT',
                error: 'Script timeout - job skipped to prevent queue failure',
                url: options.url,
                company_name: options.companyName,
                executives: [],
                company_details: {},
                timestamp: new Date().toISOString()
            };

            console.log(JSON.stringify(skipResult));
            process.exit(0); // Exit gracefully
        } else {
            throw error; // Re-throw other errors
        }
    }
}

process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled Rejection:', reason);
    process.exit(1);
});

// Handle EPIPE errors gracefully
process.on('uncaughtException', (error) => {
    if (error.code === 'EPIPE') {
        // EPIPE means the parent process has closed the pipe
        // This happens when PHP process terminates before Node.js finishes
        logger.warn('🔌 Parent process disconnected (EPIPE) - exiting gracefully');
        process.exit(0); // Exit cleanly instead of error
    } else {
        logger.error('🚨 Uncaught exception:', error);
        process.exit(1);
    }
});

// Handle broken pipe errors on stdout/stderr
process.stdout.on('error', (error) => {
    if (error.code === 'EPIPE') {
        logger.warn('🔌 STDOUT pipe broken - parent disconnected');
        process.exit(0);
    }
});

process.stderr.on('error', (error) => {
    if (error.code === 'EPIPE') {
        logger.warn('🔌 STDERR pipe broken - parent disconnected');
        process.exit(0);
    }
});

if (require.main === module) {
    main();
}

module.exports = ExecutiveCrawler;