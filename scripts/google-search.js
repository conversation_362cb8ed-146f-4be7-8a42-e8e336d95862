#!/usr/bin/env node

require('dotenv').config();
const { program } = require('commander');
const logger = require('./utils/logger');
const BrowserManager = require('./utils/browser');

program
    .option('-q, --query <query>', 'Search query')
    .option('-c, --company-name <name>', 'Company name to search for')
    .option('--output <output>', 'Output file path', './output/search.json')
    .parse();

const options = program.opts();

class GoogleSearchService {
    constructor() {
        this.browserManager = new BrowserManager();
        this.searchResults = [];
    }

    async search(companyName) {
        try {
            logger.info('🔍 Starting Bing search', { companyName });

            const searchQuery = this.prepareSearchQuery(companyName);
            const result = await this.performBingSearch(searchQuery, companyName);
            
            await this.browserManager.close();

            // Write to output file
            const fs = require('fs');
            const path = require('path');
            
            const outputDir = path.dirname(options.output);
            if (!fs.existsSync(outputDir)) {
                fs.mkdirSync(outputDir, { recursive: true });
            }
            
            fs.writeFileSync(options.output, JSON.stringify(result, null, 2));

            logger.info('✅ Bing search completed', {
                company: companyName,
                website_found: result.website || 'none',
                output_file: options.output
            });

            // Output to stdout for PHP to read
            console.log(JSON.stringify(result));
            return result;

        } catch (error) {
            logger.warn('⚠️ Bing search failed, trying Google:', error.message);
            
            // Try Google as fallback
            try {
                const searchQuery = this.prepareSearchQuery(companyName);
                const result = await this.performSearch(searchQuery, companyName);
                
                await this.browserManager.close();

                // Write to output file
                const fs = require('fs');
                const path = require('path');
                
                const outputDir = path.dirname(options.output);
                if (!fs.existsSync(outputDir)) {
                    fs.mkdirSync(outputDir, { recursive: true });
                }
                
                fs.writeFileSync(options.output, JSON.stringify(result, null, 2));

                logger.info('✅ Google search completed', {
                    company: companyName,
                    website_found: result.website || 'none',
                    output_file: options.output
                });

                console.log(JSON.stringify(result));
                return result;
                
            } catch (googleError) {
                logger.warn('⚠️ Google search also failed, trying DuckDuckGo:', googleError.message);
                
                // Try DuckDuckGo as last fallback
                try {
                    const searchQuery = this.prepareSearchQuery(companyName);
                    const result = await this.performDuckDuckGoSearch(searchQuery, companyName);
                    
                    await this.browserManager.close();

                    // Write to output file
                    const fs = require('fs');
                    const path = require('path');
                    
                    const outputDir = path.dirname(options.output);
                    if (!fs.existsSync(outputDir)) {
                        fs.mkdirSync(outputDir, { recursive: true });
                    }
                    
                    fs.writeFileSync(options.output, JSON.stringify(result, null, 2));

                    logger.info('✅ DuckDuckGo search completed', {
                        company: companyName,
                        website_found: result.website || 'none',
                        output_file: options.output
                    });

                    console.log(JSON.stringify(result));
                    return result;
                    
                } catch (duckError) {
                    logger.error('❌ All search engines failed:', { 
                        bing: error.message, 
                        google: googleError.message,
                        duckduckgo: duckError.message
                    });
                    
                    const errorResult = {
                        success: false,
                        error: `All search engines failed. Bing: ${error.message}, Google: ${googleError.message}, DuckDuckGo: ${duckError.message}`,
                        company_name: companyName,
                        website: null
                    };

                    console.log(JSON.stringify(errorResult));
                    process.exit(1);
                }
            }
        }
    }

    prepareSearchQuery(companyName) {
        // Clean company name but preserve Japanese/Asian characters
        // Only remove clearly problematic characters
        const cleanName = companyName
            // Remove brackets and parentheses content but keep the company name structure
            .replace(/[()（）\[\]{}【】「」]/g, ' ')
            // Remove common company suffixes in parentheses but keep other text
            .replace(/\s*[()（）]\s*[株式会社有限責任限公司Ltd\.Inc\.Corp\.Co\.]*\s*[()（）]\s*/g, ' ')
            // Remove excessive punctuation but keep Japanese characters
            .replace(/[#$%^&*+=|\\`~<>?]/g, ' ')
            // Keep periods, commas, hyphens as they might be part of company names
            // Keep all Unicode letters (including Japanese, Chinese, Korean, etc.)
            .replace(/\s+/g, ' ')
            .trim();
        
        // Add more specific search terms for better results
        const searchTerms = [cleanName];
        
        // For Japanese companies, add common variations
        if (/[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF]/.test(companyName)) {
            searchTerms.push('会社'); // Add "company" in Japanese
        }
        
        return searchTerms.join(' ') + ' website';
    }

    async performSearch(query, companyName) {
        const page = await this.browserManager.getPage();
        
        try {
            logger.debug(`🌐 Searching Google for: ${query}`);
            
            // Navigate to Google with random delay
            await this.browserManager.addDelay(1000, 3000);
            
            await page.goto('https://www.google.com', {
                waitUntil: 'networkidle2',
                timeout: 30000
            });
            
            // Add extra delay to appear more human
            await this.browserManager.addDelay(2000, 5000);

            // Accept cookies if present
            try {
                await page.waitForSelector('[id*="accept"], [id*="agree"]', { timeout: 3000 });
                await page.click('[id*="accept"], [id*="agree"]');
                await this.browserManager.addDelay(1000, 2000);
            } catch (e) {
                // No cookie banner or already accepted
            }

            // Find and fill search box - try multiple selectors
            const searchSelectors = [
                'input[name="q"]',
                'textarea[name="q"]',
                '[role="combobox"]',
                'input[type="text"]'
            ];
            
            let searchInput = null;
            for (const selector of searchSelectors) {
                try {
                    await page.waitForSelector(selector, { timeout: 3000 });
                    searchInput = await page.$(selector);
                    if (searchInput) {
                        logger.debug(`✅ Found search input with selector: ${selector}`);
                        break;
                    }
                } catch (e) {
                    logger.debug(`⚠️ Selector ${selector} not found`);
                }
            }
            
            if (!searchInput) {
                throw new Error('Could not find Google search input');
            }
            
            // Clear any existing text and type slowly
            await searchInput.click({ clickCount: 3 });
            await this.browserManager.addDelay(500, 1000);
            await searchInput.type(query, { delay: 150 + Math.random() * 100 });

            // Simulate human behavior before search
            await this.browserManager.simulateHumanBehavior(page);
            await this.browserManager.addDelay(1000, 2000);
            
            // Submit search with random method
            if (Math.random() > 0.5) {
                await page.keyboard.press('Enter');
            } else {
                // Try to click search button
                try {
                    const searchButton = await page.$('input[type="submit"], button[type="submit"]');
                    if (searchButton) {
                        await searchButton.click();
                    } else {
                        await page.keyboard.press('Enter');
                    }
                } catch (e) {
                    await page.keyboard.press('Enter');
                }
            }
            
            // Wait for results with multiple possible selectors
            const resultSelectors = [
                '[data-ved]',
                '.g',
                '#search',
                '.MjjYud'
            ];
            
            let resultsFound = false;
            for (const selector of resultSelectors) {
                try {
                    await page.waitForSelector(selector, { timeout: 5000 });
                    logger.debug(`✅ Found results with selector: ${selector}`);
                    resultsFound = true;
                    break;
                } catch (e) {
                    logger.debug(`⚠️ Results selector ${selector} not found`);
                }
            }
            
            if (!resultsFound) {
                // Check if we hit a CAPTCHA or other blocking page
                const pageContent = await page.content();
                if (pageContent.includes('captcha') || pageContent.includes('unusual traffic')) {
                    throw new Error('Google CAPTCHA detected - please try again later');
                }
                throw new Error('Could not find search results');
            }
            
            // Wait for results to load
            await this.browserManager.addDelay(2000, 4000);

            // Extract search results with multiple strategies
            const searchResults = await page.evaluate(() => {
                const results = [];
                
                // Try multiple selectors for search results
                const resultSelectors = [
                    'div[data-ved] h3',
                    '.g h3',
                    '.MjjYud h3',
                    '[data-header-feature] h3',
                    'h3'
                ];
                
                let resultElements = [];
                for (const selector of resultSelectors) {
                    resultElements = document.querySelectorAll(selector);
                    if (resultElements.length > 0) {
                        console.log(`Found ${resultElements.length} results with selector: ${selector}`);
                        break;
                    }
                }
                
                resultElements.forEach((element, index) => {
                    if (index >= 10) return; // Limit to first 10 results
                    
                    const linkElement = element.closest('a');
                    if (linkElement && linkElement.href && !linkElement.href.includes('google.com')) {
                        results.push({
                            title: element.textContent,
                            url: linkElement.href,
                            index: index
                        });
                    }
                });
                
                return results;
            });

            logger.debug(`📊 Found ${searchResults.length} search results`);

            // Simplified: just take the first valid website
            const website = this.extractFirstValidWebsite(searchResults);

            return {
                success: true,
                company_name: companyName,
                search_query: query,
                website: website,
                search_results: searchResults,
                extraction_method: 'js_google_search',
                timestamp: new Date().toISOString()
            };

        } catch (error) {
            logger.error(`❌ Error performing Google search:`, error);
            throw error;
        } finally {
            await this.browserManager.releasePage(page);
        }
    }

    extractFirstValidWebsite(searchResults) {
        if (!searchResults || searchResults.length === 0) {
            return null;
        }

        const skipDomains = [
            'facebook.com', 'twitter.com', 'linkedin.com', 'instagram.com', 
            'youtube.com', 'wikipedia.org', 'google.com', 'bing.com', 'duckduckgo.com'
        ];

        // Just take the first result that's not in skip domains
        for (const result of searchResults) {
            try {
                const url = new URL(result.url);
                const hostname = url.hostname.toLowerCase();
                
                if (!skipDomains.some(domain => hostname.includes(domain))) {
                    const website = `${url.protocol}//${url.hostname}`;
                    logger.info(`✅ Taking first valid result: ${website}`);
                    return website;
                }
            } catch (error) {
                logger.debug(`❌ Invalid URL: ${result.url}`);
                continue;
            }
        }

        return null;
    }

    async performDuckDuckGoSearch(query, companyName) {
        const page = await this.browserManager.getPage();
        
        try {
            logger.debug(`🦆 Searching DuckDuckGo for: ${query}`);
            
            // Navigate to DuckDuckGo
            await page.goto('https://duckduckgo.com', {
                waitUntil: 'networkidle2',
                timeout: 30000
            });

            await this.browserManager.addDelay(1000, 2000);

            // Find search input
            await page.waitForSelector('input[name="q"]', { timeout: 10000 });
            await page.type('input[name="q"]', query, { delay: 100 });

            // Submit search
            await page.keyboard.press('Enter');
            await page.waitForSelector('.result', { timeout: 15000 });
            
            await this.browserManager.addDelay(2000, 3000);

            // Extract search results
            const searchResults = await page.evaluate(() => {
                const results = [];
                const resultElements = document.querySelectorAll('.result h2 a');
                
                resultElements.forEach((element, index) => {
                    if (index >= 10) return;
                    
                    if (element.href && !element.href.includes('duckduckgo.com')) {
                        results.push({
                            title: element.textContent,
                            url: element.href,
                            index: index
                        });
                    }
                });
                
                return results;
            });

            logger.debug(`📊 Found ${searchResults.length} DuckDuckGo results`);

            const website = this.extractFirstValidWebsite(searchResults);

            return {
                success: true,
                company_name: companyName,
                search_query: query,
                website: website,
                search_results: searchResults,
                extraction_method: 'js_duckduckgo_search',
                timestamp: new Date().toISOString()
            };

        } catch (error) {
            logger.error(`❌ Error performing DuckDuckGo search:`, error);
            throw error;
        } finally {
            await this.browserManager.releasePage(page);
        }
    }

    async performBingSearch(query, companyName) {
        const page = await this.browserManager.getPage();
        
        try {
            logger.debug(`🅱️ Searching Bing for: ${query}`);
            
            // Navigate to Bing
            await page.goto('https://www.bing.com', {
                waitUntil: 'networkidle2',
                timeout: 30000
            });

            await this.browserManager.addDelay(1000, 2000);

            // Find search input - Bing uses different selectors
            const searchSelectors = ['input[name="q"]', '#sb_form_q', '.sb_form_q'];
            let searchInput = null;
            
            for (const selector of searchSelectors) {
                try {
                    await page.waitForSelector(selector, { timeout: 3000 });
                    searchInput = await page.$(selector);
                    if (searchInput) {
                        logger.debug(`✅ Found Bing search input with selector: ${selector}`);
                        break;
                    }
                } catch (e) {
                    logger.debug(`⚠️ Bing selector ${selector} not found`);
                }
            }
            
            if (!searchInput) {
                throw new Error('Could not find Bing search input');
            }

            // Type search query
            await searchInput.click({ clickCount: 3 });
            await this.browserManager.addDelay(500, 1000);
            await searchInput.type(query, { delay: 100 });

            // Submit search
            await page.keyboard.press('Enter');
            
            // Wait for results - Bing has different result selectors
            const resultSelectors = ['.b_algo', '#b_results .b_algo', 'ol#b_results li'];
            let resultsFound = false;
            
            for (const selector of resultSelectors) {
                try {
                    await page.waitForSelector(selector, { timeout: 10000 });
                    logger.debug(`✅ Found Bing results with selector: ${selector}`);
                    resultsFound = true;
                    break;
                } catch (e) {
                    logger.debug(`⚠️ Bing results selector ${selector} not found`);
                }
            }
            
            if (!resultsFound) {
                throw new Error('Could not find Bing search results');
            }
            
            await this.browserManager.addDelay(2000, 3000);

            // Extract search results with multiple strategies
            const searchResults = await page.evaluate(() => {
                const results = [];
                
                // Try multiple Bing result selectors in order of preference
                const selectorStrategies = [
                    // Strategy 1: Standard Bing results
                    { selector: '.b_algo h2 a', name: 'b_algo' },
                    { selector: '#b_results .b_algo h2 a', name: 'b_results_algo' },
                    { selector: 'ol#b_results .b_algo h2 a', name: 'ol_results' },
                    { selector: '.b_title a', name: 'b_title' },
                    // Strategy 2: Alternative selectors
                    { selector: '.b_algo .b_title a', name: 'algo_title' },
                    { selector: '[data-bm] h2 a', name: 'data_bm' },
                    { selector: '.c-contentinfo a', name: 'contentinfo' },
                    // Strategy 3: Generic link selectors
                    { selector: '.b_algo a[href^="http"]', name: 'generic_http' },
                    { selector: '#b_results a[href^="http"]', name: 'results_http' }
                ];
                
                let linkElements = [];
                let usedStrategy = null;
                
                for (const strategy of selectorStrategies) {
                    linkElements = document.querySelectorAll(strategy.selector);
                    if (linkElements.length > 0) {
                        usedStrategy = strategy.name;
                        console.log(`Using Bing strategy: ${usedStrategy} - Found ${linkElements.length} elements`);
                        break;
                    }
                }
                
                if (linkElements.length === 0) {
                    console.log('No Bing results found with any selector');
                    return [];
                }
                
                linkElements.forEach((element, index) => {
                    if (index >= 10) return; // Only take first 10
                    
                    const url = element.href;
                    let title = element.textContent || element.innerText;
                    
                    // Try to get better title from parent or nearby elements
                    if (!title || title.trim().length < 3) {
                        const parent = element.closest('.b_algo, .b_title') || element.parentElement;
                        if (parent) {
                            title = parent.textContent || parent.innerText;
                        }
                    }
                    
                    // Skip Bing's own domains and ads
                    if (url && 
                        !url.includes('bing.com') && 
                        !url.includes('microsoft.com') &&
                        !url.includes('microsofttranslator.com') &&
                        title && 
                        title.trim().length > 3) {
                        
                        results.push({
                            title: title.trim(),
                            url: url,
                            index: index,
                            strategy: usedStrategy
                        });
                    }
                });
                
                console.log(`Extracted ${results.length} valid Bing results using strategy: ${usedStrategy}`);
                return results;
            });

            logger.debug(`📊 Found ${searchResults.length} Bing results`);

            // Simplified website extraction - just take the first valid result
            let website = null;
            if (searchResults.length > 0) {
                const firstResult = searchResults[0];
                try {
                    const url = new URL(firstResult.url);
                    const skipDomains = [
                        'facebook.com', 'twitter.com', 'linkedin.com', 'instagram.com', 
                        'youtube.com', 'wikipedia.org', 'google.com', 'bing.com'
                    ];
                    
                    if (!skipDomains.some(domain => url.hostname.includes(domain))) {
                        website = `${url.protocol}//${url.hostname}`;
                        logger.info(`✅ Taking first Bing result: ${website}`);
                    }
                } catch (error) {
                    logger.debug(`❌ Invalid URL in first result: ${firstResult.url}`);
                }
            }

            return {
                success: true,
                company_name: companyName,
                search_query: query,
                website: website,
                search_results: searchResults,
                extraction_method: 'js_bing_search',
                timestamp: new Date().toISOString()
            };

        } catch (error) {
            logger.error(`❌ Error performing Bing search:`, error);
            throw error;
        } finally {
            await this.browserManager.releasePage(page);
        }
    }
}

// Main execution
async function main() {
    const companyName = options.companyName || options.query;
    
    if (!companyName) {
        logger.error('❌ Company name or query is required');
        program.help();
    }

    const searchService = new GoogleSearchService();
    await searchService.search(companyName);
}

// Handle unhandled errors
process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled Rejection:', reason);
    process.exit(1);
});

process.on('uncaughtException', (error) => {
    logger.error('Uncaught Exception:', error);
    process.exit(1);
});

// Run if called directly
if (require.main === module) {
    main();
}

module.exports = GoogleSearchService; 