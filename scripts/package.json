{"name": "company-crawler-scripts", "version": "1.0.0", "description": "JavaScript crawling scripts for company crawler", "main": "index.js", "scripts": {"crawl-companies": "node crawl-companies.js", "crawl-executives": "node crawl-executives.js", "google-search": "node google-search.js"}, "dependencies": {"axios": "^1.6.5", "commander": "^11.1.0", "deep-email-validator": "^0.1.21", "dotenv": "^16.3.1", "jssoup": "^0.0.15", "puppeteer": "^24.11.2", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-adblocker": "^2.13.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "user-agents": "^1.1.114", "winston": "^3.11.0"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0"}, "keywords": [], "author": "", "license": "ISC"}