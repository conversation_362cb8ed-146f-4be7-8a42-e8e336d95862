# Company Crawler JavaScript Scripts

Bộ JavaScript scripts cho việc crawl dữ liệu công ty, thay thế cho PHP crawling logic để cải thiện performance và maintainability.

## 📁 Cấu trúc thư mục

```
scripts/
├── utils/
│   ├── logger.js           # Winston logging utility
│   └── browser.js          # Puppeteer browser manager với anti-detection
├── crawl-companies.js      # Script crawl danh sách công ty
├── crawl-executives.js     # Script crawl thông tin executives
├── google-search.js        # Script tìm kiếm Google
├── test-script.js          # Script kiểm tra setup
├── package.json           # Dependencies
└── README.md              # Tài liệu này
```

## 🚀 Cài đặt

```bash
cd scripts
yarn install
```

## 🛠️ Dependencies chính

- **puppeteer**: Browser automation
- **puppeteer-extra**: Enhanced Puppeteer với plugins
- **puppeteer-extra-plugin-stealth**: Anti-detection
- **puppeteer-extra-plugin-adblocker**: Block ads để tăng tốc
- **cheerio**: Server-side HTML parsing
- **winston**: Advanced logging
- **commander**: CLI argument parsing

## 🧪 Kiểm tra Setup

```bash
# Test cơ bản
node test-script.js

# Test với debug mode
NODE_ENV=development node test-script.js
```

## 📝 Sử dụng Scripts

### 1. Crawl Company List

```bash
node crawl-companies.js \
  --url "https://example.com/companies" \
  --selector ".company-name" \
  --max-pages 5 \
  --has-pagination \
  --pagination-format "query_param"
```

**Parameters:**
- `--url`: URL nguồn để crawl
- `--selector`: CSS selector cho tên công ty
- `--link-selector`: CSS selector cho link công ty (optional)
- `--max-pages`: Số trang tối đa để crawl
- `--has-pagination`: Có phân trang hay không
- `--pagination-format`: Định dạng pagination (query_param, path_segment, etc.)
- `--output`: File output (default: ./output/companies.json)

### 2. Crawl Company Executives

```bash
node crawl-executives.js \
  --url "https://company.com/about" \
  --company-name "Company Name" \
  --keywords "CEO,President,Director"
```

**Parameters:**
- `--url`: URL trang thông tin công ty
- `--company-name`: Tên công ty để context
- `--keywords`: Keywords executive (comma-separated)
- `--output`: File output (default: ./output/executives.json)

### 3. Google Search

```bash
node google-search.js \
  --company-name "Company Name" \
  --query "Company Name website"
```

**Parameters:**
- `--company-name`: Tên công ty cần tìm
- `--query`: Query tìm kiếm (optional)
- `--output`: File output (default: ./output/search.json)

## 🔧 Configuration

### Environment Variables

```bash
# .env file
NODE_ENV=production          # development | production
LOG_LEVEL=info              # error | warn | info | debug
HEADLESS=true               # true | false (show browser)
```

### Anti-Detection Features

- **Stealth Plugin**: Ẩn dấu hiệu automation
- **Random User Agents**: Rotate user agents
- **Human Simulation**: Mouse movements, scrolling
- **Resource Blocking**: Block images/CSS để tăng tốc
- **Connection Pooling**: Reuse browser instances

## 📊 Output Format

### Company List Output
```json
{
  "success": true,
  "companies": [
    {
      "name": "Company Name",
      "source_url": "https://...",
      "website": "https://company.com",
      "extraction_method": "js_script"
    }
  ],
  "pages_crawled": 3,
  "total_companies": 25
}
```

### Executive Output
```json
{
  "success": true,
  "executives": [
    {
      "name": "John Doe",
      "position": "CEO",
      "level": "c_level",
      "extraction_method": "structured_html"
    }
  ],
  "company_details": {
    "description": "...",
    "address": "...",
    "phone": "...",
    "email": "..."
  }
}
```

### Google Search Output
```json
{
  "success": true,
  "company_name": "Company Name",
  "website": "https://company.com",
  "search_results": [...]
}
```

## 🚨 Error Handling

Scripts có comprehensive error handling:

- **Network errors**: Retry với exponential backoff
- **Browser crashes**: Automatic recovery
- **Timeout handling**: Configurable timeouts
- **Resource cleanup**: Always cleanup browsers/pages
- **Structured logging**: Chi tiết logs cho debugging

## 🔍 Debugging

```bash
# Enable debug logging
LOG_LEVEL=debug node crawl-companies.js ...

# Show browser (non-headless)
HEADLESS=false node crawl-companies.js ...

# Development mode với verbose logging
NODE_ENV=development LOG_LEVEL=debug node test-script.js
```

## 📈 Performance Tips

1. **Browser Reuse**: Scripts reuse browser instances
2. **Page Pooling**: Pages được pool và reuse
3. **Resource Blocking**: Block images/CSS không cần thiết
4. **Concurrent Processing**: Có thể chạy multiple instances
5. **Memory Management**: Automatic cleanup để prevent leaks

## 🔗 Integration với PHP

Scripts được thiết kế để integrate với PHP Jobs:

```php
// Trong PHP Job
$command = [
    'node',
    base_path('scripts/crawl-companies.js'),
    '--url', $dataSource->source_url,
    '--selector', $dataSource->company_name_selector,
    '--output', $outputPath
];

$process = new Process($command);
$process->run();

$result = json_decode($process->getOutput(), true);
```

## 🛡️ Security & Anti-Detection

- **Stealth techniques**: Comprehensive bot detection avoidance
- **Rate limiting**: Built-in delays between requests
- **Proxy support**: Ready cho proxy integration
- **User agent rotation**: Random, realistic user agents
- **Browser fingerprinting**: Minimize detection signatures

## 📝 Logs

Logs được lưu tại:
- `storage/logs/crawler.log`: General logs
- `storage/logs/crawler-errors.log`: Error logs only

Format: JSON structured logging với timestamp, level, message, metadata.

## 🤝 Contributing

Khi thêm features mới:

1. Follow existing code patterns
2. Add comprehensive error handling
3. Update README documentation
4. Test với `test-script.js`
5. Ensure proper resource cleanup 