#!/usr/bin/env node

require('dotenv').config();
const { program } = require('commander');
const cheerio = require('cheerio');
const logger = require('./utils/logger');
const BrowserManager = require('./utils/browser');

program
    .option('-u, --url <url>', 'Data source URL to crawl')
    .option('-s, --selector <selector>', 'Company name selector')
    .option('-l, --link-selector <linkSelector>', 'Company link selector')
    .option('-p, --pagination-selector <paginationSelector>', 'Pagination selector')
    .option('--pagination-format <format>', 'Pagination format (query_param, path_segment, etc.)')
    .option('--max-pages <maxPages>', 'Maximum pages to crawl', parseInt, 5)
    .option('--has-pagination', 'Whether the source has pagination')
    .option('--output <output>', 'Output file path', './output/companies.json')
    .parse();

const options = program.opts();

class CompanyCrawler {
    constructor(options) {
        this.options = options;
        this.browserManager = new BrowserManager();
        this.companies = [];
        this.errors = [];
    }

    async crawl() {
        try {
            logger.info('🚀 Starting company list crawl', {
                url: this.options.url,
                maxPages: this.options.maxPages,
                hasPagination: this.options.hasPagination
            });

            if (this.options.hasPagination) {
                await this.crawlWithPagination();
            } else {
                await this.crawlSinglePage(this.options.url);
            }

            await this.browserManager.close();

            const result = {
                success: true,
                companies: this.companies,
                errors: this.errors,
                pages_crawled: this.options.hasPagination ? this.options.maxPages : 1,
                total_companies: this.companies.length
            };

            // Write to output file
            const fs = require('fs');
            const path = require('path');
            
            // Ensure output directory exists
            const outputDir = path.dirname(this.options.output);
            if (!fs.existsSync(outputDir)) {
                fs.mkdirSync(outputDir, { recursive: true });
            }
            
            fs.writeFileSync(this.options.output, JSON.stringify(result, null, 2));

            logger.info('✅ Crawl completed successfully', {
                companies_found: this.companies.length,
                errors_count: this.errors.length,
                output_file: this.options.output
            });

            // Output to stdout for PHP to read
            console.log(JSON.stringify(result));

            return result;

        } catch (error) {
            logger.error('❌ Crawl failed:', error);
            
            const errorResult = {
                success: false,
                error: error.message,
                companies: this.companies,
                errors: this.errors
            };

            console.log(JSON.stringify(errorResult));
            process.exit(1);
        }
    }

    async crawlWithPagination() {
        for (let page = 1; page <= this.options.maxPages; page++) {
            const url = this.buildPaginatedUrl(page);
            logger.info(`📄 Crawling page ${page}: ${url}`);
            
            await this.crawlSinglePage(url);
            
            // Add delay between pages
            await this.browserManager.addDelay(2000, 4000);
        }
    }

    buildPaginatedUrl(page) {
        const baseUrl = this.options.url;
        
        switch (this.options.paginationFormat) {
            case 'path_segment':
                return `${baseUrl.replace(/\/$/, '')}/page/${page}`;
            
            case 'query_param':
                const separator = baseUrl.includes('?') ? '&' : '?';
                return `${baseUrl}${separator}page=${page}`;
            
            case 'query_p':
                const sep1 = baseUrl.includes('?') ? '&' : '?';
                return `${baseUrl}${sep1}p=${page}`;
            
            case 'query_start':
                const sep2 = baseUrl.includes('?') ? '&' : '?';
                const start = (page - 1) * 20;
                return `${baseUrl}${sep2}start=${start}`;
            
            case 'offset':
                const sep3 = baseUrl.includes('?') ? '&' : '?';
                const offset = (page - 1) * 20;
                return `${baseUrl}${sep3}offset=${offset}`;
            
            case 'custom_offset':
                const sep4 = baseUrl.includes('?') ? '&' : '?';
                const from = (page - 1) * 20;
                return `${baseUrl}${sep4}from=${from}`;
            
            default:
                const defaultSep = baseUrl.includes('?') ? '&' : '?';
                return `${baseUrl}${defaultSep}page=${page}`;
        }
    }

    async crawlSinglePage(url) {
        const page = await this.browserManager.getPage();
        
        try {
            logger.debug(`🌐 Loading page: ${url}`);
            
            await page.goto(url, {
                waitUntil: 'networkidle2',
                timeout: 30000
            });

            // Wait for content to load
            await page.waitForSelector('body', { timeout: 10000 });
            await this.browserManager.addDelay(2000, 4000);

            // Simulate human behavior
            await this.browserManager.simulateHumanBehavior(page);

            // Get page content
            const content = await page.content();
            
            // Extract companies
            const pageCompanies = this.extractCompaniesFromHtml(content, url);
            this.companies.push(...pageCompanies);

            logger.info(`✅ Extracted ${pageCompanies.length} companies from page`);

        } catch (error) {
            logger.error(`❌ Error crawling page ${url}:`, error);
            this.errors.push({
                url,
                error: error.message,
                timestamp: new Date().toISOString()
            });
        } finally {
            await this.browserManager.releasePage(page);
        }
    }

    extractCompaniesFromHtml(html, sourceUrl) {
        const $ = cheerio.load(html);
        const companies = [];

        try {
            // Extract companies using the provided selector
            $(this.options.selector).each((index, element) => {
                const $element = $(element);
                const name = $element.text().trim();
                
                if (!name || name.length < 2) return;

                let companyUrl = null;
                let website = null;
                let needsGoogleSearch = false;
                
                // Try to get company URL if link selector is provided
                if (this.options.linkSelector) {
                    let $link = null;
                    
                    // Try different strategies to find the link
                    // 1. Look for link inside the current element
                    $link = $element.find(this.options.linkSelector).first();
                    
                    // 2. If not found, check if the current element itself matches the link selector
                    if (!$link.length && $element.is(this.options.linkSelector)) {
                        $link = $element;
                    }
                    
                    // 3. If still not found, look for link as sibling (same parent)
                    if (!$link.length) {
                        $link = $element.siblings(this.options.linkSelector).first();
                    }
                    
                    // 4. Look for link in the parent container (most common case)
                    if (!$link.length) {
                        $link = $element.parent().find(this.options.linkSelector).first();
                    }
                    
                    // 5. Look in closest container elements
                    if (!$link.length) {
                        $link = $element.closest('li, tr, div, article, dt').find(this.options.linkSelector).first();
                    }
                    
                    // 6. Last resort: search the whole document for links near this element
                    if (!$link.length) {
                        // Get all links matching the selector
                        const $allLinks = $(this.options.linkSelector);
                        // Find the one that's closest to our name element
                        let closestLink = null;
                        let shortestDistance = Infinity;
                        
                        $allLinks.each((i, linkEl) => {
                            const $linkEl = $(linkEl);
                            // Check if link is in same container as name
                            if ($linkEl.closest('li, tr, div, article, dt').find($element).length > 0 ||
                                $element.closest('li, tr, div, article, dt').find($linkEl).length > 0) {
                                closestLink = $linkEl;
                                return false; // break
                            }
                        });
                        
                        if (closestLink) {
                            $link = $(closestLink);
                        }
                    }
                    
                    if ($link.length) {
                        companyUrl = $link.attr('href');
                        logger.debug(`🔗 Found link for ${name}: ${companyUrl}`);
                        
                        // Resolve relative URLs
                        if (companyUrl && !companyUrl.startsWith('http')) {
                            const baseUrl = new URL(sourceUrl);
                            if (companyUrl.startsWith('/')) {
                                companyUrl = `${baseUrl.protocol}//${baseUrl.host}${companyUrl}`;
                            } else {
                                companyUrl = `${baseUrl.protocol}//${baseUrl.host}/${companyUrl}`;
                            }
                        }
                        
                        // Extract website from company link
                        website = this.extractWebsiteFromLink(companyUrl, name);
                        if (website) {
                            logger.debug(`✅ Extracted website for ${name}: ${website}`);
                        } else {
                            logger.debug(`⚠️ Link found but no valid website extracted for ${name}: ${companyUrl} - will use Google search`);
                            needsGoogleSearch = true;
                        }
                    } else {
                        logger.debug(`❌ No link found for ${name} using selector: ${this.options.linkSelector} - will use Google search`);
                        needsGoogleSearch = true;
                    }
                } else {
                    // No link selector provided, so we'll need Google search for website
                    needsGoogleSearch = true;
                    logger.debug(`📋 No link selector provided for ${name} - will use Google search`);
                }

                companies.push({
                    name: this.cleanCompanyName(name),
                    source_url: companyUrl || sourceUrl,
                    website: website,
                    needs_google_search: needsGoogleSearch,
                    extraction_method: 'js_script'
                });
            });

        } catch (error) {
            logger.error('❌ Error extracting companies from HTML:', error);
            this.errors.push({
                error: 'HTML extraction failed: ' + error.message,
                timestamp: new Date().toISOString()
            });
        }

        return companies;
    }

    cleanCompanyName(name) {
        return name
            .replace(/[\r\n\t]+/g, ' ')
            .replace(/\s+/g, ' ')
            .trim();
    }

    extractWebsiteFromLink(link, companyName) {
        if (!link) return null;
        
        try {
            const url = new URL(link);
            const hostname = url.hostname.toLowerCase();
            
            // Skip obvious non-company domains and social media
            const skipDomains = [
                'facebook.com', 'twitter.com', 'linkedin.com', 'instagram.com', 
                'youtube.com', 'tiktok.com', 'pinterest.com',
                'google.com', 'yahoo.com', 'bing.com',
                'wikipedia.org', 'github.com'
            ];
            
            if (skipDomains.some(domain => hostname.includes(domain))) {
                return null;
            }
            
            // Skip obvious non-website links (check file extensions at the end of path, not domain)
            const skipExtensions = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.zip', '.rar'];
            const urlPath = url.pathname.toLowerCase();
            if (skipExtensions.some(ext => urlPath.endsWith(ext))) {
                return null;
            }
            
            // If it's an external link (different domain from source), treat it as company website
            const baseHostname = new URL(this.options.url).hostname.toLowerCase();
            if (hostname !== baseHostname) {
                logger.debug(`🔗 Found external website for ${companyName}: ${hostname}`);
                return `${url.protocol}//${url.hostname}`;
            }
            
            // For internal links, we have several strategies:
            const path = url.pathname.toLowerCase();
            
            // Strategy 1: Check if it's obviously a company detail/profile page on the directory site
            // Use generic patterns that indicate internal directory pages (no hardcoded keywords)
            const internalPagePatterns = ['/profile', '/detail', '/member', '/directory', '/listing', '/view'];
            if (internalPagePatterns.some(pattern => path.includes(pattern))) {
                logger.debug(`🏢 Found internal company page for ${companyName}: ${link}`);
                // This is a company page on the directory site, not the actual company website
                return null;
            }
            
            // Strategy 2: Check if it has patterns that suggest it's redirecting to company website
            const redirectPatterns = ['redirect', 'goto', 'visit', 'link', 'external', 'out'];
            if (redirectPatterns.some(pattern => path.includes(pattern))) {
                logger.debug(`🔀 Found potential redirect link for ${companyName}: ${link}`);
                // This might be a redirect to company website, but we can't be sure
                // Better to return null and let Google search handle it
                return null;
            }
            
            // Strategy 3: Check if company name appears in the URL (might be a subdomain or path)
            const cleanCompanyName = companyName.toLowerCase()
                .replace(/[^\w\s]/g, '')
                .split(/\s+/)
                .filter(word => word.length > 2);
                
            if (cleanCompanyName.some(word => hostname.includes(word) || path.includes(word))) {
                logger.debug(`🎯 Company name found in URL for ${companyName}: ${hostname}`);
                // If company name is in the URL, it might be their actual website
                return `${url.protocol}//${url.hostname}`;
            }
            
            // Strategy 4: Check for direct website patterns
            const websitePatterns = ['.com', '.net', '.org', '.co.jp', '.jp'];
            if (websitePatterns.some(pattern => link.includes(pattern))) {
                logger.debug(`🌐 Found website pattern for ${companyName}: ${hostname}`);
                return `${url.protocol}//${url.hostname}`;
            }
            
            logger.debug(`❓ Unclear link type for ${companyName}: ${link} - skipping`);
            return null;
        } catch (error) {
            logger.debug(`❌ Error extracting website from link: ${error.message}`);
            return null;
        }
    }
}

// Main execution
async function main() {
    if (!options.url || !options.selector) {
        logger.error('❌ URL and selector are required');
        program.help();
    }

    const crawler = new CompanyCrawler(options);
    await crawler.crawl();
}

// Handle unhandled errors
process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});

process.on('uncaughtException', (error) => {
    logger.error('Uncaught Exception:', error);
    process.exit(1);
});

// Run if called directly
if (require.main === module) {
    main();
}

module.exports = CompanyCrawler; 