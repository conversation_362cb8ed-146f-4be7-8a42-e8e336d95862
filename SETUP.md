# Company Crawler - Complete Environment Setup Guide

## Tổng quan hệ thống

**Company Crawler** là hệ thống thu thập thông tin công ty tự động với kiến trúc hybrid PHP-JavaScript-Python:

- **PHP Backend**: Laravel 12.x với Filament admin interface
- **JavaScript Crawling**: Node.js scripts với Puppeteer automation  
- **Python Processing**: Japanese name conversion với advanced Romaji processing
- **Database**: MySQL với comprehensive data models
- **Queue System**: Redis-backed Laravel Queue cho background processing

## System Requirements

### Core Requirements
- **PHP**: 8.2 hoặc cao hơn
- **Node.js**: 18.x hoặc cao hơn 
- **Python**: 3.6 hoặc cao hơn
- **Composer**: 2.x
- **NPM**: 8.x hoặc cao hơn
- **MySQL**: 8.0 hoặc cao hơn
- **Redis**: 6.0 hoặc cao hơn (cho queue system)

### Operating System Support
- **macOS**: 10.15+ (Catalina hoặc mới hơn)
- **Ubuntu**: 20.04 LTS hoặc mới hơn
- **Windows**: 10/11 với WSL2 (recommended)

### Browser Requirements
- **Chrome/Chromium**: Latest stable version (cho Puppeteer)
- **ChromeDriver**: Auto-managed by Puppeteer

## 1. System Dependencies Installation

### macOS Setup
```bash
# Install Homebrew nếu chưa có
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install core dependencies
brew install php@8.2 mysql redis node python3
brew install --cask google-chrome

# Install Composer
curl -sS https://getcomposer.org/installer | php
mv composer.phar /usr/local/bin/composer

# Start services
brew services start mysql
brew services start redis
```

### Ubuntu Setup
```bash
# Update package index
sudo apt update && sudo apt upgrade -y

# Install PHP 8.2
sudo add-apt-repository ppa:ondrej/php
sudo apt update
sudo apt install php8.2 php8.2-cli php8.2-fpm php8.2-mysql php8.2-xml php8.2-curl php8.2-zip php8.2-gd php8.2-mbstring php8.2-intl php8.2-bcmath

# Install MySQL
sudo apt install mysql-server

# Install Redis
sudo apt install redis-server

# Install Node.js 18.x
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install nodejs

# Install Python 3 và pip
sudo apt install python3 python3-pip

# Install Chrome
wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | sudo apt-key add -
sudo sh -c 'echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google-chrome.list'
sudo apt update
sudo apt install google-chrome-stable

# Install Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer

# Start services
sudo systemctl start mysql
sudo systemctl enable mysql
sudo systemctl start redis
sudo systemctl enable redis
```

### Windows (WSL2) Setup
```bash
# Trong WSL2 Ubuntu environment
# Follow Ubuntu setup instructions above

# Ensure WSL2 can access Windows Chrome
export PUPPETEER_EXECUTABLE_PATH="/mnt/c/Program Files/Google/Chrome/Application/chrome.exe"
```

## 2. Project Setup

### Clone Repository
```bash
git clone <repository-url>
cd company-crawler
```

### Directory Structure Check
```
company-crawler/
├── app/                    # Laravel application
├── scripts/                # Node.js crawling scripts
├── crawl-executives-scripts/   # Python Japanese processing
├── database/              # Laravel migrations & seeders  
├── storage/               # Laravel storage
└── vendor/                # PHP dependencies (after composer install)
```

## 3. PHP/Laravel Environment Setup

### Install PHP Dependencies
```bash
# Install Laravel dependencies
composer install

# Copy environment file
cp .env.example .env

# Generate application key
php artisan key:generate
```

### Configure Environment (.env)
```env
# Basic App Configuration
APP_NAME="Company Crawler"
APP_ENV=local
APP_KEY=base64:... # Generated by php artisan key:generate
APP_DEBUG=true
APP_URL=http://localhost:8000

# Database Configuration
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=company_crawler
DB_USERNAME=root
DB_PASSWORD=your_mysql_password

# Queue Configuration (Redis recommended)
QUEUE_CONNECTION=redis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# Browser Automation
PUPPETEER_EXECUTABLE_PATH=/usr/bin/google-chrome  # Linux
# PUPPETEER_EXECUTABLE_PATH=/Applications/Google Chrome.app/Contents/MacOS/Google Chrome  # macOS
# PUPPETEER_EXECUTABLE_PATH="/mnt/c/Program Files/Google/Chrome/Application/chrome.exe"  # WSL2

# Crawling Configuration
CRAWL_DELAY=1
CRAWL_TIMEOUT=30
MAX_RETRY_ATTEMPTS=3

# File Storage
FILESYSTEM_DISK=local

# Logging
LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug
```

### Database Setup
```bash
# Create database
mysql -u root -p
CREATE DATABASE company_crawler CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
EXIT;

# Run migrations
php artisan migrate

# Seed database với default data
php artisan db:seed
```

## 4. Node.js Crawling Environment Setup

### Navigate to Scripts Directory
```bash
cd scripts/
```

### Install Node.js Dependencies
```bash
# Install all crawling dependencies
npm install

# Verify installation
npm list --depth=0
```

### Key Dependencies Installed
```json
{
  "puppeteer": "^21.5.0",           // Browser automation
  "puppeteer-extra": "^3.3.6",     // Enhanced Puppeteer với plugins
  "puppeteer-extra-plugin-stealth": "^2.11.2",  // Anti-detection
  "puppeteer-extra-plugin-adblocker": "^2.13.6", // Ad blocking
  "winston": "^3.11.0",            // Advanced logging
  "cheerio": "^1.0.0-rc.12"        // HTML parsing
}
```

### Test Node.js Environment
```bash
# Test browser launch
node -e "
const puppeteer = require('puppeteer');
(async () => {
  const browser = await puppeteer.launch({headless: 'new'});
  console.log('✅ Puppeteer browser launched successfully');
  await browser.close();
})();
"

# Test all crawling scripts
node crawl-companies.js --help
node crawl-executives.js --help  
node google-search.js --help
```

### Configure Scripts Directory
```bash
# Make sure utils directory có proper permissions
chmod +x utils/*.js

# Check script configurations
ls -la *.js
```

## 5. Python Japanese Processing Setup

### Navigate to Python Scripts Directory
```bash
cd ../crawl-executives-scripts/
```

### Install Python Dependencies
```bash
# Install pykakasi for Japanese processing
pip3 install -r requirements.txt

# Verify installation
python3 -c "import pykakasi; print('✅ pykakasi installed successfully')"
```

### Requirements.txt Contents
```txt
pykakasi>=2.2.1
```

### Test Python Environment
```bash
# Test Japanese name conversion
python3 kanji_to_romaji.py "田中太郎"
# Expected output: Tanaka Tarou

python3 kanji_to_romaji.py "代表取締役社長 山田花子"  
# Expected output: Yamada Hanako

# Test với complex names
python3 kanji_to_romaji.py "株式会社山田商事 代表取締役 山田太郎"
# Expected output: Yamada Tarou
```

### Python Script Features
- **Advanced Kanji Conversion**: Uses pykakasi library for accurate conversion
- **Name Formatting**: Proper Japanese name spacing (Family Given)
- **Title Removal**: Strips business titles và honorifics
- **Error Handling**: Graceful fallback nếu conversion fails
- **Performance**: ~100-200ms per name conversion

## 6. Queue System Setup

### Redis Configuration
```bash
# Test Redis connection
redis-cli ping
# Expected: PONG

# Check Redis is running
redis-cli info server | head -20
```

### Queue Worker Setup
```bash
# Return to project root
cd ..

# Test queue configuration  
php artisan queue:table
php artisan migrate

# Create queue worker script
cat > start-queue.sh << 'EOF'
#!/bin/bash
echo "Starting queue worker với extended timeout..."
php artisan queue:work redis --sleep=3 --tries=3 --max-time=7200 --timeout=7200
EOF

chmod +x start-queue.sh
```

### Queue Testing
```bash
# Test queue worker
php artisan queue:work --once

# Monitor queue status (nếu có Horizon)
php artisan horizon:install
php artisan horizon:publish
```

## 7. Browser Automation Setup

### ChromeDriver Setup
```bash
# Check Chrome version
google-chrome --version
# hoặc: /Applications/Google\ Chrome.app/Contents/MacOS/Google\ Chrome --version

# Puppeteer sẽ tự động manage ChromeDriver
# Không cần manual installation
```

### Browser Testing
```bash
# Test browser automation from Laravel
php artisan tinker
>>> $service = app(\App\Services\EnhancedWebCrawlerService::class);
>>> $result = $service->testConnection(new \stdClass());
>>> print_r($result);
```

## 8. Development Environment

### Start Development Services
```bash
# Terminal 1: Start Laravel development server
php artisan serve

# Terminal 2: Start queue worker
./start-queue.sh

# Terminal 3: Monitor logs (optional)
tail -f storage/logs/laravel.log

# Terminal 4: Redis monitoring (optional)  
redis-cli monitor
```

### Access Application
- **Admin Interface**: http://localhost:8000/admin
- **Default Credentials**: Set up during seeding or create manually

### Create Admin User
```bash
php artisan tinker
>>> $user = \App\Models\User::create([
...     'name' => 'Admin User',
...     'email' => '<EMAIL>',
...     'password' => bcrypt('password123'),
...     'role' => 'admin'
... ]);
>>> echo "Admin user created: " . $user->email;
```

## 9. Production Deployment Considerations

### Process Management (Supervisor)
```ini
# /etc/supervisor/conf.d/company-crawler-worker.conf
[program:company-crawler-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/company-crawler/artisan queue:work redis --sleep=3 --tries=3 --max-time=7200 --timeout=7200
directory=/var/www/company-crawler
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=2
redirect_stderr=true
stdout_logfile=/var/www/company-crawler/storage/logs/worker.log
stopwaitsecs=3600
```

### Environment Optimization
```env
# Production environment
APP_ENV=production
APP_DEBUG=false
QUEUE_CONNECTION=redis

# Performance tuning
OCTANE_SERVER=swoole  # Optional: Laravel Octane
REDIS_CLUSTER=true    # For Redis clustering
```

## 10. Testing & Verification

### Complete System Test
```bash
# 1. Test PHP/Laravel
php artisan test

# 2. Test Database connection
php artisan migrate:status

# 3. Test Node.js scripts
cd scripts/
npm test  # If tests are configured

# 4. Test Python scripts
cd ../crawl-executives-scripts/
python3 kanji_to_romaji.py "テスト名前"

# 5. Test queue system
cd ..
php artisan queue:work --once

# 6. Test crawling integration
php artisan crawler:test-google-search "test company"
```

### Verify All Components Working
```bash
# Check all services
echo "✓ PHP:" $(php --version | head -1)
echo "✓ Node.js:" $(node --version)  
echo "✓ Python:" $(python3 --version)
echo "✓ MySQL:" $(mysql --version | head -1)
echo "✓ Redis:" $(redis-cli --version)
echo "✓ Chrome:" $(google-chrome --version)

# Check Laravel installation
php artisan --version

# Check directories
ls -la scripts/node_modules/ | head -5
ls -la crawl-executives-scripts/
```

## 11. Troubleshooting

### Common Issues

#### Chrome/Puppeteer Issues
```bash
# Linux: Install missing dependencies
sudo apt install -y libnss3 libatk-bridge2.0-0 libdrm2 libxcomposite1 libxdamage1 libxrandr2 libgbm1 libxss1 libasound2

# macOS: Verify Chrome path
ls -la "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"

# Permission issues
chmod +x scripts/*.js
chmod +x crawl-executives-scripts/*.py
```

#### Database Connection Issues
```bash
# Check MySQL service
sudo systemctl status mysql

# Test connection manually
mysql -u root -p -e "SELECT VERSION();"

# Reset MySQL password nếu cần
sudo mysql
ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY 'newpassword';
FLUSH PRIVILEGES;
```

#### Queue Worker Issues
```bash
# Clear failed jobs
php artisan queue:clear

# Restart Redis
sudo systemctl restart redis

# Check queue status
php artisan queue:monitor
```

#### Python/Japanese Processing Issues
```bash
# Reinstall pykakasi
pip3 uninstall pykakasi
pip3 install pykakasi

# Check Python path
which python3
python3 --version

# Test encoding
python3 -c "print('テスト'.encode('utf-8'))"
```

### Performance Tuning

#### Memory Optimization
```env
# PHP memory limits
memory_limit=512M
max_execution_time=300

# Node.js heap size
NODE_OPTIONS="--max-old-space-size=4096"
```

#### Browser Resource Management
```javascript
// scripts/utils/browser.js optimization
const browser = await puppeteer.launch({
    headless: 'new',
    args: [
        '--no-sandbox',
        '--disable-dev-shm-usage',
        '--disable-gpu',
        '--memory-pressure-off',
        '--max_old_space_size=4096'
    ]
});
```

## 12. Monitoring & Maintenance

### Log Monitoring
```bash
# Laravel logs
tail -f storage/logs/laravel.log

# Queue worker logs
tail -f storage/logs/worker.log

# Crawling logs (from Node.js scripts)
tail -f scripts/logs/crawl.log
```

### Database Maintenance
```bash
# Regular maintenance
php artisan queue:prune-failed --hours=48
php artisan model:prune

# Backup
mysqldump -u root -p company_crawler > backup.sql
```

### Security Considerations
```bash
# Update dependencies regularly
composer update
npm update
pip3 list --outdated

# Monitor security advisories
composer audit
npm audit
```

## Kết luận

Sau khi hoàn thành setup guide này, bạn sẽ có:

✅ **Complete Development Environment**: PHP, Node.js, Python hoạt động cùng nhau  
✅ **Browser Automation**: Puppeteer với anti-detection capabilities  
✅ **Japanese Processing**: Advanced Romaji conversion với pykakasi  
✅ **Queue System**: Redis-backed background processing  
✅ **Database**: MySQL với comprehensive schema  
✅ **Admin Interface**: Filament-powered management system  
✅ **Monitoring**: Comprehensive logging và debugging tools  

Hệ thống Company Crawler giờ đã sẵn sàng cho việc thu thập thông tin công ty từ websites Nhật Bản và quốc tế với độ chính xác cao và khả năng xử lý quy mô lớn. 