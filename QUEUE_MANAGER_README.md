# Company Crawler Multi-Queue Manager

Comprehensive queue management script for the Company Crawler system with cross-platform support for Ubuntu and macOS.

## Features

✅ **Multi-Queue Support**: Manages multiple queue workers with different priorities  
✅ **Cross-Platform**: Works on both Ubuntu and macOS  
✅ **Process Management**: Automatic start, stop, restart, and cleanup  
✅ **Real-time Monitoring**: Live status and continuous watch mode  
✅ **Resource Monitoring**: Memory usage and system load tracking  
✅ **Browser Cleanup**: Automatic cleanup of hanging Chrome processes  
✅ **Colored Output**: Beautiful colored terminal output  

## Queue Configuration

The script manages 3 queue workers with different priorities:

| Worker | Queues | Memory | Timeout | Sleep | Priority |
|--------|--------|--------|---------|-------|----------|
| **High Priority** | `campaigns`, `company-list` | 1024MB | 30min | 1s | Highest |
| **Executives** | `executives` | 1024MB | 30min | 3s | Medium |
| **Default** | `default` | 512MB | 30min | 5s | Lowest |

## Usage

### Basic Commands

```bash
# Show help
./queue-manager.sh help

# Start all queue workers
./queue-manager.sh start

# Stop all queue workers  
./queue-manager.sh stop

# Restart all queue workers
./queue-manager.sh restart

# Show current status
./queue-manager.sh status

# Continuous monitoring (press Ctrl+C to stop)
./queue-manager.sh watch
```

### Installation

1. Make the script executable:
```bash
chmod +x queue-manager.sh
```

2. Ensure you're in the Laravel project root directory
3. Run the script with desired command

### Examples

#### Start Workers
```bash
./queue-manager.sh start
```

#### Check Status
```bash
./queue-manager.sh status
```

#### Continuous Monitoring
```bash
./queue-manager.sh watch
```

#### Stop Workers
```bash
./queue-manager.sh stop
```

#### Force Kill Workers
```bash
./queue-manager.sh kill
```
Use when normal stop doesn't work - forcefully kills all processes with SIGKILL.

#### Restart Workers
```bash
./queue-manager.sh restart
```

## Configuration

The script manages 3 queue workers:

- **High Priority**: campaigns, company-list (1024MB, 1s sleep)
- **Executives**: executives queue (1024MB, 3s sleep)  
- **Default**: default queue (512MB, 5s sleep)

## Troubleshooting

### Make executable:
```bash
chmod +x queue-manager.sh
```

### Manual commands:
```bash
# Clear failed jobs
php artisan queue:flush

# Retry failed jobs
php artisan queue:retry all
```

### Stuck processes:
If workers are unresponsive, use force kill:
```bash
./queue-manager.sh kill
```

## Compatibility

- ✅ macOS (bash/zsh)
- ✅ Ubuntu/Linux
- ✅ Laravel 8.x+

---

**Note**: This script replaces all previous individual queue scripts and provides unified queue management.
