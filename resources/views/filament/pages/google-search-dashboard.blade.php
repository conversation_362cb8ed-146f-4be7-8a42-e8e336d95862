<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <x-filament::section>
                <x-slot name="heading">
                    Total Companies
                </x-slot>
                <div class="text-3xl font-bold text-primary-600">
                    {{ number_format($stats['total_companies']) }}
                </div>
                <div class="text-sm text-gray-600">
                    Total companies in database
                </div>
            </x-filament::section>

            <x-filament::section>
                <x-slot name="heading">
                    With Website
                </x-slot>
                <div class="text-3xl font-bold text-success-600">
                    {{ number_format($stats['companies_with_website']) }}
                </div>
                <div class="text-sm text-gray-600">
                    {{ $stats['completion_rate'] }}% completion rate
                </div>
            </x-filament::section>

            <x-filament::section>
                <x-slot name="heading">
                    Need Google Search
                </x-slot>
                <div class="text-3xl font-bold text-warning-600">
                    {{ number_format($stats['companies_needing_search']) }}
                </div>
                <div class="text-sm text-gray-600">
                    Missing or invalid websites
                </div>
            </x-filament::section>

            <x-filament::section>
                <x-slot name="heading">
                    Found via Google
                </x-slot>
                <div class="text-3xl font-bold text-info-600">
                    {{ number_format($stats['google_search_found']) }}
                </div>
                <div class="text-sm text-gray-600">
                    {{ $stats['google_success_rate'] }}% Google success rate
                </div>
            </x-filament::section>

            <x-filament::section>
                <x-slot name="heading">
                    Actions
                </x-slot>
                <div class="space-y-2">
                    <x-filament::button 
                        wire:click="triggerBatchSearch"
                        size="sm"
                        color="primary"
                    >
                        🔍 Start Batch Search
                    </x-filament::button>
                    <x-filament::button 
                        wire:click="clearSearchCache"
                        size="sm"
                        color="warning"
                    >
                        🗑️ Clear Cache
                    </x-filament::button>
                </div>
            </x-filament::section>
        </div>

        <!-- Recent Google Searches -->
        <x-filament::section>
            <x-slot name="heading">
                Recent Google Search Results
            </x-slot>
            
            @if(count($recentSearches) > 0)
                <div class="overflow-x-auto">
                    <table class="w-full table-auto">
                        <thead>
                            <tr class="border-b">
                                <th class="text-left p-2">Company Name</th>
                                <th class="text-left p-2">Found Website</th>
                                <th class="text-left p-2">Previous</th>
                                <th class="text-left p-2">Found At</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($recentSearches as $search)
                                <tr class="border-b">
                                    <td class="p-2 font-medium">{{ $search['name'] }}</td>
                                    <td class="p-2">
                                        <a href="{{ $search['website'] }}" 
                                           target="_blank" 
                                           class="text-primary-600 hover:underline">
                                            {{ Str::limit($search['website'], 40) }}
                                        </a>
                                    </td>
                                    <td class="p-2 text-gray-500 text-sm">
                                        {{ $search['previous_website'] }}
                                    </td>
                                    <td class="p-2 text-sm text-gray-600">
                                        {{ $search['found_at'] }}
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="text-center py-8 text-gray-500">
                    No Google search results yet. Start a batch search to see results here.
                </div>
            @endif
        </x-filament::section>

        <!-- Companies Needing Search -->
        <x-filament::section>
            <x-slot name="heading">
                Companies Needing Google Search
            </x-slot>
            
            @if(count($companiesNeedingSearch) > 0)
                <div class="overflow-x-auto">
                    <table class="w-full table-auto">
                        <thead>
                            <tr class="border-b">
                                <th class="text-left p-2">Company Name</th>
                                <th class="text-left p-2">Current Website</th>
                                <th class="text-left p-2">Source URL</th>
                                <th class="text-left p-2">Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($companiesNeedingSearch as $company)
                                <tr class="border-b">
                                    <td class="p-2 font-medium">{{ $company['name'] }}</td>
                                    <td class="p-2 text-gray-500">
                                        {{ $company['website'] ?: 'null' }}
                                    </td>
                                    <td class="p-2">
                                        @if($company['source_url'])
                                            <a href="{{ $company['source_url'] }}" 
                                               target="_blank" 
                                               class="text-primary-600 hover:underline text-sm">
                                                {{ Str::limit($company['source_url'], 30) }}
                                            </a>
                                        @else
                                            <span class="text-gray-400">No source</span>
                                        @endif
                                    </td>
                                    <td class="p-2">
                                        <x-filament::button 
                                            size="xs"
                                            color="info"
                                            href="{{ route('filament.admin.resources.companies.edit', $company['id']) }}"
                                        >
                                            Edit
                                        </x-filament::button>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                
                @if(count($companiesNeedingSearch) >= 20)
                    <div class="mt-4 text-sm text-gray-600 text-center">
                        Showing first 20 companies. There are {{ $stats['companies_needing_search'] }} total companies needing search.
                    </div>
                @endif
            @else
                <div class="text-center py-8 text-gray-500">
                    🎉 All companies have valid websites!
                </div>
            @endif
        </x-filament::section>

        <!-- Daily Google Search Activity -->
        <x-filament::section>
            <x-slot name="heading">
                Daily Google Search Activity (Last 7 Days)
            </x-slot>
            
            @if(count($successfulSearches) > 0)
                <div class="space-y-2">
                    @foreach($successfulSearches as $day)
                        <div class="flex justify-between items-center p-2 bg-gray-50 rounded">
                            <span class="font-medium">{{ $day['date'] }}</span>
                            <span class="bg-primary-100 text-primary-800 px-2 py-1 rounded-full text-sm">
                                {{ $day['count'] }} websites found
                            </span>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-8 text-gray-500">
                    No Google search activity in the last 7 days.
                </div>
            @endif
        </x-filament::section>
    </div>
</x-filament-panels::page>
