@php
    $state = $getState();
    $budgetRemaining = (!empty($state['budget_remaining'])) ? $state['budget_remaining'] : 0;
    $amount = (!empty($state['amount'])) ? $state['amount'] : 0;
    $exceeds = $state['exceeds_budget'] ?? false;
    $allowOverdraft = $state['allow_overdraft'] ?? false;
@endphp

@if($exceeds && !$allowOverdraft)
<div class="bg-red-50 border-l-4 border-red-400 p-4 my-3">
    <div class="flex items-center gap-4">
        <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="red">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
        </div>
        <div class="ml-3">
            <p class="text-sm text-red">
                <strong>Cảnh báo:</strong> Số tiền đề xuất ({{ number_format($amount, 0, ',', '.') }} VND) vượt quá ngân sách còn lại ({{ number_format($budgetRemaining, 0, ',', '.') }} VND).
            </p>
            <p class="text-sm text-red mt-1">
                Vui lòng giảm số tiền đề xuất hoặc liên hệ với người quản lý để tăng hạn mức ngân sách.
            </p>
        </div>
    </div>
</div>
@elseif($exceeds && $allowOverdraft)
<div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 my-3">
    <div class="flex items-center gap-4">
        <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="#f59e0b">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
        </div>
        <div class="ml-3">
            <p class="text-sm text-amber-600">
                <strong>Lưu ý:</strong> Số tiền đề xuất ({{ number_format($amount, 0, ',', '.') }} VND) vượt quá ngân sách còn lại ({{ number_format($budgetRemaining, 0, ',', '.') }} VND).
            </p>
            <p class="text-sm text-amber-600 mt-1">
                Ngân sách này cho phép vượt hạn mức, bạn có thể tiếp tục gửi đề xuất này.
            </p>
        </div>
    </div>
</div>
@endif