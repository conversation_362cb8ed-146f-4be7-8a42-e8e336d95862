<div class="space-y-6">
    <!-- Company Information -->
    <div class="bg-gray-50 rounded-lg p-4">
        <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ $company->name }}</h3>
        <div class="text-sm text-gray-600">
            <p><strong>Website:</strong> {{ $company->website }}</p>
            <p><strong>Total Executives:</strong> {{ count($executives) }}</p>
        </div>
    </div>

    @if(empty($executives))
        <div class="text-center py-8">
            <div class="text-gray-400 text-lg mb-2">👥</div>
            <p class="text-gray-600">No executives found for this company.</p>
            <p class="text-sm text-gray-500 mt-2">Add executives manually or run the executive crawler first.</p>
        </div>
    @else
        <!-- Executives List -->
        <div class="space-y-4">
            <h4 class="text-md font-medium text-gray-900">Executives to Process:</h4>
            
            @php
                $executivesWithoutPredictions = 0;
                $executivesWithPredictions = 0;
                
                foreach ($executives as $executive) {
                    if (empty($executive['email_predictions'] ?? [])) {
                        $executivesWithoutPredictions++;
                    } else {
                        $executivesWithPredictions++;
                    }
                }
            @endphp
            
            <!-- Summary Stats -->
            <div class="grid grid-cols-3 gap-4 mb-4">
                <div class="bg-blue-50 rounded-lg p-3 text-center">
                    <div class="text-2xl font-bold text-blue-600">{{ $executivesWithoutPredictions }}</div>
                    <div class="text-sm text-blue-800">Need Predictions</div>
                </div>
                <div class="bg-green-50 rounded-lg p-3 text-center">
                    <div class="text-2xl font-bold text-green-600">{{ $executivesWithPredictions }}</div>
                    <div class="text-sm text-green-800">Have Predictions</div>
                </div>
                <div class="bg-gray-50 rounded-lg p-3 text-center">
                    <div class="text-2xl font-bold text-gray-600">{{ count($executives) }}</div>
                    <div class="text-sm text-gray-800">Total Executives</div>
                </div>
            </div>
            
            <!-- Executives Grid -->
            <div class="max-h-96 overflow-y-auto space-y-3">
                @foreach($executives as $index => $executive)
                    <div class="border rounded-lg p-4 {{ !empty($executive['email_predictions'] ?? []) ? 'bg-green-50 border-green-200' : 'bg-blue-50 border-blue-200' }}">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h5 class="font-medium text-gray-900">
                                    {{ $executive['name'] ?? 'Unknown Name' }}
                                </h5>
                                <p class="text-sm text-gray-600">{{ $executive['position'] ?? 'No position' }}</p>
                                
                                @if(!empty($executive['level']))
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 mt-1">
                                        {{ ucfirst(str_replace('_', '-', $executive['level'])) }}
                                    </span>
                                @endif
                                
                                @if(!empty($executive['name_romaji']))
                                    <p class="text-xs text-gray-500 mt-1">
                                        <strong>Romaji:</strong> {{ $executive['name_romaji'] }}
                                    </p>
                                @endif
                            </div>
                            
                            <!-- Prediction Status -->
                            <div class="ml-4 text-right">
                                @if(!empty($executive['email_predictions'] ?? []))
                                    <div class="flex items-center text-green-600">
                                        <svg class="w-5 h-5 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                        <span class="text-sm font-medium">{{ count($executive['email_predictions']) }} predictions</span>
                                    </div>
                                @else
                                    <div class="flex items-center text-blue-600">
                                        <svg class="w-5 h-5 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                        </svg>
                                        <span class="text-sm font-medium">No predictions</span>
                                    </div>
                                @endif
                            </div>
                        </div>
                        
                        <!-- Show existing predictions -->
                        @if(!empty($executive['email_predictions'] ?? []))
                            <div class="mt-3 pt-3 border-t border-green-200">
                                <div class="text-xs text-gray-600 mb-2">Current Predictions:</div>
                                <div class="flex flex-wrap gap-1">
                                    @foreach(array_slice($executive['email_predictions'], 0, 3) as $email)
                                        <span class="inline-block bg-white px-2 py-1 rounded text-xs font-mono text-gray-700 border">
                                            {{ $email }}
                                        </span>
                                    @endforeach
                                    @if(count($executive['email_predictions']) > 3)
                                        <span class="inline-block px-2 py-1 rounded text-xs text-gray-500">
                                            +{{ count($executive['email_predictions']) - 3 }} more
                                        </span>
                                    @endif
                                </div>
                            </div>
                        @endif
                    </div>
                @endforeach
            </div>
            
            @if($executivesWithoutPredictions > 0)
                <div class="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-blue-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                        </svg>
                        <div>
                            <div class="text-sm font-medium text-blue-800">
                                {{ $executivesWithoutPredictions }} executive(s) will get new email predictions
                            </div>
                            <div class="text-xs text-blue-600 mt-1">
                                Executives with existing predictions will be skipped. The system will generate 15+ email variations per executive using advanced AI patterns and Japanese name conversion.
                            </div>
                        </div>
                    </div>
                </div>
            @else
                <div class="mt-4 p-4 bg-amber-50 rounded-lg border border-amber-200">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-amber-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                        <div>
                            <div class="text-sm font-medium text-amber-800">
                                All executives already have email predictions
                            </div>
                            <div class="text-xs text-amber-600 mt-1">
                                No new predictions will be generated. Use the "Regenerate" action if you want to update existing predictions.
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    @endif
</div> 