@php
    $predictions = $getState() ?? [];
@endphp

@if(!empty($predictions))
    <div class="space-y-2">
        @foreach($predictions as $index => $email)
            <div class="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800 rounded-md">
                <div class="flex items-center space-x-2">
                    <span class="text-xs font-medium text-gray-500 dark:text-gray-400 bg-gray-200 dark:bg-gray-700 px-2 py-1 rounded">
                        {{ $index + 1 }}
                    </span>
                    <code class="text-sm font-mono text-gray-900 dark:text-gray-100">
                        {{ $email }}
                    </code>
                </div>
                <button 
                    type="button"
                    onclick="navigator.clipboard.writeText('{{ $email }}')"
                    class="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 focus:outline-none"
                    title="Copy to clipboard"
                >
                    Copy
                </button>
            </div>
        @endforeach
    </div>
@else
    <div class="text-sm text-gray-500 dark:text-gray-400 italic">
        No email predictions available
    </div>
@endif
