@php
    $executives = $executives ?? [];
    $company = $company ?? null;
@endphp

<div class="space-y-6">
    @if(!empty($executives))
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @foreach($executives as $index => $executive)
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-700">
                    {{-- Executive Header --}}
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                                {{ $executive['name'] ?? 'Unknown' }}
                            </h3>
                            @if(!empty($executive['name_romaji']))
                                <p class="text-sm text-gray-500 dark:text-gray-400 font-mono">
                                    {{ $executive['name_romaji'] }}
                                </p>
                            @endif
                        </div>
                        <span class="text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded-full">
                            #{{ $index + 1 }}
                        </span>
                    </div>

                    {{-- Position --}}
                    @if(!empty($executive['position']))
                        <div class="mb-4">
                            <div class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2V6"></path>
                                </svg>
                                {{ $executive['position'] }}
                            </div>
                        </div>
                    @endif

                    {{-- Level --}}
                    @if(!empty($executive['level']))
                        <div class="mb-4">
                            @php
                                $levelColors = [
                                    'c_level' => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
                                    'director' => 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
                                    'manager' => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
                                ];
                                $levelClass = $levelColors[$executive['level']] ?? 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
                            @endphp
                            <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium {{ $levelClass }}">
                                {{ ucfirst(str_replace('_', ' ', $executive['level'])) }}
                            </span>
                        </div>
                    @endif

                    {{-- Contact Info --}}
                    @if(!empty($executive['email']) || !empty($executive['phone']))
                        <div class="space-y-2 mb-4">
                            @if(!empty($executive['email']))
                                <div class="flex items-center text-sm">
                                    <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
                                    </svg>
                                    <a href="mailto:{{ $executive['email'] }}" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                                        {{ $executive['email'] }}
                                    </a>
                                </div>
                            @endif
                            @if(!empty($executive['phone']))
                                <div class="flex items-center text-sm">
                                    <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                    </svg>
                                    <a href="tel:{{ $executive['phone'] }}" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                                        {{ $executive['phone'] }}
                                    </a>
                                </div>
                            @endif
                        </div>
                    @endif

                    {{-- Email Predictions --}}
                    @if(!empty($executive['email_predictions']))
                        <div class="border-t border-gray-200 dark:border-gray-600 pt-4">
                            <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3 flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1H8a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                                Email Predictions ({{ count($executive['email_predictions']) }})
                            </h4>
                            <div class="space-y-2 max-h-32 overflow-y-auto">
                                @foreach($executive['email_predictions'] as $emailIndex => $email)
                                    <div class="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded text-xs">
                                        <div class="flex items-center space-x-2 flex-1 min-w-0">
                                            <span class="flex-shrink-0 w-5 h-5 bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200 rounded-full flex items-center justify-center text-xs font-medium">
                                                {{ $emailIndex + 1 }}
                                            </span>
                                            <code class="font-mono text-gray-900 dark:text-gray-100 truncate">{{ $email }}</code>
                                        </div>
                                        <button 
                                            type="button"
                                            onclick="navigator.clipboard.writeText('{{ $email }}').then(() => {
                                                this.textContent = 'Copied!';
                                                setTimeout(() => this.textContent = 'Copy', 2000);
                                            })"
                                            class="flex-shrink-0 ml-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 focus:outline-none transition-colors"
                                            title="Copy to clipboard"
                                        >
                                            Copy
                                        </button>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    {{-- Bio --}}
                    @if(!empty($executive['bio']))
                        <div class="border-t border-gray-200 dark:border-gray-600 pt-4 mt-4">
                            <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Biography</h4>
                            <p class="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
                                {{ $executive['bio'] }}
                            </p>
                        </div>
                    @endif
                </div>
            @endforeach
        </div>

        {{-- Summary --}}
        <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
            <div class="flex items-center">
                <svg class="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <div class="text-sm">
                    <span class="font-medium text-blue-900 dark:text-blue-100">{{ count($executives) }}</span>
                    <span class="text-blue-700 dark:text-blue-300">executives found with</span>
                    <span class="font-medium text-blue-900 dark:text-blue-100">
                        {{ collect($executives)->sum(fn($exec) => count($exec['email_predictions'] ?? [])) }}
                    </span>
                    <span class="text-blue-700 dark:text-blue-300">total email predictions</span>
                </div>
            </div>
        </div>
    @else
        <div class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No executives found</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                No executive information has been crawled for this company yet.
            </p>
        </div>
    @endif
</div>
