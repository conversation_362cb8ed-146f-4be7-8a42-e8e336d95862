<div class="space-y-6">
    <!-- Company Count Overview -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-8 w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-blue-600 dark:text-blue-400">Total Companies</p>
                    <p class="text-2xl font-bold text-blue-900 dark:text-blue-100">{{ number_format($companiesCount) }}</p>
                </div>
            </div>
        </div>

        <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-green-600 dark:text-green-400">Status</p>
                    <p class="text-lg font-bold text-green-900 dark:text-green-100">
                        @if($companiesCount > 0)
                            Active
                        @else
                            No Data
                        @endif
                    </p>
                </div>
            </div>
        </div>

        <div class="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg border border-purple-200 dark:border-purple-800">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-8 w-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-purple-600 dark:text-purple-400">Last Crawled</p>
                    <p class="text-sm font-bold text-purple-900 dark:text-purple-100">
                        @if($lastCrawled)
                            {{ $lastCrawled->format('M d, Y H:i') }}
                        @else
                            Never
                        @endif
                    </p>
                </div>
            </div>
        </div>
    </div>

    @if($companiesCount > 0)
        <!-- Latest and Oldest Companies -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            @if($latestCompany)
                <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                    <h4 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">Latest Company Added</h4>
                    <div class="space-y-2">
                        <p class="text-sm text-gray-600 dark:text-gray-400">Name:</p>
                        <p class="font-medium text-gray-900 dark:text-gray-100">{{ $latestCompany->name }}</p>
                        @if($latestCompany->website)
                            <p class="text-sm text-gray-600 dark:text-gray-400">Website:</p>
                            <a href="{{ $latestCompany->website }}" target="_blank" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm">
                                {{ $latestCompany->website }}
                            </a>
                        @endif
                        <p class="text-xs text-gray-500 dark:text-gray-400">
                            Added: {{ $latestCompany->created_at->format('M d, Y H:i') }}
                        </p>
                    </div>
                </div>
            @endif

            @if($oldestCompany && $oldestCompany->id !== $latestCompany?->id)
                <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                    <h4 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">First Company Added</h4>
                    <div class="space-y-2">
                        <p class="text-sm text-gray-600 dark:text-gray-400">Name:</p>
                        <p class="font-medium text-gray-900 dark:text-gray-100">{{ $oldestCompany->name }}</p>
                        @if($oldestCompany->website)
                            <p class="text-sm text-gray-600 dark:text-gray-400">Website:</p>
                            <a href="{{ $oldestCompany->website }}" target="_blank" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm">
                                {{ $oldestCompany->website }}
                            </a>
                        @endif
                        <p class="text-xs text-gray-500 dark:text-gray-400">
                            Added: {{ $oldestCompany->created_at->format('M d, Y H:i') }}
                        </p>
                    </div>
                </div>
            @endif
        </div>

        <!-- Data Source Info -->
        <div class="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
            <h4 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">Data Source Information</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                    <p class="text-gray-600 dark:text-gray-400">Source URL:</p>
                    <a href="{{ $dataSource->source_url }}" target="_blank" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 break-all">
                        {{ $dataSource->source_url }}
                    </a>
                </div>
                <div>
                    <p class="text-gray-600 dark:text-gray-400">Configuration:</p>
                    <div class="flex flex-wrap gap-1 mt-1">
                        @if($dataSource->has_pagination)
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                Pagination
                            </span>
                        @endif
                        @if($dataSource->has_company_links)
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                Company Links
                            </span>
                        @endif
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                            JavaScript
                        </span>
                        @if($dataSource->is_active)
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                Active
                            </span>
                        @else
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                Inactive
                            </span>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    @else
        <!-- No Companies Message -->
        <div class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No companies found</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">This data source hasn't been crawled yet or no companies were found.</p>
            <div class="mt-6">
                <p class="text-xs text-gray-400 dark:text-gray-500">
                    Use the crawler commands to start collecting company data from this source.
                </p>
            </div>
        </div>
    @endif
</div>
