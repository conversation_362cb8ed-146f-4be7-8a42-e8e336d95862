@php
    $totalJobs = $batch->totalJobs;
    $pendingJobs = $batch->pendingJobs;
    $failedJobs = $batch->failedJobs;
    $processedJobs = $totalJobs - $pendingJobs;
    $successfulJobs = $processedJobs - $failedJobs;
    $progressPercentage = $totalJobs > 0 ? round(($processedJobs / $totalJobs) * 100, 1) : 0;
    $successPercentage = $totalJobs > 0 ? round(($successfulJobs / $totalJobs) * 100, 1) : 0;
@endphp

<div class="space-y-6">
    <!-- Batch Overview -->
    <div class="grid grid-cols-2 gap-4">
        <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
            <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Batch ID</div>
            <div class="text-lg font-semibold text-gray-900 dark:text-gray-100 font-mono">{{ $batch->id }}</div>
        </div>
        <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
            <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Started At</div>
            <div class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {{ \Carbon\Carbon::createFromTimestamp($batch->createdAt)->format('M j, Y H:i:s') }}
            </div>
        </div>
    </div>

    <!-- Progress Bar -->
    <div class="space-y-2">
        <div class="flex justify-between text-sm">
            <span class="font-medium text-gray-700 dark:text-gray-300">Progress: {{ $processedJobs }}/{{ $totalJobs }} jobs</span>
            <span class="text-gray-500 dark:text-gray-400">{{ $progressPercentage }}%</span>
        </div>
        <div class="w-full bg-gray-200 rounded-full h-3 dark:bg-gray-700">
            <div class="bg-gradient-to-r from-blue-500 to-green-500 h-3 rounded-full transition-all duration-300" 
                 style="width: {{ $progressPercentage }}%"></div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-4 gap-4">
        <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ $totalJobs }}</div>
            <div class="text-sm text-blue-700 dark:text-blue-300">Total Jobs</div>
        </div>
        
        <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-green-600 dark:text-green-400">{{ $successfulJobs }}</div>
            <div class="text-sm text-green-700 dark:text-green-300">Successful</div>
        </div>
        
        <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{{ $pendingJobs }}</div>
            <div class="text-sm text-yellow-700 dark:text-yellow-300">Pending</div>
        </div>
        
        <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-red-600 dark:text-red-400">{{ $failedJobs }}</div>
            <div class="text-sm text-red-700 dark:text-red-300">Failed</div>
        </div>
    </div>

    <!-- Status Information -->
    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">Batch Status</h3>
        
        <div class="space-y-2">
            @if($batch->finished())
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                    <span class="text-green-700 dark:text-green-300 font-medium">Completed</span>
                    <span class="text-gray-500 dark:text-gray-400 ml-2">
                        at {{ \Carbon\Carbon::createFromTimestamp($batch->finishedAt)->format('M j, Y H:i:s') }}
                    </span>
                </div>
            @elseif($batch->cancelled())
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                    <span class="text-red-700 dark:text-red-300 font-medium">Cancelled</span>
                </div>
            @else
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-blue-500 rounded-full mr-2 animate-pulse"></div>
                    <span class="text-blue-700 dark:text-blue-300 font-medium">Processing...</span>
                </div>
            @endif
            
            @if($failedJobs > 0)
                <div class="text-sm text-gray-600 dark:text-gray-400">
                    <strong>Failed Jobs:</strong> {{ $failedJobs }} out of {{ $totalJobs }}
                </div>
            @endif
            
            <div class="text-sm text-gray-600 dark:text-gray-400">
                <strong>Success Rate:</strong> {{ $successPercentage }}%
            </div>
        </div>
    </div>

    <!-- Company Status Breakdown -->
    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">Company Status Breakdown</h3>
        
        @php
            $completedCompanies = $dataSource->companies()->where('status', 'completed')->count();
            $processingCompanies = $dataSource->companies()->where('status', 'processing')->count();
            $pendingCompanies = $dataSource->companies()->where('status', 'pending')->count();
            $failedCompanies = $dataSource->companies()->where('status', 'failed')->count();
        @endphp
        
        <div class="grid grid-cols-2 gap-4 text-sm">
            <div class="flex justify-between">
                <span>Completed:</span>
                <span class="font-medium text-green-600 dark:text-green-400">{{ $completedCompanies }}</span>
            </div>
            <div class="flex justify-between">
                <span>Processing:</span>
                <span class="font-medium text-yellow-600 dark:text-yellow-400">{{ $processingCompanies }}</span>
            </div>
            <div class="flex justify-between">
                <span>Pending:</span>
                <span class="font-medium text-gray-600 dark:text-gray-400">{{ $pendingCompanies }}</span>
            </div>
            <div class="flex justify-between">
                <span>Failed:</span>
                <span class="font-medium text-red-600 dark:text-red-400">{{ $failedCompanies }}</span>
            </div>
        </div>
    </div>

    <!-- Actions -->
    <div class="flex justify-center space-x-4">
        @if(!$batch->finished() && !$batch->cancelled())
            <button 
                onclick="location.reload()"
                class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
                Refresh Progress
            </button>
        @endif
        
        <a 
            href="{{ route('filament.admin.resources.companies.index', ['tableFilters[data_source_id][value]' => $dataSource->id]) }}"
            class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
        >
            View Companies
        </a>
    </div>
</div>
