<x-filament-panels::page.simple>
    @if (filament()->hasLogin())
        <x-slot name="logo">
            <x-filament-panels::logo />
        </x-slot>
    @endif

    @if (filament()->hasRegistration())
        <x-slot name="subheading">
            {{ __('filament-panels::pages/auth/login.actions.register.before') }}

            {{ $this->registerAction }}
        </x-slot>
    @endif

    {{ \Filament\Support\Facades\FilamentView::renderHook(\Filament\View\PanelsRenderHook::AUTH_LOGIN_FORM_BEFORE, scopes: $this->getRenderHookScopes()) }}

    <x-filament-panels::form id="form" wire:submit="authenticate">
        {{ $this->form }}

        <x-filament-panels::form.actions
            :actions="$this->getCachedFormActions()"
            :full-width="$this->hasFullWidthFormActions()"
        />
    </x-filament-panels::form>

    {{ \Filament\Support\Facades\FilamentView::renderHook(\Filament\View\PanelsRenderHook::AUTH_LOGIN_FORM_AFTER, scopes: $this->getRenderHookScopes()) }}

    <!-- Nút đăng nhập bằng Keycloak -->
    <div class="space-y-6 mt-6">
        <div class="relative">
            <div class="absolute inset-0 flex items-center">
                <span class="w-full border-t border-gray-300 dark:border-gray-700"></span>
            </div>
            <div class="relative flex justify-center text-sm">
                <span class="bg-white dark:bg-gray-900 px-2 text-gray-500 dark:text-gray-400">
                    {{ __('Hoặc đăng nhập bằng') }}
                </span>
            </div>
        </div>

        <div class="flex items-center justify-center">
            <x-filament::button
                href="{{ route('login.keycloak') }}"
                tag="a"
                outlined
                icon="heroicon-o-key"
            >
                DEHA Authenticator
            </x-filament::button>
        </div>
    </div>
</x-filament-panels::page.simple>
