#!/bin/bash
# Company Crawler Multi-Queue Manager
# Cross-platform queue management script for Ubuntu and macOS
# Usage: ./queue-manager.sh [start|stop|restart|status|watch]

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$SCRIPT_DIR"
LOG_FILE="$PROJECT_DIR/storage/logs/laravel.log"

# Queue configurations (using arrays instead of associative arrays for macOS compatibility)
QUEUE_NAMES=("high_priority" "executives" "default")
QUEUE_high_priority="campaigns,company-list"
QUEUE_executives="executives"
QUEUE_default="default"

SETTINGS_high_priority="--tries=2 --timeout=1800 --memory=1024 --sleep=1"
SETTINGS_executives="--tries=2 --timeout=1800 --memory=1024 --sleep=3"
SETTINGS_default="--tries=2 --timeout=1800 --memory=512 --sleep=5"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Detect OS
detect_os() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        echo "linux"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        echo "macos"
    else
        echo "unknown"
    fi
}

OS=$(detect_os)

# Get queue configuration by name
get_queue_list() {
    local name=$1
    case $name in
        "high_priority") echo "$QUEUE_high_priority" ;;
        "executives") echo "$QUEUE_executives" ;;
        "default") echo "$QUEUE_default" ;;
        *) echo "" ;;
    esac
}

get_queue_settings() {
    local name=$1
    case $name in
        "high_priority") echo "$SETTINGS_high_priority" ;;
        "executives") echo "$SETTINGS_executives" ;;
        "default") echo "$SETTINGS_default" ;;
        *) echo "" ;;
    esac
}

# Cross-platform process management
kill_processes() {
    local pattern="$1"
    local signal="${2:-TERM}"
    
    if [[ "$OS" == "linux" ]]; then
        pkill -$signal -f "$pattern" 2>/dev/null || true
    elif [[ "$OS" == "macos" ]]; then
        pkill -$signal -f "$pattern" 2>/dev/null || true
    fi
}

get_memory_info() {
    if [[ "$OS" == "linux" ]] && command -v free &> /dev/null; then
        free -h | grep '^Mem:' | awk '{print $3"/"$2}'
    elif [[ "$OS" == "macos" ]] && command -v vm_stat &> /dev/null; then
        vm_stat | grep "Pages free" | awk '{print int($3)*4096/1024/1024 "MB free"}'
    else
        echo "N/A"
    fi
}

# Print colored output
print_status() {
    echo -e "${BLUE}📊 Company Crawler Multi-Queue Manager${NC}"
    echo -e "${BLUE}=======================================${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${CYAN}ℹ️  $1${NC}"
}

# Check if we're in the correct directory
check_project_dir() {
    if [ ! -f "$PROJECT_DIR/artisan" ]; then
        print_error "Laravel artisan not found. Please run this script from the project root."
        exit 1
    fi
}

# Clear caches
clear_caches() {
    print_info "Clearing caches..."
    cd "$PROJECT_DIR"
    php artisan config:clear >/dev/null 2>&1
    php artisan cache:clear >/dev/null 2>&1
}

# Stop all queue workers
stop_workers() {
    print_info "Stopping existing queue workers..."
    kill_processes "queue:work"
    
    print_info "Cleaning up browser processes..."
    kill_processes "Chrome.*--remote-debugging-port"
    kill_processes "Chromium.*--remote-debugging-port"
    kill_processes "Google Chrome.*--remote-debugging-port"
    kill_processes "chrome.*--headless"
    kill_processes "node.*crawl-executives"
    
    # Wait for cleanup
    sleep 3
    print_success "All workers stopped"
}

# Force kill all queue workers (using SIGKILL)
force_kill_workers() {
    print_warning "Force killing all queue workers..."
    kill_processes "queue:work" "KILL"
    
    print_warning "Force killing browser processes..."
    kill_processes "Chrome.*--remote-debugging-port" "KILL"
    kill_processes "Chromium.*--remote-debugging-port" "KILL"
    kill_processes "Google Chrome.*--remote-debugging-port" "KILL"
    kill_processes "chrome.*--headless" "KILL"
    kill_processes "node.*crawl-executives" "KILL"
    
    # Additional cleanup for Laravel queue processes
    print_warning "Cleaning up PHP artisan processes..."
    kill_processes "php.*artisan.*queue" "KILL"
    
    # Wait for cleanup
    sleep 2
    print_success "All processes force killed"
}

# Start multi-queue workers
start_workers() {
    check_project_dir
    clear_caches
    
    cd "$PROJECT_DIR"
    
    echo ""
    print_info "Starting multi-queue workers..."
    
    # Start each queue worker
    local worker_count=0
    
    for queue_name in "${QUEUE_NAMES[@]}"; do
        local queue_list=$(get_queue_list "$queue_name")
        local settings=$(get_queue_settings "$queue_name")
        
        print_info "Starting $queue_name worker (queues: $queue_list)..."
        php artisan queue:work --queue="$queue_list" $settings &
        local pid=$!
        
        worker_count=$((worker_count + 1))
        echo -e "   ${GREEN}🔄 Worker $worker_count ($queue_name): PID $pid${NC}"
        
        sleep 2
    done
    
    echo ""
    print_success "All $worker_count workers started successfully!"
    echo ""
    print_info "Monitor with:"
    echo "   - Status: $0 status"
    echo "   - Watch: $0 watch"
    echo "   - Logs: tail -f $LOG_FILE"
    echo "   - Stop: $0 stop"
}

# Get queue status
get_queue_status() {
    cd "$PROJECT_DIR"
    
    print_status
    echo ""
    
    # Running workers
    echo -e "${PURPLE}🔄 Running Queue Workers:${NC}"
    local worker_count=0
    while IFS= read -r line; do
        if [ -n "$line" ]; then
            worker_count=$((worker_count + 1))
            echo -e "   ${GREEN}✅ Worker $worker_count: $line${NC}"
        fi
    done < <(ps aux | grep 'queue:work' | grep -v grep || true)
    
    if [ $worker_count -eq 0 ]; then
        echo -e "   ${RED}❌ No workers running${NC}"
    fi
    
    echo ""
    
    # Queue job counts
    echo -e "${PURPLE}📋 Queue Jobs Count:${NC}"
    local total_jobs=0
    for queue_name in "campaigns" "company-list" "executives" "default"; do
        local count
        count=$(php artisan tinker --execute="echo \\DB::table('jobs')->where('queue', '$queue_name')->count();" 2>/dev/null || echo "0")
        total_jobs=$((total_jobs + count))
        
        local icon="📋"
        case $queue_name in
            "campaigns") icon="🎯" ;;
            "company-list") icon="📋" ;;
            "executives") icon="👥" ;;
            "default") icon="🔄" ;;
        esac
        
        echo -e "   $icon $(printf '%-15s' "$queue_name:"): $count"
    done
    echo -e "   ${CYAN}📊 Total Jobs: $total_jobs${NC}"
    
    echo ""
    
    # Failed jobs
    echo -e "${PURPLE}⚡ Failed Jobs:${NC}"
    local failed_count
    failed_count=$(php artisan tinker --execute="echo \\DB::table('failed_jobs')->count();" 2>/dev/null || echo "0")
    
    if [ "$failed_count" -gt 0 ]; then
        echo -e "   ${RED}🔴 Total Failed: $failed_count${NC}"
    else
        echo -e "   ${GREEN}✅ No failed jobs${NC}"
    fi
    
    echo ""
    
    # System resources
    echo -e "${PURPLE}💾 System Resources:${NC}"
    echo -e "   Memory: $(get_memory_info)"
    echo -e "   Load: $(uptime | awk -F'load average:' '{print $2}' 2>/dev/null || echo "N/A")"
    
    echo ""
    
    # Quick commands
    echo -e "${PURPLE}🛠️  Quick Commands:${NC}"
    echo "   - Start workers: $0 start"
    echo "   - Restart workers: $0 restart"
    echo "   - Stop workers: $0 stop"
    echo "   - Watch continuously: $0 watch"
    echo "   - Clear failed: php artisan queue:flush"
    echo "   - Retry failed: php artisan queue:retry all"
}

# Watch queue status continuously
watch_queues() {
    print_info "Starting continuous queue monitoring..."
    print_warning "Press Ctrl+C to stop"
    echo ""
    
    while true; do
        clear
        get_queue_status
        echo ""
        echo -e "${CYAN}🕒 Last updated: $(date '+%H:%M:%S') | Refreshing in 20 seconds...${NC}"
        sleep 20
    done
}

# Show usage
show_usage() {
    echo "Company Crawler Multi-Queue Manager"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start     Start all queue workers"
    echo "  stop      Stop all queue workers"
    echo "  kill      Force kill all queue workers (SIGKILL)"
    echo "  restart   Restart all queue workers"
    echo "  status    Show current queue status"
    echo "  watch     Continuously monitor queues"
    echo "  help      Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start      # Start multi-queue workers"
    echo "  $0 watch      # Monitor queues in real-time"
    echo "  $0 restart    # Restart all workers"
    echo "  $0 kill       # Force kill stuck workers"
}

# Main script logic
main() {
    case "${1:-}" in
        "start")
            stop_workers
            start_workers
            ;;
        "stop")
            stop_workers
            ;;
        "kill")
            force_kill_workers
            ;;
        "restart")
            print_info "Restarting queue workers..."
            stop_workers
            clear_caches
            start_workers
            ;;
        "status")
            get_queue_status
            ;;
        "watch")
            watch_queues
            ;;
        "help"|"--help"|"-h")
            show_usage
            ;;
        *)
            print_error "Unknown command: $1"
            show_usage
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
