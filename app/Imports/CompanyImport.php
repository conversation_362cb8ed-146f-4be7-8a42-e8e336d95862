<?php

namespace App\Imports;

use App\Models\Company;
use App\Models\Blacklist;
use App\Models\DataSource;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\SkipsErrors;
use Maatwebsite\Excel\Concerns\SkipsFailures;
use Maatwebsite\Excel\Concerns\SkipsOnError;
use Maatwebsite\Excel\Concerns\SkipsOnFailure;

class CompanyImport implements ToModel, WithHeadingRow, WithValidation, SkipsOnError, SkipsOnFailure
{
    use Importable, SkipsErrors, SkipsFailures;

    private $importedCount = 0;
    private $skippedCount = 0;
    private $blacklistedCount = 0;
    private $customErrors = [];
    private $dataSourceId = null;

    public function __construct($dataSourceId = null)
    {
        $this->dataSourceId = $dataSourceId;
    }

    public function model(array $row)
    {
        try {
            // Get data source ID - use provided ID or fallback to Excel Import
            $dataSourceId = $this->dataSourceId;
            if ($dataSourceId === null) {
                $dataSource = DataSource::where('name', 'Excel Import')->first();
                if (!$dataSource) {
                    throw new \Exception('Excel Import data source not found. Please create it first.');
                }
                $dataSourceId = $dataSource->id;
            }
            // Skip rows without required name
            if (empty($row['name'])) {
                $this->skippedCount++;
                $this->customErrors[] = "Row skipped: Company name is required";
                return null;
            }

            $companyName = trim($row['name']);
            $website = isset($row['website']) ? trim($row['website']) : null;

            // Clean up website URL
            if ($website) {
                // Add protocol if missing
                if (!preg_match('/^https?:\/\//', $website)) {
                    $website = 'https://' . $website;
                }
                
                // Validate URL format
                if (!filter_var($website, FILTER_VALIDATE_URL)) {
                    $website = null; // Invalid URL, set to null
                }
            }

            // Check if company is blacklisted
            $domain = null;
            if ($website) {
                $domain = parse_url($website, PHP_URL_HOST);
            }
            
            if (Blacklist::isCompanyBlacklisted($companyName, $domain)) {
                $this->blacklistedCount++;
                $this->customErrors[] = "Company skipped (blacklisted): {$companyName}";
                return null;
            }

            // Check if company already exists
            $existing = Company::where('name', $companyName)->first();
            
            if ($existing) {
                // Update existing company if website is provided and current website is empty
                if ($website && !$existing->website) {
                    $existing->update(['website' => $website]);
                    $this->importedCount++;
                }
                return null; // Don't create new model
            }

            // Create new company with selected data source
            $company = Company::create([
                'name' => $companyName,
                'website' => $website,
                'status' => 'pending',
                'crawl_attempts' => 0,
                'data_source_id' => $dataSourceId,
            ]);
            
            $this->importedCount++;
            return $company;

        } catch (\Exception $e) {
            $this->skippedCount++;
            $this->customErrors[] = "Row error: " . $e->getMessage();
            return null;
        }
    }

    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'website' => 'nullable|string|max:255',
        ];
    }

    public function customValidationMessages()
    {
        return [
            'name.required' => 'Company name field is required',
            'name.string' => 'Company name must be a string',
            'name.max' => 'Company name cannot exceed 255 characters',
            'website.string' => 'Website must be a string',
            'website.max' => 'Website cannot exceed 255 characters',
        ];
    }

    public function getImportedCount(): int
    {
        return $this->importedCount;
    }

    public function getSkippedCount(): int
    {
        return $this->skippedCount;
    }

    public function getBlacklistedCount(): int
    {
        return $this->blacklistedCount;
    }

    public function getCustomErrors(): array
    {
        return $this->customErrors;
    }

    public function getImportSummary(): array
    {
        return [
            'imported' => $this->importedCount,
            'skipped' => $this->skippedCount,
            'blacklisted' => $this->blacklistedCount,
            'custom_errors' => $this->customErrors,
            'validation_failures' => $this->failures()->toArray(),
            'processing_errors' => $this->errors()->toArray(),
        ];
    }
} 