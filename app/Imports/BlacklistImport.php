<?php

namespace App\Imports;

use App\Models\Blacklist;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\SkipsErrors;
use Maatwebsite\Excel\Concerns\SkipsFailures;
use Maatwebsite\Excel\Concerns\SkipsOnError;
use Maatwebsite\Excel\Concerns\SkipsOnFailure;
use Maatwebsite\Excel\Validators\Failure;
use Maatwebsite\Excel\Validators\ValidationException;

class BlacklistImport implements ToModel, WithHeadingRow, WithValidation, SkipsOnError, SkipsOnFailure
{
    use Importable, SkipsErrors, SkipsFailures;

    private $importedCount = 0;
    private $skippedCount = 0;
    private $customErrors = []; // Renamed to avoid conflict with SkipsErrors trait
    
    public function __construct()
    {
        // Constructor cleaned up - no logs needed
    }

    public function model(array $row)
    {
        try {
            // Clean and normalize row data
            $row = array_map(function($value) {
                return is_string($value) ? trim($value) : $value;
            }, $row);
            
            // Map CSV headers to expected field names (case-insensitive)
            $mappedRow = [];
            foreach ($row as $key => $value) {
                $normalizedKey = strtolower(str_replace(' ', '_', $key));
                $mappedRow[$normalizedKey] = $value;
            }
            
            // Use mapped row for processing
            $row = $mappedRow;

            // Skip completely empty rows
            if (empty(array_filter($row))) {
                $this->skippedCount++;
                $this->customErrors[] = "Row skipped: Empty row";
                return null;
            }

            // Skip rows without required associated_company
            if (empty($row['associated_company'])) {
                $this->skippedCount++;
                $this->customErrors[] = "Row skipped: Associated Company is required";
                return null;
            }

            // All validation removed except required associated_company

            // Check if record already exists (by email or code)
            $existing = null;
            if (!empty($row['email'])) {
                $existing = Blacklist::where('email', $row['email'])->first();
            } elseif (!empty($row['code'])) {
                $existing = Blacklist::where('code', $row['code'])->first();
            }

            if ($existing) {
                // Update existing record
                $existing->update([
                    'code' => $row['code'] ?? $existing->code,
                    'name' => $row['name'] ?? $existing->name,
                    'email' => $row['email'] ?? $existing->email,
                    'associated_company' => $row['associated_company'],
                    'website' => $row['website'] ?? $existing->website,
                    'company_name' => $row['company_name'] ?? $existing->company_name,
                    'domain' => $row['domain'] ?? $existing->domain,
                    'reason' => $this->validateReason($row['reason'] ?? $existing->reason ?? 'manual_request'),
                    'note' => $row['note'] ?? $existing->note,
                    'is_active' => $this->parseBoolean($row['is_active'] ?? $existing->is_active),
                    'added_by' => auth()->id() ?? $existing->added_by,
                    'last_update' => now(),
                ]);
                $this->importedCount++;
                return null; // Don't create new model
            }

            // Create new record
            $this->importedCount++;
            $newRecord = new Blacklist([
                'code' => $row['code'] ?? null, // Now can have duplicate codes
                'name' => $row['name'] ?? null,
                'email' => $row['email'] ?? null,
                'associated_company' => $row['associated_company'],
                'website' => $row['website'] ?? null,
                'company_name' => $row['company_name'] ?? $row['associated_company'], // Use associated_company if company_name missing
                'domain' => $row['domain'] ?? null,
                'reason' => $this->validateReason($row['reason'] ?? 'manual_request'),
                'note' => $row['note'] ?? null,
                'is_active' => $this->parseBoolean($row['is_active'] ?? true),
                'added_by' => auth()->id(),
                'last_update' => now(),
            ]);
            
            return $newRecord;

        } catch (\Exception $e) {
            $this->skippedCount++;
            $errorMessage = "Row error: " . $e->getMessage();
            $this->customErrors[] = $errorMessage;
            
            \Log::error('BlacklistImport row processing error', [
                'error_message' => $e->getMessage(),
                'error_code' => $e->getCode(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'row_data' => $row ?? [],
                'stack_trace' => $e->getTraceAsString()
            ]);
            
            return null;
        }
    }

    private function validateReason($reason): string
    {
        $validReasons = ['duplicate', 'invalid', 'not_target', 'manual_request'];
        return in_array($reason, $validReasons) ? $reason : 'manual_request';
    }

    private function parseBoolean($value): bool
    {
        if (is_bool($value)) {
            return $value;
        }
        
        if (is_string($value)) {
            $value = strtolower(trim($value));
            return in_array($value, ['true', '1', 'yes', 'on', 'active']);
        }
        
        return (bool) $value;
    }

    public function rules(): array
    {
        return [
            'associated_company' => 'required',
            // Remove all other validation rules - accept any data format
        ];
    }

    public function customValidationMessages()
    {
        return [
            'associated_company.required' => 'Associated Company field is required',
        ];
    }

    public function getImportedCount(): int
    {
        return $this->importedCount;
    }

    public function getSkippedCount(): int
    {
        return $this->skippedCount;
    }

    public function getCustomErrors(): array
    {
        return $this->customErrors;
    }

    public function getImportSummary(): array
    {
        try {
            $failures = $this->failures()->toArray();
            $errors = $this->errors()->toArray();
            
            // Convert errors to array format safely
            $processedErrors = [];
            foreach ($errors as $error) {
                if (is_array($error)) {
                    $processedErrors[] = $error;
                } else {
                    // Handle non-array errors (like Exception objects)
                    $processedErrors[] = [
                        'message' => is_object($error) ? $error->getMessage() : (string)$error,
                        'type' => is_object($error) ? get_class($error) : 'unknown'
                    ];
                }
            }
            
            // Debug log removed for cleaner output
            
            return [
                'imported' => $this->importedCount,
                'skipped' => $this->skippedCount,
                'custom_errors' => $this->customErrors,
                'validation_failures' => $failures,
                'processing_errors' => $processedErrors,
            ];
        } catch (\Exception $e) {
            \Log::error('BlacklistImport getImportSummary error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return [
                'imported' => $this->importedCount,
                'skipped' => $this->skippedCount,
                'custom_errors' => array_merge($this->customErrors, ['Summary generation error: ' . $e->getMessage()]),
                'validation_failures' => [],
                'processing_errors' => [],
            ];
        }
    }
} 