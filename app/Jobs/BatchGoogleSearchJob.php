<?php

namespace App\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Bus\Batchable;
use App\Models\Company;
use Illuminate\Support\Facades\Log;
use Filament\Notifications\Notification;
use Symfony\Component\Process\Process;

class BatchGoogleSearchJob implements ShouldQueue
{
    use Queueable, InteractsWithQueue, SerializesModels, Batchable;

    public int $timeout = 3600; // 1 hour timeout
    public int $tries = 3;

    private int $batchSize;
    private ?int $userId;

    /**
     * Create a new job instance.
     */
    public function __construct(int $batchSize = 50, ?int $userId = null)
    {
        $this->batchSize = $batchSize;
        $this->userId = $userId;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info("🚀 Starting batch Google search job", [
            'batch_size' => $this->batchSize,
            'user_id' => $this->userId,
        ]);

        // Find companies without valid websites
        $companies = Company::query()
            ->where(function ($query) {
                $query->whereNull('website')
                    ->orWhere('website', '')
                    ->orWhere('website', 'like', '%example%')
                    ->orWhere('website', '#')
                    ->orWhere('website', 'javascript:')
                    ->orWhere('website', 'mailto:');
            })
            ->limit($this->batchSize)
            ->get();

        if ($companies->isEmpty()) {
            Log::info("✅ No companies found needing Google search");
            $this->sendNotification("✅ All companies already have valid websites!", 'success');
            return;
        }

        Log::info("🔍 Found {$companies->count()} companies needing Google search");

        $processed = 0;
        $successful = 0;
        $failed = 0;
        $cached = 0;

        foreach ($companies as $company) {
            try {
                Log::info("🔍 Searching for company: {$company->name}");

                // Check if already cached
                $cacheKey = 'google_search_' . md5($company->name);
                if (cache()->has($cacheKey)) {
                    $cachedResult = cache()->get($cacheKey);
                    if ($cachedResult) {
                        $company->update([
                            'website' => $cachedResult,
                            'crawl_metadata' => array_merge($company->crawl_metadata ?? [], [
                                'google_search_found' => true,
                                'google_search_cached' => true,
                                'google_search_at' => now()->toISOString(),
                                'previous_website' => $company->website,
                            ])
                        ]);
                        $cached++;
                        $successful++;
                        Log::info("📦 Used cached result for {$company->name}: {$cachedResult}");
                    }
                } else {
                    // Perform Google search using JavaScript script
                    $foundWebsite = $this->executeJavaScriptGoogleSearch($company->name);

                    if ($foundWebsite) {
                        // Cache the result
                        cache()->put($cacheKey, $foundWebsite, now()->addHours(24));
                        
                        $company->update([
                            'website' => $foundWebsite,
                            'crawl_metadata' => array_merge($company->crawl_metadata ?? [], [
                                'google_search_found' => true,
                                'google_search_cached' => false,
                                'google_search_method' => 'javascript',
                                'google_search_at' => now()->toISOString(),
                                'previous_website' => $company->website,
                            ])
                        ]);

                        $successful++;
                        Log::info("✅ Found website for {$company->name}: {$foundWebsite}");
                    } else {
                        $failed++;
                        Log::warning("❌ No website found for {$company->name}");
                    }

                    // Rate limiting between searches
                    sleep(3); // Slightly longer delay for JS script
                }

                $processed++;

                // Send progress notification every 10 companies
                if ($processed % 10 === 0) {
                    $this->sendProgressNotification($processed, $companies->count(), $successful);
                }

            } catch (\Exception $e) {
                $failed++;
                Log::error("❌ Error searching for {$company->name}: " . $e->getMessage());
                continue;
            }
        }

        // Final summary
        $summary = [
            'total_processed' => $processed,
            'successful_searches' => $successful,
            'failed_searches' => $failed,
            'cached_results' => $cached,
            'success_rate' => $processed > 0 ? round(($successful / $processed) * 100, 1) : 0,
        ];

        Log::info("🏁 Batch Google search completed", $summary);
        $this->sendCompletionNotification($summary);
    }

    /**
     * Execute JavaScript Google search script
     */
    private function executeJavaScriptGoogleSearch(string $companyName): ?string
    {
        $scriptPath = base_path('scripts/google-search.js');
        $outputPath = storage_path('app/google-search-' . md5($companyName) . '.json');

        $command = [
            'node',
            $scriptPath,
            '--query', $companyName,
            '--output', $outputPath
        ];

        try {
            Log::debug("🔍 Executing Google search script", [
                'company' => $companyName,
                'script' => $scriptPath
            ]);

            $process = new Process($command);
            $process->setTimeout(60); // 1 minute timeout
            $process->run();

            if (!$process->isSuccessful()) {
                Log::warning("❌ Google search script failed", [
                    'company' => $companyName,
                    'exit_code' => $process->getExitCode(),
                    'error' => $process->getErrorOutput()
                ]);
                return null;
            }

            // Parse JSON output from stdout
            $output = trim($process->getOutput());
            if ($output) {
                $result = json_decode($output, true);
                
                if ($result && isset($result['success']) && $result['success'] && isset($result['website'])) {
                    Log::info("✅ Google search script found website", [
                        'company' => $companyName,
                        'website' => $result['website']
                    ]);
                    
                    return $result['website'];
                }
            }

            Log::debug("ℹ️ Google search script completed but no website found", [
                'company' => $companyName,
                'output' => $output
            ]);

            return null;

        } catch (\Exception $e) {
            Log::error("❌ Error executing Google search script", [
                'company' => $companyName,
                'error' => $e->getMessage()
            ]);
            return null;
        } finally {
            // Clean up output file
            if (file_exists($outputPath)) {
                unlink($outputPath);
            }
        }
    }

    /**
     * Send progress notification
     */
    private function sendProgressNotification(int $processed, int $total, int $successful): void
    {
        if (!$this->userId) return;

        $progress = round(($processed / $total) * 100, 1);
        
        Notification::make()
            ->info()
            ->title('Google Search Progress')
            ->body("Processed {$processed}/{$total} companies ({$progress}%). Found {$successful} websites so far.")
            ->sendToDatabase(\App\Models\User::find($this->userId));
    }

    /**
     * Send completion notification
     */
    private function sendCompletionNotification(array $summary): void
    {
        if (!$this->userId) return;

        $title = $summary['successful_searches'] > 0 ? 
            '✅ Batch Google Search Completed!' : 
            '⚠️ Batch Google Search Completed with No Results';

        $body = "Processed {$summary['total_processed']} companies. " .
                "Found {$summary['successful_searches']} websites " .
                "({$summary['cached_results']} from cache). " .
                "Success rate: {$summary['success_rate']}%";

        Notification::make()
            ->success()
            ->title($title)
            ->body($body)
            ->sendToDatabase(\App\Models\User::find($this->userId));
    }

    /**
     * Send general notification
     */
    private function sendNotification(string $message, string $type = 'info'): void
    {
        if (!$this->userId) return;

        Notification::make()
            ->$type()
            ->title('Google Search Job')
            ->body($message)
            ->sendToDatabase(\App\Models\User::find($this->userId));
    }

    /**
     * Handle job failure
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("❌ Batch Google search job failed", [
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
            'batch_size' => $this->batchSize,
            'user_id' => $this->userId,
        ]);

        if ($this->userId) {
            Notification::make()
                ->danger()
                ->title('❌ Google Search Job Failed')
                ->body("Batch Google search failed: " . $exception->getMessage())
                ->sendToDatabase(\App\Models\User::find($this->userId));
        }
    }
}
