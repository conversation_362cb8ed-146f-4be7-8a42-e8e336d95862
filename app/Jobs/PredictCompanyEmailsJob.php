<?php

namespace App\Jobs;

use App\Models\Company;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Filament\Notifications\Notification;

class PredictCompanyEmailsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 2;
    public $timeout = 300; // 5 minutes timeout
    public $backoff = [60, 120]; // Retry after 1 min, then 2 min

    protected Company $company;
    protected ?int $userId;
    protected bool $overwriteExisting;

    /**
     * Create a new job instance.
     */
    public function __construct(Company $company, ?int $userId = null, bool $overwriteExisting = true)
    {
        $this->company = $company;
        $this->userId = $userId;
        $this->overwriteExisting = $overwriteExisting;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info("🔮 Starting email prediction job for company: {$this->company->name}");

        try {
            $result = $this->predictEmailsForCompany($this->company);

            if ($result['success']) {
                $this->sendSuccessNotification($result);
                Log::info("✅ Email prediction completed for {$this->company->name}", $result);
            } else {
                $this->sendErrorNotification($result['error']);
                Log::warning("⚠️ Email prediction failed for {$this->company->name}: {$result['error']}");
            }
        } catch (\Exception $e) {
            Log::error("❌ Email prediction job failed for {$this->company->name}: " . $e->getMessage(), [
                'company_id' => $this->company->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            $this->sendErrorNotification($e->getMessage());
            throw $e; // Re-throw to trigger retry mechanism
        }
    }

    /**
     * Handle job failure
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("❌ Email prediction job permanently failed for {$this->company->name}", [
            'company_id' => $this->company->id,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts()
        ]);

        $this->sendErrorNotification("Job failed after {$this->attempts()} attempts: " . $exception->getMessage());
    }

    /**
     * Predict emails for company executives
     */
    private function predictEmailsForCompany(Company $company): array
    {
        $executives = $company->executives ?? [];
        $website = $company->website;

        if (empty($executives)) {
            return [
                'success' => false,
                'error' => 'No executives found for this company',
                'predictions_generated' => 0
            ];
        }

        if (empty($website)) {
            return [
                'success' => false,
                'error' => 'No website found for this company',
                'predictions_generated' => 0
            ];
        }

        // Extract domain from website
        $domain = parse_url($website, PHP_URL_HOST);
        if (!$domain) {
            return [
                'success' => false,
                'error' => 'Could not extract domain from website URL',
                'predictions_generated' => 0
            ];
        }

        $predictionsGenerated = 0;
        $skippedCount = 0;
        $errorCount = 0;
        $noValidationCount = 0;
        $updatedExecutives = [];

        foreach ($executives as $index => $executive) {
            $executiveName = trim($executive['name'] ?? '');

            if (empty($executiveName)) {
                $skippedCount++;
                $updatedExecutives[] = $executive;
                continue;
            }

            // Skip if already has predictions and not overwriting
            if (!$this->overwriteExisting && !empty($executive['email_predictions'])) {
                $skippedCount++;
                $updatedExecutives[] = $executive;
                continue;
            }

            try {
                Log::info("🔍 Generating email predictions for executive: {$executiveName}");

                // Generate raw predictions first
                $rawPredictions = $this->callPythonEmailPredictionRaw($executiveName, $domain);

                // Nếu không có predictions từ Python, KHÔNG fallback sang PHP nữa
                if (empty($rawPredictions)) {
                    $executive['email_predictions'] = [];
                } else {
                    $executive['email_predictions'] = $rawPredictions;
                }

                // Validate predictions with API
                $validatedPredictions = $this->validateEmailsWithAPI($executive['email_predictions'], $executiveName);

                if (!empty($validatedPredictions)) {
                    $executive['email_predictions'] = $validatedPredictions;
                    $predictionsGenerated++;
                    Log::info("✅ Generated " . count($validatedPredictions) . " validated predictions for {$executiveName}");
                } else {
                    $executive['email_predictions'] = [];
                    $errorCount++;
                    Log::warning("❌ Failed to generate any predictions for {$executiveName}");
                }

                $updatedExecutives[] = $executive;
            } catch (\Exception $e) {
                Log::error("Failed to generate predictions for executive", [
                    'executive_name' => $executiveName,
                    'company' => $company->name,
                    'error' => $e->getMessage()
                ]);

                $errorCount++;
                $updatedExecutives[] = $executive;
            }
        }

        // Update company with new executives data
        $company->update(['executives' => $updatedExecutives]);

        return [
            'success' => true,
            'predictions_generated' => $predictionsGenerated,
            'executives_processed' => count($executives),
            'skipped_count' => $skippedCount,
            'error_count' => $errorCount,
            'no_validation_count' => $noValidationCount
        ];
    }

    /**
     * Call Python email prediction script (raw, no validation)
     */
    private function callPythonEmailPredictionRaw(string $name, string $domain): array
    {
        try {
            if (!$name || !$domain || trim($name) === '' || trim($domain) === '') {
                return [];
            }

            // Đảm bảo xác định đúng đường dẫn scriptsPath và pythonPath
            $scriptsPath = base_path('crawl-executives-scripts');
            $pythonPath = $scriptsPath . '/.venv/bin/python3';

            // Clean the name and domain for command (prevent injection)
            $cleanName = str_replace('"', '\"', $name);
            $cleanDomain = str_replace('"', '\"', $domain);

            // Lệnh gọi Python script với --no-validation để tránh SMTP validation
            // Redirect stderr to /dev/null to avoid log messages mixing with JSON
            $command = 'cd "' . $scriptsPath . '" && "' . $pythonPath . '" generate_email_predictions.py "' . $cleanName . '" "' . $cleanDomain . '" --no-validation 2>/dev/null';

            // Log command trước khi chạy
            Log::info('Python command', ['command' => $command]);
            $output = shell_exec($command);
            Log::info('Python output', ['output' => $output]);

            // Parse JSON response (output should be clean now with stderr redirected)
            $jsonResponse = json_decode(trim($output), true);

            if ($jsonResponse === null || json_last_error() !== JSON_ERROR_NONE) {
                Log::error("Invalid JSON output from Python script", [
                    'name' => $name,
                    'domain' => $domain,
                    'output' => $output,
                    'json_error' => json_last_error_msg()
                ]);
                throw new \Exception("Invalid JSON output from Python script");
            }

            $predictions = $jsonResponse['predictions'] ?? [];
            $allEmails = [];

            foreach ($predictions as $prediction) {
                if (is_array($prediction) && !empty($prediction['email'])) {
                    // When using --no-validation, is_valid is null, so we accept all emails
                    // When validation is enabled, only accept emails with is_valid: true
                    if (!isset($prediction['is_valid']) || $prediction['is_valid'] === null || $prediction['is_valid'] === true) {
                        $allEmails[] = $prediction['email'];
                    }
                } elseif (is_string($prediction) && !empty($prediction)) {
                    // Fallback for string format
                    $allEmails[] = $prediction;
                }
            }

            return $allEmails;
        } catch (\Exception $e) {
            Log::error("Python email generation failed for \"{$name}\": " . $e->getMessage(), [
                'domain' => $domain,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Fallback email prediction using PHP
     */
    private function fallbackEmailPredictionRaw(string $name, string $domain): array
    {
        try {
            Log::info("🔄 Using PHP fallback for email prediction: {$name}");

            $emailService = new \App\Services\ExecutiveEmailService();
            $predictions = $emailService->generateEmailPredictions($name, $domain);

            return $predictions;
        } catch (\Exception $e) {
            Log::error("Fallback email prediction failed", [
                'name' => $name,
                'domain' => $domain,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Validate emails with external API
     */
    private function validateEmailsWithAPI(array $emails, string $executiveName): array
    {
        try {
            Log::info("🔍 Running API email validation for \"{$executiveName}\" on " . count($emails) . " emails");

            $apiUrl = 'http://14.224.203.110:3472/api/validate-emails';
            $apiKey = 'SrmnTK1itOO8vJzEVFQiEG9kltXHxG9B';

            // Prepare curl command with proper escaping (matching CompanyResource)
            $payload = json_encode(['emails' => $emails]);
            $escapedPayload = str_replace('"', '\\"', $payload);

            $command = sprintf(
                'curl -X POST "%s" -H "Content-Type: application/json" -H "X-API-Key: %s" -d "%s" --connect-timeout 30 --max-time 60 --silent',
                $apiUrl,
                $apiKey,
                $escapedPayload
            );

            $response = shell_exec($command);

            if ($response === null) {
                Log::error("❌ API validation failed for \"{$executiveName}\": curl command failed");
                Log::warning("⚠️ Skipping API validation, returning original emails");
                return $emails; // Return original emails if API fails
            }

            // Parse API response
            $result = json_decode(trim($response), true);

            if (json_last_error() !== JSON_ERROR_NONE || !isset($result['results'])) {
                Log::error("❌ Invalid API response format for \"{$executiveName}\"", [
                    'response' => $result,
                    'json_error' => json_last_error_msg()
                ]);
                Log::warning("⚠️ Skipping API validation, returning original emails");
                return $emails; // Return original emails if parsing fails
            }

            // Debug: Log raw API response
            Log::info("🔍 Raw API response for \"{$executiveName}\":", [
                'response' => $result,
                'results_count' => count($result['results'] ?? [])
            ]);

            // Extract valid emails (matching CompanyResource logic)
            $validatedEmails = [];
            if (isset($result['results'])) {
                foreach ($result['results'] as $resultItem) {
                    Log::info("🔍 Validating email: " . $resultItem['email'] . " - is_valid: " . ($resultItem['is_valid'] ? 'true' : 'false'));

                    // Chỉ lưu email thực sự hợp lệ (is_valid === true)
                    if (isset($resultItem['is_valid']) && $resultItem['is_valid'] === true) {
                        $validatedEmails[] = $resultItem['email'];
                        Log::info("✅ Email valid: " . $resultItem['email']);
                    } else {
                        Log::warning("❌ Email invalid: " . $resultItem['email'] . " - reason: " . ($resultItem['reason'] ?? 'unknown'));
                    }
                }
            }

            // Log tổng kết validation
            Log::info("📊 Validation summary: " . count($validatedEmails) . " valid out of " . count($emails) . " total emails");

            return $validatedEmails;
        } catch (\Exception $e) {
            Log::error("❌ Failed to validate emails via API: " . $e->getMessage(), [
                'executive' => $executiveName,
                'emails_count' => count($emails),
                'error' => $e->getMessage()
            ]);
            Log::warning("⚠️ Skipping API validation, returning original emails");
            return $emails; // Return original emails if validation fails
        }
    }

    /**
     * Send success notification
     */
    private function sendSuccessNotification(array $result): void
    {
        if (!$this->userId) {
            return;
        }

        // Đếm tổng số email hợp lệ đã dự đoán được cho toàn bộ executives
        $executives = $this->company->executives ?? [];
        $totalValidEmails = 0;
        foreach ($executives as $executive) {
            if (!empty($executive['email_predictions']) && is_array($executive['email_predictions'])) {
                $totalValidEmails += count($executive['email_predictions']);
            }
        }

        $title = "Email Predictions Generated";
        $body = sprintf(
            "Successfully generated predictions for %s.\nProcessed: %d executives\nExecutives with valid emails: %d\nTotal valid emails: %d\nSkipped: %d",
            $this->company->name,
            $result['executives_processed'],
            $result['predictions_generated'],
            $totalValidEmails,
            $result['skipped_count']
        );

        if ($result['no_validation_count'] > 0) {
            $body .= "\nNo valid emails: {$result['no_validation_count']}";
        }

        Notification::make()
            ->title($title)
            ->body($body)
            ->success()
            ->duration(10000)
            ->sendToDatabase(\App\Models\User::find($this->userId));
    }

    /**
     * Send error notification
     */
    private function sendErrorNotification(string $error): void
    {
        if (!$this->userId) {
            return;
        }

        Notification::make()
            ->title("Email Prediction Failed")
            ->body("Failed to generate email predictions for {$this->company->name}: {$error}")
            ->danger()
            ->duration(15000)
            ->sendToDatabase(\App\Models\User::find($this->userId));
    }
}
