<?php

namespace App\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Bus\Batchable;
use App\Models\DataSource;
use App\Models\Company;
use App\Models\Blacklist;
use App\Models\CrawlerLog;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Filament\Notifications\Notification;
use Symfony\Component\Process\Process;
use Symfony\Component\Process\Exception\ProcessTimedOutException;

class CrawlCompanyListJob implements ShouldQueue
{
    use Queueable, InteractsWithQueue, SerializesModels, Batchable;

    public DataSource $dataSource;
    public int $maxPages;
    public int $maxCompanies;
    public ?int $userId;
    
    public int $timeout = 1800; // 30 minutes
    public int $tries = 2;

    public function __construct(DataSource $dataSource, int $maxPages = 5, int $maxCompanies = 50, ?int $userId = null)
    {
        $this->dataSource = $dataSource;
        $this->maxPages = $maxPages;
        $this->maxCompanies = $maxCompanies;
        $this->userId = $userId;
        
        // Use high priority queue for company list crawling
        $this->onQueue('company-list');
    }

    public function middleware(): array
    {
        return [
            new WithoutOverlapping($this->dataSource->id)
        ];
    }

    public function handle(): void
    {
        $cacheKey = "crawl_progress_{$this->dataSource->id}";
        
        // Set crawling status to true
        $this->dataSource->update(['is_crawling' => true]);
        
        $this->updateProgress($cacheKey, 0, 'Starting crawl...');
        
        $log = CrawlerLog::create([
            'type' => 'company_list',
            'status' => 'started',
            'target_url' => $this->dataSource->source_url,
            'crawlable_type' => DataSource::class,
            'crawlable_id' => $this->dataSource->id,
            'started_at' => now(),
        ]);

        try {
            Log::info("Starting company list crawl for: {$this->dataSource->name}");
            
            $this->updateProgress($cacheKey, 10, 'Preparing JavaScript crawler...');

            // Determine max pages to crawl
            $maxPages = $this->determineMaxPagesToCrawl();
            
            $this->updateProgress($cacheKey, 20, 'Starting JavaScript crawl...');
            
            // Execute JavaScript crawler
            $result = $this->executeJavaScriptCrawler($maxPages);
            
            if (!$result['success']) {
                throw new \Exception($result['error'] ?? 'JavaScript crawler failed');
            }

            $this->updateProgress($cacheKey, 30, "Crawled {$result['pages_crawled']} pages, processing companies...", [
                'pages_crawled' => $result['pages_crawled'],
                'companies_found' => count($result['companies']),
            ]);
            
            // Process companies
            $processed = $this->processCompanies($result['companies'], $cacheKey);

            $this->updateProgress($cacheKey, 95, 'Finalizing...');

            // Update data source
            $this->dataSource->update([
                'last_crawled_at' => now(),
                'is_crawling' => false
            ]);

            $log->update([
                'status' => 'completed',
                'completed_at' => now(),
                'duration_seconds' => now()->diffInSeconds($log->started_at),
                'metadata' => [
                    'pages_crawled' => $result['pages_crawled'],
                    'companies_found' => count($result['companies']),
                    'companies_added' => $processed['added'],
                    'companies_skipped' => $processed['skipped'],
                    'google_searches' => $processed['google_searched'],
                    'extraction_method' => 'js_script'
                ]
            ]);

            $this->updateProgress($cacheKey, 100, 'Completed successfully!');
            $this->sendCompletionNotification($processed['added'], $processed['skipped'], count($result['companies']), $processed['google_searched']);

            Log::info("Company list crawl completed for: {$this->dataSource->name}");

        } catch (ProcessTimedOutException $e) {
            $this->handleTimeoutError($e, $log, $cacheKey);
        } catch (\Exception $e) {
            $this->handleError($e, $log, $cacheKey);
        }
    }

    private function executeJavaScriptCrawler(int $maxPages): array
    {
        $scriptPath = base_path('scripts/crawl-companies.js');
        $outputPath = storage_path('app/crawler-output-' . $this->dataSource->id . '.json');

        // Prepare command arguments
        $command = [
            'node',
            $scriptPath,
            '--url', $this->dataSource->source_url,
            '--selector', $this->dataSource->company_name_selector,
            '--max-pages', (string) $maxPages,
            '--output', $outputPath
        ];

        // Add optional parameters
        if ($this->dataSource->company_link_selector) {
            $command[] = '--link-selector';
            $command[] = $this->dataSource->company_link_selector;
        }

        if ($this->dataSource->has_pagination) {
            $command[] = '--has-pagination';
            
            if ($this->dataSource->pagination_format) {
                $command[] = '--pagination-format';
                $command[] = $this->dataSource->pagination_format;
            }
        }

        Log::info("Executing JavaScript crawler", [
            'command' => implode(' ', $command),
            'data_source' => $this->dataSource->name
        ]);

        // Execute the JavaScript process
        $process = new Process($command);
        $process->setTimeout($this->timeout - 300); // Leave 5 minutes buffer
        $process->setWorkingDirectory(base_path());

        try {
            $process->run();

            if (!$process->isSuccessful()) {
                Log::error("JavaScript crawler failed", [
                    'exit_code' => $process->getExitCode(),
                    'error_output' => $process->getErrorOutput(),
                    'output' => $process->getOutput()
                ]);
                
                return [
                    'success' => false,
                    'error' => 'JavaScript crawler process failed: ' . $process->getErrorOutput()
                ];
            }

            // Parse JSON output - get the last line which should be the JSON result
            $output = $process->getOutput();
            
            // Split output into lines and get the last non-empty line
            $lines = array_filter(explode("\n", trim($output)), function($line) {
                return !empty(trim($line));
            });
            
            if (empty($lines)) {
                Log::error("No output from JavaScript crawler", [
                    'raw_output' => $output
                ]);
                
                return [
                    'success' => false,
                    'error' => 'No output from JavaScript crawler'
                ];
            }
            
            // The last line should be the JSON result
            $jsonLine = end($lines);
            $result = json_decode($jsonLine, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::error("Failed to parse JavaScript crawler output", [
                    'json_line' => $jsonLine,
                    'full_output' => $output,
                    'json_error' => json_last_error_msg()
                ]);
                
                return [
                    'success' => false,
                    'error' => 'Failed to parse crawler output: ' . json_last_error_msg()
                ];
            }

            // Clean up output file
            if (file_exists($outputPath)) {
                unlink($outputPath);
            }

            Log::info("JavaScript crawler completed successfully", [
                'companies_found' => count($result['companies'] ?? []),
                'pages_crawled' => $result['pages_crawled'] ?? 1
            ]);

            return $result;

        } catch (ProcessTimedOutException $e) {
            Log::error("JavaScript crawler timed out", [
                'timeout' => $process->getTimeout(),
                'data_source' => $this->dataSource->name
            ]);
            
            $process->stop();
            throw $e;
        }
    }

    private function processCompanies(array $companies, string $cacheKey): array
    {
        $processed = 0;
        $skipped = 0;
        $added = 0;
        $googleSearched = 0;
        $totalCompanies = count($companies);

        foreach ($companies as $index => $companyData) {
            // Skip limit check if maxCompanies is 0 (unlimited)
            if ($this->maxCompanies > 0 && $processed >= $this->maxCompanies) {
                break;
            }

            $processed++;
            
            // Update progress
            $progressPercent = 50 + (($index / max($totalCompanies, 1)) * 40);
            $searchStatus = $this->needsWebsiteSearch($companyData) ? ' (Google Search)' : '';
            $this->updateProgress($cacheKey, $progressPercent, "Processing company {$processed}/{$totalCompanies}: {$companyData['name']}{$searchStatus}");

            // Check blacklist - use comprehensive method like import process
            $domain = null;
            if (!empty($companyData['website'])) {
                $domain = parse_url($companyData['website'], PHP_URL_HOST);
            }
            
            if (Blacklist::isCompanyBlacklisted($companyData['name'], $domain)) {
                $skipped++;
                Log::debug("Skipped blacklisted company: {$companyData['name']}" . ($domain ? " (domain: {$domain})" : ""));
                continue;
            }

            // Check if company already exists
            if (Company::where('name', $companyData['name'])->exists()) {
                $skipped++;
                Log::debug("Skipped existing company: {$companyData['name']}");
                continue;
            }

            // Log if company already has a valid website
            if (!$this->needsWebsiteSearch($companyData)) {
                Log::debug("Company already has valid website: {$companyData['name']} -> {$companyData['website']}");
            }
            
            // Auto-search for website if missing
            $finalCompanyData = $this->ensureCompanyWebsite($companyData);
            if ($finalCompanyData['website'] !== $companyData['website']) {
                $googleSearched++;
                Log::info("Found website via Google search: {$companyData['name']} -> {$finalCompanyData['website']}");
            }

            // Create new company
            Company::create([
                'name' => $finalCompanyData['name'],
                'source_url' => $finalCompanyData['source_url'],
                'website' => $finalCompanyData['website'],
                'status' => 'pending',
                'data_source_id' => $this->dataSource->id,
            ]);

            $added++;
            Log::debug("Added new company: {$finalCompanyData['name']}");
        }

        return [
            'added' => $added,
            'skipped' => $skipped,
            'google_searched' => $googleSearched,
            'processed' => $processed
        ];
    }

    private function ensureCompanyWebsite(array $companyData): array
    {
        $website = $companyData['website'] ?? '';
        
        if (!$this->needsWebsiteSearch($companyData)) {
            return $companyData;
        }

        // Log the reason for Google search
        $searchReason = '';
        if (isset($companyData['needs_google_search']) && $companyData['needs_google_search']) {
            if (empty($website)) {
                $searchReason = $this->dataSource->company_link_selector ? 
                    'No link found with selector' : 'No link selector configured';
            } else {
                $searchReason = 'Invalid link found with selector';
            }
        } else {
            $searchReason = 'Invalid or missing website';
        }

        Log::info("Using Google search for company website", [
            'company' => $companyData['name'],
            'reason' => $searchReason,
            'current_website' => $website ?: 'none'
        ]);

        try {
            // Use JavaScript Google search
            $searchResult = $this->executeGoogleSearch($companyData['name']);
            
            if ($searchResult && isset($searchResult['website']) && $searchResult['website']) {
                $companyData['website'] = $searchResult['website'];
                Log::info("Successfully found website via Google search", [
                    'company' => $companyData['name'],
                    'website' => $searchResult['website']
                ]);
            } else {
                Log::warning("Google search did not find valid website", [
                    'company' => $companyData['name']
                ]);
            }
        } catch (\Exception $e) {
            Log::error("Google search failed for company: {$companyData['name']}", [
                'error' => $e->getMessage()
            ]);
        }

        return $companyData;
    }

    private function executeGoogleSearch(string $companyName): ?array
    {
        $scriptPath = base_path('scripts/google-search.js');
        $outputPath = storage_path('app/google-search-' . md5($companyName) . '.json');

        $command = [
            'node',
            $scriptPath,
            '--company-name', $companyName,
            '--output', $outputPath
        ];

        $process = new Process($command);
        $process->setTimeout(120); // Increased timeout to 2 minutes for Japanese companies
        $process->setWorkingDirectory(base_path());

        try {
            Log::info("Starting Google search for company", [
                'company' => $companyName,
                'command' => implode(' ', array_slice($command, 0, 4)) . ' ...'
            ]);

            $process->run();

            if (!$process->isSuccessful()) {
                Log::error("Google search process failed", [
                    'company' => $companyName,
                    'exit_code' => $process->getExitCode(),
                    'error_output' => $process->getErrorOutput(),
                    'output' => $process->getOutput()
                ]);
                return null;
            }

            $output = $process->getOutput();
            
            // Log the raw output for debugging
            Log::debug("Google search raw output", [
                'company' => $companyName,
                'output_length' => strlen($output),
                'output_preview' => substr($output, 0, 500)
            ]);

            $result = json_decode($output, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::error("Failed to decode Google search JSON", [
                    'company' => $companyName,
                    'json_error' => json_last_error_msg(),
                    'output' => $output
                ]);
                return null;
            }

            Log::info("Google search completed successfully", [
                'company' => $companyName,
                'success' => $result['success'] ?? false,
                'website' => $result['website'] ?? 'none',
                'search_results_count' => count($result['search_results'] ?? [])
            ]);

            // Clean up output file
            if (file_exists($outputPath)) {
                unlink($outputPath);
            }

            return $result;

        } catch (\Exception $e) {
            Log::error("Google search process failed", [
                'company' => $companyName,
                'error' => $e->getMessage(),
                'error_class' => get_class($e)
            ]);
            return null;
        }
    }

    private function needsWebsiteSearch($companyData): bool
    {
        // Check if JavaScript crawler explicitly marked this company as needing Google search
        if (is_array($companyData) && isset($companyData['needs_google_search']) && $companyData['needs_google_search']) {
            return true;
        }
        
        // For backward compatibility, also check the website field
        $website = is_array($companyData) ? ($companyData['website'] ?? '') : $companyData;
        
        // No website provided
        if (empty($website)) {
            return true;
        }
        
        // Invalid or placeholder URLs
        $invalidPatterns = [
            '#',
            'javascript:',
            'mailto:',
            'tel:',
            'example.com',
            'test.com',
            'placeholder',
            'coming-soon',
            'under-construction'
        ];
        
        foreach ($invalidPatterns as $pattern) {
            if (str_contains(strtolower($website), $pattern)) {
                return true;
            }
        }
        
        // Must be a valid URL with http/https
        if (!filter_var($website, FILTER_VALIDATE_URL) || 
            (!str_starts_with($website, 'http://') && !str_starts_with($website, 'https://'))) {
            return true;
        }
        
        return false;
    }

    private function determineMaxPagesToCrawl(): int
    {
        if ($this->dataSource->max_pages && $this->dataSource->max_pages > 0) {
            return min($this->maxPages, $this->dataSource->max_pages);
        }
        
        return $this->maxPages;
    }

    private function updateProgress(string $cacheKey, float $percent, string $message, array $extraData = []): void
    {
        Cache::put($cacheKey, array_merge([
            'percent' => $percent,
            'message' => $message,
            'updated_at' => now(),
        ], $extraData), 3600);
    }

    private function sendCompletionNotification(int $added, int $skipped, int $total, int $googleSearched = 0): void
    {
        if (!$this->userId) return;

        $title = $added > 0 ? '✅ Company List Crawl Completed!' : '⚠️ No New Companies Found';
        $body = "Processed {$total} companies. Added {$added} new companies, skipped {$skipped} existing/blacklisted.";
        
        $companiesWithDirectWebsite = $added - $googleSearched;
        
        // Detail about website extraction methods
        if ($companiesWithDirectWebsite > 0 && $googleSearched > 0) {
            $body .= " Website sources: {$companiesWithDirectWebsite} extracted directly from links, {$googleSearched} found via Google Search.";
        } elseif ($companiesWithDirectWebsite > 0) {
            $body .= " All {$companiesWithDirectWebsite} companies had websites extracted directly from page links.";
        } elseif ($googleSearched > 0) {
            $body .= " All {$googleSearched} company websites found via Google Search (no valid links found on source page).";
        }

        // Add note about link selector configuration
        if ($googleSearched > 0 && $this->dataSource->company_link_selector) {
            $body .= " Note: Link selector '{$this->dataSource->company_link_selector}' was configured but some companies needed Google search.";
        } elseif ($googleSearched > 0 && !$this->dataSource->company_link_selector) {
            $body .= " Note: No link selector configured, so Google search was used to find company websites.";
        }

        Notification::make()
            ->success()
            ->title($title)
            ->body($body)
            ->sendToDatabase(\App\Models\User::find($this->userId));
    }

    private function handleTimeoutError(\Exception $e, CrawlerLog $log, string $cacheKey): void
    {
        Log::error("Crawl job timed out", [
            'data_source' => $this->dataSource->name,
            'error' => $e->getMessage(),
        ]);

        // Reset crawling status
        $this->dataSource->update(['is_crawling' => false]);

        $log->markAsFailed('Job timed out: ' . $e->getMessage());
        $this->updateProgress($cacheKey, 0, 'Crawl timed out');
        
        if ($this->userId) {
            Notification::make()
                ->danger()
                ->title('❌ Crawl Timed Out')
                ->body("Company list crawl for {$this->dataSource->name} timed out after {$this->timeout} seconds.")
                ->sendToDatabase(\App\Models\User::find($this->userId));
        }

        $this->fail($e);
    }

    private function handleError(\Exception $e, CrawlerLog $log, string $cacheKey): void
    {
        Log::error("Crawl job failed", [
            'data_source' => $this->dataSource->name,
            'error' => $e->getMessage(),
        ]);

        // Reset crawling status
        $this->dataSource->update(['is_crawling' => false]);

        $log->markAsFailed($e->getMessage());
        $this->updateProgress($cacheKey, 0, 'Crawl failed: ' . $e->getMessage());
        
        if ($this->userId) {
            Notification::make()
                ->danger()
                ->title('❌ Crawl Failed')
                ->body("Company list crawl for {$this->dataSource->name} failed: " . $e->getMessage())
                ->sendToDatabase(\App\Models\User::find($this->userId));
        }

        throw $e;
    }

    public function failed(\Throwable $exception): void
    {
        // Reset crawling status in case of permanent failure
        $this->dataSource->update(['is_crawling' => false]);

        Log::error("❌ Company list crawl job failed permanently", [
            'data_source_id' => $this->dataSource->id,
            'data_source_name' => $this->dataSource->name,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ]);
    }
}
