<?php

namespace App\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Bus\Batchable;
use App\Models\Company;
use App\Models\Keyword;
use Illuminate\Support\Facades\Log;
use Symfony\Component\Process\Process;
use Symfony\Component\Process\Exception\ProcessTimedOutException;

class CrawlCompanyExecutivesJob implements ShouldQueue
{
    use Queueable, InteractsWithQueue, SerializesModels, Batchable;

    public Company $company;
    public int $timeout = 900; // 15 minutes
    public int $tries = 2;

    public function __construct(Company $company)
    {
        $this->company = $company;
        
        // Use separate queue for executive crawling to avoid blocking company list jobs
        $this->onQueue('executives');
    }

    public function handle(): void
    {
        // Skip if batch has been cancelled
        if ($this->batch()?->cancelled()) {
            Log::info("CrawlCompanyExecutivesJob cancelled for company: {$this->company->name}");
            return;
        }

        try {
            Log::info("Starting CrawlCompanyExecutivesJob for company: {$this->company->name} (Attempt: {$this->attempts()})");

            // Validate company data first
            if (!$this->validateCompanyData()) {
                $this->markJobAsFailedPermanently('Invalid company data');
                return;
            }

            // Update company status
            $this->company->update(['status' => 'processing']);
            
            // Try crawling with fallback strategies
            $crawlResults = [];
            $fallbackAttempted = false;
            
            try {
                // Primary strategy: Find and crawl all potential info pages
                $crawlResults = $this->crawlAllInfoPages();
            } catch (\Exception $e) {
                Log::warning("Primary crawl strategy failed, attempting fallback", [
                    'company' => $this->company->name,
                    'error' => $e->getMessage()
                ]);
                $fallbackAttempted = true;
                
                // Fallback strategy: Try only homepage with simpler approach
                try {
                    $homepageResult = $this->crawlHomepageForBasicInfo();
                    if ($homepageResult) {
                        $crawlResults = [$homepageResult];
                    }
                } catch (\Exception $fallbackError) {
                    Log::warning("Fallback crawl also failed", [
                        'company' => $this->company->name,
                        'fallback_error' => $fallbackError->getMessage()
                    ]);
                }
            }
            
            if (empty($crawlResults)) {
                // If both primary and fallback failed, mark as completed with timeout
                if ($fallbackAttempted) {
                    $this->markAsCompletedWithTimeout();
                } else {
                    $this->markAsCompletedWithNoData();
                }
                return;
            } else {
                // Process and update company with aggregated data from all pages
                $this->processAggregatedData($crawlResults);
            }

            // Calculate total executives found
            $totalExecutives = 0;
            foreach ($crawlResults as $crawlData) {
                $totalExecutives += count($crawlData['result']['executives'] ?? []);
            }

            Log::info("CrawlCompanyExecutivesJob completed successfully for company: {$this->company->name}. Found {$totalExecutives} executives from " . count($crawlResults) . " pages. Attempt: {$this->attempts()}");

        } catch (ProcessTimedOutException $e) {
            $this->handleTimeoutError($e);
        } catch (\Exception $e) {
            $this->handleGenericError($e);
        }
    }

    private function executeJavaScriptExecutiveCrawler(string $url): array
    {
        $scriptPath = base_path('scripts/crawl-executives.js');
        $outputPath = storage_path('app/executives-' . $this->company->id . '.json');

        // Get keywords from database
        $executiveKeywords = Keyword::getExecutiveKeywords();
        $companyInfoKeywords = Keyword::getCompanyInfoKeywords();
        
        // Prepare command arguments
        $command = [
            'node',
            $scriptPath,
            '--url', $url,
            '--company-name', $this->company->name,
            '--executive-keywords', implode(',', $executiveKeywords),
            '--info-keywords', implode(',', $companyInfoKeywords),
            '--output', $outputPath
        ];

        Log::info("Executing JavaScript executive crawler", [
            'command' => implode(' ', array_slice($command, 0, 4)) . ' ...',
            'company' => $this->company->name,
            'url' => $url
        ]);

        // Execute the JavaScript process
        $process = new Process($command);
        $process->setTimeout(600); // 10 minutes max per page
        $process->setWorkingDirectory(base_path());

        try {
            $process->run();

            if (!$process->isSuccessful()) {
                $exitCode = $process->getExitCode();
                $errorOutput = $process->getErrorOutput();
                $output = $process->getOutput();
                
                // Check if it's an EPIPE error (exit code 0 but with EPIPE in output)
                if ($exitCode === 0 || strpos($output, 'EPIPE') !== false || strpos($errorOutput, 'EPIPE') !== false) {
                    Log::warning("JavaScript executive crawler exited due to EPIPE (parent disconnect)", [
                        'company' => $this->company->name,
                        'exit_code' => $exitCode,
                        'note' => 'Process disconnected gracefully'
                    ]);
                    
                    return [
                        'success' => false,
                        'skipped' => true,
                        'skip_reason' => 'PROCESS_DISCONNECT',
                        'error' => 'Process disconnected (EPIPE) - treating as timeout',
                        'executives' => [],
                        'company_details' => []
                    ];
                }
                
                Log::error("JavaScript executive crawler failed", [
                    'company' => $this->company->name,
                    'exit_code' => $exitCode,
                    'error_output' => $errorOutput,
                    'output' => $output
                ]);
                
                return [
                    'success' => false,
                    'error' => 'JavaScript executive crawler process failed: ' . $errorOutput
                ];
            }

            // Parse JSON output - get the last line which should be the JSON result
            $output = $process->getOutput();
            
            // Split output into lines and get the last non-empty line
            $lines = array_filter(explode("\n", trim($output)), function($line) {
                return !empty(trim($line));
            });
            
            if (empty($lines)) {
                Log::error("No output from JavaScript executive crawler", [
                    'company' => $this->company->name,
                    'raw_output' => $output
                ]);
                
                return [
                    'success' => false,
                    'error' => 'No output from JavaScript executive crawler'
                ];
            }
            
            // The last line should be the JSON result
            $jsonLine = end($lines);
            $result = json_decode($jsonLine, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::error("Failed to parse JavaScript executive crawler output", [
                    'company' => $this->company->name,
                    'json_line' => $jsonLine,
                    'raw_output' => $output,
                    'json_error' => json_last_error_msg()
                ]);
                
                return [
                    'success' => false,
                    'error' => 'Failed to parse crawler output: ' . json_last_error_msg()
                ];
            }

            // Check if job was skipped due to timeout
            if (isset($result['skipped']) && $result['skipped'] === true && 
                isset($result['skip_reason']) && $result['skip_reason'] === 'BROWSER_TIMEOUT') {
                
                Log::warning("JavaScript executive crawler skipped due to browser timeout", [
                    'company' => $this->company->name,
                    'url' => $url,
                    'skip_reason' => $result['skip_reason']
                ]);
                
                // Clean up output file
                if (file_exists($outputPath)) {
                    unlink($outputPath);
                }
                
                return [
                    'success' => false,
                    'skipped' => true,
                    'skip_reason' => 'BROWSER_TIMEOUT',
                    'error' => 'Browser timeout - job skipped to prevent queue failure',
                    'executives' => [],
                    'company_details' => []
                ];
            }

            // Clean up output file
            if (file_exists($outputPath)) {
                unlink($outputPath);
            }

            Log::info("JavaScript executive crawler completed successfully", [
                'company' => $this->company->name,
                'executives_found' => count($result['executives'] ?? [])
            ]);

            return $result;

        } catch (ProcessTimedOutException $e) {
            Log::error("JavaScript executive crawler timed out", [
                'timeout' => $process->getTimeout(),
                'company' => $this->company->name
            ]);
            
            $process->stop();
            throw $e;
        }
    }

    private function processAggregatedData(array $crawlResults): void
    {
        $allExecutives = [];
        $allCompanyDetails = [];
        $crawledUrls = [];
        
        // Aggregate data from all successful crawls
        foreach ($crawlResults as $crawlData) {
            $result = $crawlData['result'];
            $url = $crawlData['url'];
            
            $crawledUrls[] = $url;
            
            // Merge executives (avoid duplicates by name)
            if (!empty($result['executives'])) {
                foreach ($result['executives'] as $executive) {
                    // Check for duplicates by name
                    $exists = false;
                    foreach ($allExecutives as $existing) {
                        if (strtolower($existing['name']) === strtolower($executive['name'])) {
                            $exists = true;
                            break;
                        }
                    }
                    
                    if (!$exists) {
                        $executive['source_url'] = $url; // Track which page this came from
                        $allExecutives[] = $executive;
                    }
                }
            }
            
            // Merge company details (prioritize non-empty values)
            if (!empty($result['company_details'])) {
                foreach ($result['company_details'] as $key => $value) {
                    if (!empty($value) && (empty($allCompanyDetails[$key]) || strlen($value) > strlen($allCompanyDetails[$key] ?? ''))) {
                        $allCompanyDetails[$key] = $value;
                        $allCompanyDetails[$key . '_source_url'] = $url; // Track source
                    }
                }
            }
        }

        // Process executives to add email predictions and enhancements
        $processedExecutives = $this->processExecutivesWithEmailPredictions($allExecutives);

        // Prepare update data
        $primaryInfoPageUrl = !empty($crawlResults) ? $crawlResults[0]['url'] : null;
        
        $updateData = array_merge($allCompanyDetails, [
            'info_page_url' => $primaryInfoPageUrl,
            'executives' => $processedExecutives,
            'status' => 'completed',
            'last_crawled_at' => now(),
            'crawl_attempts' => $this->company->crawl_attempts + 1,
            'crawl_metadata' => [
                'extraction_method' => 'js_script_multi_page',
                'content_method' => 'javascript',
                'info_page_url' => $primaryInfoPageUrl,
                'all_crawled_urls' => $crawledUrls,
                'pages_crawled' => count($crawlResults),
                'executives_found' => count($processedExecutives),
                'job_attempts' => $this->attempts(),
                'completed_at' => now()->toISOString(),
                'company_details_extracted' => !empty($allCompanyDetails),
                'script_version' => '1.1.0', // Updated version for multi-page crawling
                'unique_executives_by_page' => array_map(function($crawl) {
                    return [
                        'url' => $crawl['url'],
                        'executives_count' => count($crawl['result']['executives'] ?? [])
                    ];
                }, $crawlResults)
            ]
        ]);

        $this->company->update($updateData);
        
        Log::info("Aggregated data from multiple pages", [
            'company' => $this->company->name,
            'total_pages_crawled' => count($crawlResults),
            'urls_crawled' => $crawledUrls,
            'total_executives_found' => count($processedExecutives),
            'company_details_fields' => array_keys($allCompanyDetails)
        ]);
    }

    private function processExecutivesWithEmailPredictions(array $executives): array
    {
        // JavaScript crawler already generates sophisticated email predictions
        // So we just need to ensure the data is properly formatted
        
        return array_map(function ($executive) {
            // JavaScript script already provides email_predictions array
            // If not present, add empty array for consistency
            if (!isset($executive['email_predictions'])) {
                $executive['email_predictions'] = [];
            }
            
            // Ensure email_predictions is an array
            if (!is_array($executive['email_predictions'])) {
                $executive['email_predictions'] = [];
            }
            
            // Log for debugging
            if (!empty($executive['email_predictions'])) {
                Log::debug("Executive has email predictions", [
                    'name' => $executive['name'] ?? 'Unknown',
                    'predictions_count' => count($executive['email_predictions']),
                    'first_few_predictions' => array_slice($executive['email_predictions'], 0, 3)
                ]);
            }
            
            return $executive;
        }, $executives);
    }

    private function extractDomainFromWebsite(?string $website): ?string
    {
        if (!$website) return null;
        
        try {
            $parsed = parse_url($website);
            return $parsed['host'] ?? null;
        } catch (\Exception $e) {
            return null;
        }
    }

    private function validateCompanyData(): bool
    {
        if (!$this->company->website || empty(trim($this->company->website))) {
            Log::warning("Company has no website", ['company' => $this->company->name]);
            return false;
        }

        if (filter_var($this->company->website, FILTER_VALIDATE_URL) === false) {
            Log::warning("Company has invalid website URL", [
                'company' => $this->company->name,
                'website' => $this->company->website
            ]);
            return false;
        }

        return true;
    }

    private function crawlAllInfoPages(): array
    {
        $baseUrl = rtrim($this->company->website, '/');
        
        // Get company info keywords from database
        $infoKeywords = Keyword::getCompanyInfoKeywords();
        
        // Convert keywords to URL paths (e.g., 'about' -> '/about')
        $possiblePaths = ['/']; // Root page first
        
        foreach ($infoKeywords as $keyword) {
            $path = '/' . strtolower(trim($keyword));
            if (!in_array($path, $possiblePaths)) {
                $possiblePaths[] = $path;
            }
        }
        
        // Add some common variations
        $commonPaths = [
            '/about-us',
            '/company-info', 
            '/team-members',
            '/leadership-team',
            '/management',
            '/officers',
            '/staff',
            '/people',
            '/team'
        ];
        
        $possiblePaths = array_merge($possiblePaths, $commonPaths);
        
        Log::info("Checking all potential info pages", [
            'company' => $this->company->name,
            'base_url' => $baseUrl,
            'total_paths' => count($possiblePaths),
            'paths' => $possiblePaths
        ]);

        $successfulCrawls = [];
        $crawledUrls = []; // Track unique final URLs to avoid duplicates
        $maxPages = 8; // Limit maximum pages to crawl to avoid timeout
        $pagesChecked = 0;
        
        foreach ($possiblePaths as $path) {
            // Early exit if we've checked enough pages
            if ($pagesChecked >= $maxPages) {
                Log::info("Reached maximum page limit", [
                    'company' => $this->company->name,
                    'max_pages' => $maxPages,
                    'successful_crawls' => count($successfulCrawls)
                ]);
                break;
            }
            
            $pagesChecked++;
            $fullUrl = $baseUrl . $path;
            
            // Check if URL returns 200 and has content, get final URL
            $validationResult = $this->isValidInfoPageWithDetails($fullUrl);
            
            if ($validationResult['valid']) {
                $finalUrl = $validationResult['final_url'];
                
                // Skip if we've already crawled this final URL
                if (in_array($finalUrl, $crawledUrls)) {
                    Log::debug("Skipping duplicate final URL", [
                        'company' => $this->company->name,
                        'original_url' => $fullUrl,
                        'final_url' => $finalUrl,
                        'already_crawled' => true
                    ]);
                    continue;
                }
                
                $crawledUrls[] = $finalUrl;
                
                Log::info("Found valid info page, crawling...", [
                    'company' => $this->company->name,
                    'original_url' => $fullUrl,
                    'final_url' => $finalUrl
                ]);
                
                // Execute JavaScript executive crawler for this page
                $result = $this->executeJavaScriptExecutiveCrawler($finalUrl);
                
                // Check if job was skipped due to timeout
                if (isset($result['skipped']) && $result['skipped'] === true) {
                    Log::warning("Page crawl skipped due to timeout", [
                        'company' => $this->company->name,
                        'original_url' => $fullUrl,
                        'final_url' => $finalUrl,
                        'skip_reason' => $result['skip_reason'] ?? 'Unknown',
                        'continuing_with_next_page' => true
                    ]);
                    continue; // Skip this page and continue with others
                }
                
                if ($result['success'] && !empty($result['executives'])) {
                    $successfulCrawls[] = [
                        'url' => $fullUrl,
                        'final_url' => $finalUrl,
                        'path' => $path,
                        'result' => $result
                    ];
                    
                    Log::info("Successfully extracted executives from page", [
                        'company' => $this->company->name,
                        'original_url' => $fullUrl,
                        'final_url' => $finalUrl,
                        'executives_found' => count($result['executives'])
                    ]);
                    
                    // Early exit if we've found enough executives (3+ from 2+ pages)
                    $totalExecutives = 0;
                    foreach ($successfulCrawls as $crawl) {
                        $totalExecutives += count($crawl['result']['executives'] ?? []);
                    }
                    
                    if (count($successfulCrawls) >= 2 && $totalExecutives >= 3) {
                        Log::info("Early exit: Found sufficient executives", [
                            'company' => $this->company->name,
                            'pages_crawled' => count($successfulCrawls),
                            'total_executives' => $totalExecutives
                        ]);
                        break;
                    }
                } elseif ($result['success']) {
                    Log::debug("Page crawled successfully but no executives found", [
                        'company' => $this->company->name,
                        'original_url' => $fullUrl,
                        'final_url' => $finalUrl
                    ]);
                } else {
                    Log::warning("Failed to crawl page", [
                        'company' => $this->company->name,
                        'original_url' => $fullUrl,
                        'final_url' => $finalUrl,
                        'error' => $result['error'] ?? 'Unknown error'
                    ]);
                }
            } else {
                Log::debug("Page not accessible or invalid", [
                    'company' => $this->company->name,
                    'url' => $fullUrl,
                    'reason' => $validationResult['reason'] ?? 'Unknown'
                ]);
            }
            
            // Small delay between requests to be respectful
            sleep(1);
        }
        
        Log::info("Completed crawling all info pages", [
            'company' => $this->company->name,
            'total_checked' => count($possiblePaths),
            'successful_crawls' => count($successfulCrawls),
            'pages_with_executives' => array_column($successfulCrawls, 'url')
        ]);
        
        return $successfulCrawls;
    }

    private function crawlHomepageForBasicInfo(): ?array
    {
        $baseUrl = rtrim($this->company->website, '/');
        
        Log::info("Attempting to crawl homepage for basic company info", [
            'company' => $this->company->name,
            'url' => $baseUrl
        ]);
        
        // First check if homepage is accessible
        $validationResult = $this->isValidInfoPageWithDetails($baseUrl);
        if (!$validationResult['valid']) {
            Log::warning("Homepage not accessible", [
                'company' => $this->company->name,
                'url' => $baseUrl,
                'reason' => $validationResult['reason']
            ]);
            return null;
        }
        
        try {
            // Execute JavaScript crawler on homepage to get company details
            $result = $this->executeJavaScriptExecutiveCrawler($baseUrl);
            
            // Check if job was skipped due to timeout
            if (isset($result['skipped']) && $result['skipped'] === true) {
                Log::warning("Homepage crawl skipped due to timeout", [
                    'company' => $this->company->name,
                    'url' => $baseUrl,
                    'skip_reason' => $result['skip_reason'] ?? 'Unknown'
                ]);
                return null; // Skip homepage crawling
            }
            
            if ($result['success']) {
                Log::info("Successfully crawled homepage for basic info", [
                    'company' => $this->company->name,
                    'url' => $baseUrl,
                    'has_company_details' => !empty($result['company_details']),
                    'has_executives' => !empty($result['executives'])
                ]);
                
                return [
                    'url' => $baseUrl,
                    'result' => $result
                ];
            } else {
                Log::warning("Homepage crawl failed", [
                    'company' => $this->company->name,
                    'url' => $baseUrl,
                    'error' => $result['error'] ?? 'Unknown error'
                ]);
            }
            
            return null;
            
        } catch (\Exception $e) {
            Log::warning("Failed to crawl homepage for basic info", [
                'company' => $this->company->name,
                'url' => $baseUrl,
                'error' => $e->getMessage()
            ]);
            
            return null;
        }
    }

    private function processBasicCompanyInfo(array $homepageData): void
    {
        $result = $homepageData['result'];
        $url = $homepageData['url'];
        $companyDetails = $result['company_details'] ?? [];

        // Prepare update data with no executives
        $updateData = array_merge($companyDetails, [
            'info_page_url' => $url,
            'executives' => [], // No executives found
            'status' => 'completed',
            'last_crawled_at' => now(),
            'crawl_attempts' => $this->company->crawl_attempts + 1,
            'crawl_metadata' => [
                'extraction_method' => 'js_script_homepage_only',
                'content_method' => 'javascript',
                'info_page_url' => $url,
                'all_crawled_urls' => [$url],
                'pages_crawled' => 1,
                'executives_found' => 0,
                'job_attempts' => $this->attempts(),
                'completed_at' => now()->toISOString(),
                'company_details_extracted' => !empty($companyDetails),
                'script_version' => '1.1.0',
                'note' => 'No executives found in any pages, but company details extracted from homepage'
            ]
        ]);

        $this->company->update($updateData);
        
        Log::info("Updated company with basic info only", [
            'company' => $this->company->name,
            'url' => $url,
            'company_details_fields' => array_keys($companyDetails),
            'executives_found' => 0
        ]);
    }

    private function isValidInfoPageWithDetails(string $url): array
    {
        try {
            // Use cURL to handle redirects properly
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_MAXREDIRS => 3,
                CURLOPT_TIMEOUT => 8, // Reduced from 15 to 8 seconds
                CURLOPT_CONNECTTIMEOUT => 5, // Add connection timeout
                CURLOPT_USERAGENT => 'Mozilla/5.0 (compatible; CompanyCrawler/1.0)',
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_SSL_VERIFYHOST => false,
                CURLOPT_HEADER => false,
                CURLOPT_NOBODY => false
            ]);
            
            $content = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $finalUrl = curl_getinfo($ch, CURLINFO_EFFECTIVE_URL);
            $error = curl_error($ch);
            curl_close($ch);
            
            if ($content === false || !empty($error)) {
                return [
                    'valid' => false,
                    'reason' => 'cURL error: ' . $error,
                    'final_url' => null
                ];
            }
            
            // Check for 200 status code
            if ($httpCode !== 200) {
                return [
                    'valid' => false,
                    'reason' => 'Non-200 status: ' . $httpCode,
                    'final_url' => $finalUrl
                ];
            }
            
            // Check if content has HTML and is substantial (reduced threshold)
            if (strlen($content) < 200) {
                return [
                    'valid' => false,
                    'reason' => 'Content too short: ' . strlen($content) . ' bytes',
                    'final_url' => $finalUrl
                ];
            }
            
            // Basic check for HTML content
            if (!preg_match('/<html|<body|<div|<p/i', $content)) {
                return [
                    'valid' => false,
                    'reason' => 'No HTML content detected',
                    'final_url' => $finalUrl
                ];
            }
            
            return [
                'valid' => true,
                'final_url' => $finalUrl,
                'content_length' => strlen($content),
                'http_code' => $httpCode
            ];
            
        } catch (\Exception $e) {
            return [
                'valid' => false,
                'reason' => 'Exception: ' . $e->getMessage(),
                'final_url' => null
            ];
        }
    }

    private function isValidInfoPage(string $url): bool
    {
        $result = $this->isValidInfoPageWithDetails($url);
        return $result['valid'];
    }

    private function handleTimeoutError(\Exception $e): void
    {
        Log::warning("Executive crawl job timed out - marking as completed with timeout", [
            'company' => $this->company->name,
            'error' => $e->getMessage(),
            'attempt' => $this->attempts()
        ]);

        // Instead of failing, mark as completed with timeout metadata
        $this->company->update([
            'status' => 'completed',
            'last_crawled_at' => now(),
            'crawl_attempts' => $this->company->crawl_attempts + 1,
            'executives' => [],
            'crawl_metadata' => [
                'extraction_method' => 'js_script_timeout',
                'content_method' => 'javascript',
                'info_page_url' => $this->company->website,
                'all_crawled_urls' => [$this->company->website],
                'pages_crawled' => 0,
                'executives_found' => 0,
                'job_attempts' => $this->attempts(),
                'completed_at' => now()->toISOString(),
                'company_details_extracted' => false,
                'script_version' => '1.1.0',
                'timeout_error' => $e->getMessage(),
                'note' => 'Job timed out - marked as completed to prevent queue failure'
            ]
        ]);

        Log::info("Company marked as completed due to timeout instead of failed", [
            'company' => $this->company->name,
            'timeout_duration' => $this->timeout . ' seconds'
        ]);
    }

    private function handleGenericError(\Exception $e): void
    {
        Log::error("Executive crawl job failed", [
            'company' => $this->company->name,
            'error' => $e->getMessage(),
        ]);

        $this->updateCompanyWithError($e->getMessage(), 'generic_error');
        throw $e;
    }

    private function updateCompanyWithError(string $errorMessage, string $errorType): void
    {
        $this->company->update([
            'status' => 'failed',
            'crawl_attempts' => $this->company->crawl_attempts + 1,
            'crawl_metadata' => array_merge($this->company->crawl_metadata ?? [], [
                'last_error' => $errorMessage,
                'error_type' => $errorType,
                'failed_at' => now()->toISOString(),
                'job_attempts' => $this->attempts(),
            ])
        ]);
    }

    private function markAsCompletedWithNoData(): void
    {
        Log::info("Marking executive crawl as completed with no data found", [
            'company' => $this->company->name,
            'website' => $this->company->website
        ]);

        // Update company as completed but with no executives/details found
        $this->company->update([
            'status' => 'completed',
            'last_crawled_at' => now(),
            'crawl_attempts' => $this->company->crawl_attempts + 1,
            'executives' => [],
            'crawl_metadata' => [
                'extraction_method' => 'js_script_no_data',
                'content_method' => 'javascript',
                'info_page_url' => $this->company->website,
                'all_crawled_urls' => [$this->company->website],
                'pages_crawled' => 0,
                'executives_found' => 0,
                'job_attempts' => $this->attempts(),
                'completed_at' => now()->toISOString(),
                'company_details_extracted' => false,
                'script_version' => '1.1.0',
                'note' => 'No accessible info pages found and homepage crawl failed/skipped - marked as completed gracefully'
            ]
        ]);
    }

    private function markAsCompletedWithTimeout(): void
    {
        Log::info("Marking executive crawl as completed due to timeout/browser issues", [
            'company' => $this->company->name,
            'website' => $this->company->website
        ]);

        // Update company as completed but with timeout error
        $this->company->update([
            'status' => 'completed',
            'last_crawled_at' => now(),
            'crawl_attempts' => $this->company->crawl_attempts + 1,
            'executives' => [],
            'crawl_metadata' => [
                'extraction_method' => 'js_script_timeout',
                'content_method' => 'javascript',
                'info_page_url' => $this->company->website,
                'all_crawled_urls' => [$this->company->website],
                'pages_crawled' => 0,
                'executives_found' => 0,
                'job_attempts' => $this->attempts(),
                'completed_at' => now()->toISOString(),
                'company_details_extracted' => false,
                'script_version' => '1.1.0',
                'note' => 'Browser timeout or connection issues - marked as completed to prevent queue blocking'
            ]
        ]);
    }

    private function markJobAsFailedPermanently(string $reason): void
    {
        Log::warning("Marking executive crawl job as permanently failed", [
            'company' => $this->company->name,
            'reason' => $reason
        ]);

        $this->updateCompanyWithError($reason, 'permanent_failure');
        $this->fail(new \Exception($reason));
    }

    public function failed(\Throwable $exception): void
    {
        Log::error("❌ Executive crawl job failed permanently", [
            'company_id' => $this->company->id,
            'company_name' => $this->company->name,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ]);

        // Update company status to failed if not already updated
        if ($this->company->status !== 'failed') {
            $this->updateCompanyWithError(
                'Job failed permanently: ' . $exception->getMessage(),
                'permanent_failure'
            );
        }
    }
}
