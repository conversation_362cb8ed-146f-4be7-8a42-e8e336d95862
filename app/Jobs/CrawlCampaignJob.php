<?php

namespace App\Jobs;

use App\Models\Campaign;
use App\Models\Company;
use App\Models\DataSource;
use App\Services\EnhancedWebCrawlerService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Filament\Notifications\Notification;
use Symfony\Component\DomCrawler\Crawler;
use Symfony\Component\Process\Process;

class CrawlCampaignJob implements ShouldQueue
{
    use Queueable, InteractsWithQueue, SerializesModels;

    public Campaign $campaign;
    public ?int $userId;
    public int $timeout = 1800; // 30 minutes
    public int $tries = 2;

    /**
     * Create a new job instance.
     */
    public function __construct(Campaign $campaign, ?int $userId = null)
    {
        $this->campaign = $campaign;
        $this->userId = $userId;
        
        // Use high priority queue for campaign crawling
        $this->onQueue('campaigns');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info("Starting campaign crawl", [
                'campaign_id' => $this->campaign->id,
                'campaign_name' => $this->campaign->name,
                'data_source' => $this->campaign->dataSource->name,
                'type' => $this->campaign->type,
                'from_page' => $this->campaign->from_page,
                'to_page' => $this->campaign->to_page
            ]);

            $this->campaign->markAsRunning();

            $dataSource = $this->campaign->dataSource;
            
            // Mark DataSource as currently crawling
            $dataSource->update(['is_crawling' => true]);
            
            $crawler = new EnhancedWebCrawlerService();

            $totalCompaniesFound = 0;
            $totalGoogleSearched = 0;

            if ($this->campaign->type === 'pagination_range' && $dataSource->has_pagination) {
                // Crawl specific page range
                for ($page = $this->campaign->from_page; $page <= $this->campaign->to_page; $page++) {
                    Log::info("Crawling page {$page} for campaign {$this->campaign->name}");

                    $pageUrl = $dataSource->buildPaginationUrl($page);
                    
                    try {
                        $companies = $this->crawlSinglePage($crawler, $dataSource, $pageUrl);
                        $pageCompaniesCount = count($companies);
                        $totalCompaniesFound += $pageCompaniesCount;

                        // Update companies to belong to this campaign
                        $companyIds = [];
                        $googleSearched = 0;
                        foreach ($companies as $companyData) {
                            // Check blacklist before creating/updating company
                            $domain = null;
                            if (!empty($companyData['website'])) {
                                $domain = parse_url($companyData['website'], PHP_URL_HOST);
                            }
                            
                            if (\App\Models\Blacklist::isCompanyBlacklisted($companyData['name'], $domain)) {
                                Log::debug("Skipped blacklisted company in campaign: {$companyData['name']}" . ($domain ? " (domain: {$domain})" : ""));
                                continue;
                            }
                            
                            // Auto-search for website if missing (same as CrawlCompanyListJob)
                            $finalCompanyData = $this->ensureCompanyWebsite($companyData);
                            if ($finalCompanyData['website'] !== $companyData['website']) {
                                $googleSearched++;
                                Log::info("Found website via Google search in campaign: {$companyData['name']} -> {$finalCompanyData['website']}");
                            }
                            
                            // Use name + data_source_id as unique key to avoid overwriting when website is 'none' or empty
                            $company = Company::updateOrCreate(
                                [
                                    'name' => $finalCompanyData['name'],
                                    'data_source_id' => $dataSource->id
                                ],
                                array_merge($finalCompanyData, [
                                    'campaign_id' => $this->campaign->id
                                ])
                            );
                            $companyIds[] = $company->id;
                        }

                        Log::info("Page {$page} completed", [
                            'companies_found' => $pageCompaniesCount,
                            'google_searched' => $googleSearched,
                            'url' => $pageUrl
                        ]);

                        // Update campaign progress
                        $this->campaign->updateProgress(
                            $page - $this->campaign->from_page + 1,
                            $totalCompaniesFound
                        );

                        // Track total Google searches
                        $totalGoogleSearched += $googleSearched;

                        // Small delay between pages to be respectful
                        if ($page < $this->campaign->to_page) {
                            sleep(2);
                        }

                    } catch (\Exception $e) {
                        Log::error("Failed to crawl page {$page} for campaign {$this->campaign->name}", [
                            'error' => $e->getMessage(),
                            'url' => $pageUrl
                        ]);
                        
                        // Continue with next page instead of failing entire campaign
                        continue;
                    }
                }
            } else {
                // Crawl single page (full type)
                try {
                    $companies = $this->crawlSinglePage($crawler, $dataSource, $dataSource->source_url);
                    $totalCompaniesFound = count($companies);

                    // Update companies to belong to this campaign
                    $companyIds = [];
                    $googleSearched = 0;
                    foreach ($companies as $companyData) {
                        // Check blacklist before creating/updating company
                        $domain = null;
                        if (!empty($companyData['website'])) {
                            $domain = parse_url($companyData['website'], PHP_URL_HOST);
                        }
                        
                        if (\App\Models\Blacklist::isCompanyBlacklisted($companyData['name'], $domain)) {
                            Log::debug("Skipped blacklisted company in campaign: {$companyData['name']}" . ($domain ? " (domain: {$domain})" : ""));
                            continue;
                        }
                        
                        // Auto-search for website if missing (same as CrawlCompanyListJob)
                        $finalCompanyData = $this->ensureCompanyWebsite($companyData);
                        if ($finalCompanyData['website'] !== $companyData['website']) {
                            $googleSearched++;
                            Log::info("Found website via Google search in campaign: {$companyData['name']} -> {$finalCompanyData['website']}");
                        }
                        
                        Company::updateOrCreate(
                            [
                                'name' => $finalCompanyData['name'],
                                'data_source_id' => $dataSource->id
                            ],
                            array_merge($finalCompanyData, [
                                'campaign_id' => $this->campaign->id
                            ])
                        );
                    }

                    $this->campaign->updateProgress(1, $totalCompaniesFound);
                    
                    // Track Google searches for single page
                    $totalGoogleSearched = $googleSearched;

                } catch (\Exception $e) {
                    Log::error("Failed to crawl single page for campaign {$this->campaign->name}", [
                        'error' => $e->getMessage(),
                        'url' => $dataSource->source_url
                    ]);
                    throw $e;
                }
            }

            $this->campaign->markAsCompleted();

            // Update DataSource last_crawled_at timestamp
            $dataSource->update([
                'last_crawled_at' => now(),
                'is_crawling' => false
            ]);

            Log::info("Campaign completed successfully", [
                'campaign_id' => $this->campaign->id,
                'total_companies_found' => $totalCompaniesFound,
                'total_google_searched' => $totalGoogleSearched,
                'pages_crawled' => $this->campaign->completed_pages
            ]);

            // Send success notification
            if ($this->userId) {
                $searchInfo = $totalGoogleSearched > 0 ? " (including {$totalGoogleSearched} companies found via Google search)" : '';
                
                Notification::make()
                    ->success()
                    ->title('Campaign Completed!')
                    ->body("Campaign '{$this->campaign->name}' has completed successfully. Found {$totalCompaniesFound} companies{$searchInfo}.")
                    ->actions([
                        \Filament\Notifications\Actions\Action::make('view_companies')
                            ->button()
                            ->url(route('filament.admin.resources.companies.index', [
                                'tableFilters[campaign_id][value]' => $this->campaign->id
                            ]))
                            ->label('View Companies'),
                    ])
                    ->duration(10000)
                    ->sendToDatabase(\App\Models\User::find($this->userId));
            }

        } catch (\Exception $e) {
            $this->campaign->markAsFailed($e->getMessage());

            // Reset DataSource crawling state on failure
            $dataSource->update(['is_crawling' => false]);

            Log::error("Campaign failed", [
                'campaign_id' => $this->campaign->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Send failure notification
            if ($this->userId) {
                Notification::make()
                    ->danger()
                    ->title('Campaign Failed')
                    ->body("Campaign '{$this->campaign->name}' has failed: " . $e->getMessage())
                    ->duration(10000)
                    ->sendToDatabase(\App\Models\User::find($this->userId));
            }

            throw $e;
        }
    }

    /**
     * Crawl a single page URL and extract companies
     */
    private function crawlSinglePage(EnhancedWebCrawlerService $crawler, DataSource $dataSource, string $url): array
    {
        Log::info("Crawling single page", ['url' => $url]);

        // Fetch content from the specific URL
        $content = $crawler->fetchContent($url);
        
        if (!$content['success']) {
            throw new \Exception("Failed to fetch content from {$url}: " . $content['error']);
        }

        // Create crawler instance from the HTML content
        $domCrawler = new Crawler($content['html']);
        
        // Extract companies using the data source's selectors
        // Use reflection to access the protected method
        $reflection = new \ReflectionClass($crawler);
        $method = $reflection->getMethod('extractCompaniesFromPage');
        $method->setAccessible(true);
        
        $companies = $method->invoke($crawler, $domCrawler, $dataSource);
        
        Log::info("Extracted companies from page", [
            'url' => $url,
            'companies_found' => count($companies),
            'method_used' => $content['method'] ?? 'unknown'
        ]);

        return $companies;
    }

    /**
     * Ensure company has a valid website, using Google search if needed
     */
    private function ensureCompanyWebsite(array $companyData): array
    {
        $website = $companyData['website'] ?? '';
        
        if (!$this->needsWebsiteSearch($companyData)) {
            return $companyData;
        }

        // Log the reason for Google search
        $searchReason = '';
        if (isset($companyData['needs_google_search']) && $companyData['needs_google_search']) {
            if (empty($website)) {
                $searchReason = 'No link found with selector';
            } else {
                $searchReason = 'Invalid link found with selector';
            }
        } else {
            $searchReason = 'Invalid or missing website';
        }

        Log::info("Using Google search for company website in campaign", [
            'campaign' => $this->campaign->name,
            'company' => $companyData['name'],
            'reason' => $searchReason,
            'current_website' => $website ?: 'none'
        ]);

        try {
            // Use JavaScript Google search
            $searchResult = $this->executeGoogleSearch($companyData['name']);
            
            if ($searchResult && isset($searchResult['website']) && $searchResult['website']) {
                $companyData['website'] = $searchResult['website'];
                Log::info("Successfully found website via Google search in campaign", [
                    'campaign' => $this->campaign->name,
                    'company' => $companyData['name'],
                    'website' => $searchResult['website']
                ]);
            } else {
                Log::warning("Google search did not find valid website in campaign", [
                    'campaign' => $this->campaign->name,
                    'company' => $companyData['name']
                ]);
            }
        } catch (\Exception $e) {
            Log::error("Google search failed for company in campaign: {$companyData['name']}", [
                'campaign' => $this->campaign->name,
                'error' => $e->getMessage()
            ]);
        }

        return $companyData;
    }

    /**
     * Execute Google search for company website
     */
    private function executeGoogleSearch(string $companyName): ?array
    {
        $scriptPath = base_path('scripts/google-search.js');
        $outputPath = storage_path('app/google-search-campaign-' . md5($companyName) . '.json');

        $command = [
            'node',
            $scriptPath,
            '--company-name', $companyName,
            '--output', $outputPath
        ];

        $process = new Process($command);
        $process->setTimeout(120); // Increased timeout to 2 minutes for Japanese companies
        $process->setWorkingDirectory(base_path());

        try {
            Log::info("Starting Google search for company in campaign", [
                'campaign' => $this->campaign->name,
                'company' => $companyName,
                'command' => implode(' ', array_slice($command, 0, 4)) . ' ...'
            ]);

            $process->run();

            if (!$process->isSuccessful()) {
                Log::error("Google search process failed in campaign", [
                    'campaign' => $this->campaign->name,
                    'company' => $companyName,
                    'exit_code' => $process->getExitCode(),
                    'error_output' => $process->getErrorOutput(),
                    'output' => $process->getOutput()
                ]);
                return null;
            }

            $output = $process->getOutput();
            
            // Log the raw output for debugging
            Log::debug("Google search raw output in campaign", [
                'campaign' => $this->campaign->name,
                'company' => $companyName,
                'output_length' => strlen($output),
                'output_preview' => substr($output, 0, 500)
            ]);

            $result = json_decode($output, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::error("Failed to decode Google search JSON in campaign", [
                    'campaign' => $this->campaign->name,
                    'company' => $companyName,
                    'json_error' => json_last_error_msg(),
                    'output' => $output
                ]);
                return null;
            }

            Log::info("Google search completed successfully in campaign", [
                'campaign' => $this->campaign->name,
                'company' => $companyName,
                'success' => $result['success'] ?? false,
                'website' => $result['website'] ?? 'none',
                'search_results_count' => count($result['search_results'] ?? [])
            ]);

            // Clean up output file
            if (file_exists($outputPath)) {
                unlink($outputPath);
            }

            return $result;

        } catch (\Exception $e) {
            Log::error("Google search process failed in campaign", [
                'campaign' => $this->campaign->name,
                'company' => $companyName,
                'error' => $e->getMessage(),
                'error_class' => get_class($e)
            ]);
            return null;
        }
    }

    /**
     * Check if company needs website search
     */
    private function needsWebsiteSearch($companyData): bool
    {
        // Check if JavaScript crawler explicitly marked this company as needing Google search
        if (is_array($companyData) && isset($companyData['needs_google_search']) && $companyData['needs_google_search']) {
            return true;
        }
        
        // For backward compatibility, also check the website field
        $website = is_array($companyData) ? ($companyData['website'] ?? '') : $companyData;
        
        // No website provided
        if (empty($website)) {
            return true;
        }
        
        // Invalid or placeholder URLs
        $invalidPatterns = [
            '#',
            'javascript:',
            'mailto:',
            'tel:',
            'example.com',
            'test.com',
            'placeholder',
            'coming-soon',
            'under-construction'
        ];
        
        foreach ($invalidPatterns as $pattern) {
            if (str_contains(strtolower($website), $pattern)) {
                return true;
            }
        }
        
        // Must be a valid URL with http/https
        if (!filter_var($website, FILTER_VALIDATE_URL) || 
            (!str_starts_with($website, 'http://') && !str_starts_with($website, 'https://'))) {
            return true;
        }
        
        return false;
    }

    public function failed(\Throwable $exception): void
    {
        Log::error("Campaign job failed permanently", [
            'campaign_id' => $this->campaign->id,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);

        $this->campaign->markAsFailed($exception->getMessage());
        
        // Reset DataSource crawling state on permanent failure
        $this->campaign->dataSource->update(['is_crawling' => false]);
    }
}
