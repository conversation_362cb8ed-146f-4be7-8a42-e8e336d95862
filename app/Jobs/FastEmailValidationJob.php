<?php

namespace App\Jobs;

use App\Models\Company;
use App\Services\FastEmailValidationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Filament\Notifications\Notification;

class FastEmailValidationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected Company $company;
    protected ?int $userId;
    protected bool $overwriteExisting;

    public function __construct(Company $company, ?int $userId = null, bool $overwriteExisting = true)
    {
        $this->company = $company;
        $this->userId = $userId;
        $this->overwriteExisting = $overwriteExisting;
    }

    public function handle(): void
    {
        try {
            Log::info("⚡ Starting fast email validation for company: {$this->company->name}");

            $result = $this->validateEmailsForCompany($this->company);

            if ($result['success']) {
                $this->sendSuccessNotification($result);
                Log::info("✅ Fast email validation completed for {$this->company->name}", $result);
            } else {
                $this->sendErrorNotification($result['error']);
                Log::error("❌ Fast email validation failed for {$this->company->name}: {$result['error']}");
            }
        } catch (\Exception $e) {
            $this->sendErrorNotification($e->getMessage());
            Log::error("❌ Fast email validation job failed for {$this->company->name}: " . $e->getMessage());
            throw $e;
        }
    }

    public function failed(\Throwable $exception): void
    {
        $this->sendErrorNotification($exception->getMessage());
        Log::error("❌ Fast email validation job failed for {$this->company->name}: " . $exception->getMessage());
    }

    private function validateEmailsForCompany(Company $company): array
    {
        $executives = $company->executives ?? [];
        if (empty($executives)) {
            return [
                'success' => false,
                'error' => 'No executives found'
            ];
        }

        $validationService = new FastEmailValidationService();
        $executivesProcessed = 0;
        $predictionsGenerated = 0;
        $skippedCount = 0;
        $totalEmails = 0;
        $validEmails = 0;

        foreach ($executives as &$executive) {
            $executiveName = $executive['name'] ?? 'Unknown';
            $existingPredictions = $executive['email_predictions'] ?? [];

            // Skip if already has predictions and not overwriting
            if (!empty($existingPredictions) && !$this->overwriteExisting) {
                $skippedCount++;
                continue;
            }

            $executivesProcessed++;

            // Get all email predictions (from Python script or existing)
            $allEmails = $this->getAllEmailPredictions($executive, $company->website);

            if (empty($allEmails)) {
                Log::warning("⚠️ No email predictions found for executive: {$executiveName}");
                continue;
            }

            $totalEmails += count($allEmails);

            // Fast validation (regex + MX check)
            $validationResult = $validationService->validateEmails($allEmails);
            $validatedEmails = $validationResult['valid'];

            // Update executive with validated emails
            $executive['email_predictions'] = $validatedEmails;
            $validEmails += count($validatedEmails);

            if (!empty($validatedEmails)) {
                $predictionsGenerated++;
                Log::info("✅ Fast validation completed for {$executiveName}: " . count($validatedEmails) . "/" . count($allEmails) . " emails valid");
            } else {
                Log::warning("⚠️ No valid emails found for {$executiveName} after fast validation");
            }
        }

        // Save updated executives
        $company->executives = $executives;
        $company->save();

        return [
            'success' => true,
            'executives_processed' => $executivesProcessed,
            'predictions_generated' => $predictionsGenerated,
            'skipped_count' => $skippedCount,
            'total_emails' => $totalEmails,
            'valid_emails' => $validEmails,
            'validation_percentage' => $totalEmails > 0 ? round(($validEmails / $totalEmails) * 100, 2) : 0
        ];
    }

    private function getAllEmailPredictions(array $executive, string $website): array
    {
        $allEmails = [];

        // Get existing predictions
        $existingPredictions = $executive['email_predictions'] ?? [];
        if (!empty($existingPredictions)) {
            $allEmails = array_merge($allEmails, $existingPredictions);
        }

        // Get predictions from Python script if no existing ones
        if (empty($allEmails)) {
            $name = $executive['name'] ?? '';
            $domain = $this->extractDomain($website);

            if (!empty($name) && !empty($domain)) {
                $pythonPredictions = $this->getPythonPredictions($name, $domain);
                $allEmails = array_merge($allEmails, $pythonPredictions);
            }
        }

        return array_unique($allEmails);
    }

    private function extractDomain(string $website): string
    {
        // Remove protocol
        $domain = preg_replace('/^https?:\/\//', '', $website);

        // Remove www
        $domain = preg_replace('/^www\./', '', $domain);

        // Remove trailing slash and path
        $domain = strtok($domain, '/');

        // Remove port if exists
        $domain = strtok($domain, ':');

        return strtolower(trim($domain));
    }

    private function getPythonPredictions(string $name, string $domain): array
    {
        try {
            $scriptsPath = base_path('crawl-executives-scripts');
            $pythonPath = $scriptsPath . '/.venv/bin/python3';

            if (!file_exists($pythonPath)) {
                $pythonPath = $scriptsPath . '/venv/bin/python3';
            }

            if (!file_exists($pythonPath)) {
                Log::error("Python virtual environment not found");
                return [];
            }

            // Clean name and domain
            $cleanName = escapeshellarg($name);
            $cleanDomain = escapeshellarg($domain);

            // Call Python script with --no-validation
            $command = 'cd "' . $scriptsPath . '" && "' . $pythonPath . '" generate_email_predictions.py ' . $cleanName . ' ' . $cleanDomain . ' --no-validation 2>/dev/null';

            $output = shell_exec($command);
            $jsonResponse = json_decode(trim($output), true);

            if ($jsonResponse === null || json_last_error() !== JSON_ERROR_NONE) {
                Log::error("Invalid JSON output from Python script", [
                    'name' => $name,
                    'domain' => $domain,
                    'output' => $output
                ]);
                return [];
            }

            $predictions = $jsonResponse['predictions'] ?? [];
            $emails = [];

            foreach ($predictions as $prediction) {
                if (is_array($prediction) && !empty($prediction['email'])) {
                    $emails[] = $prediction['email'];
                } elseif (is_string($prediction) && !empty($prediction)) {
                    $emails[] = $prediction;
                }
            }

            return $emails;
        } catch (\Exception $e) {
            Log::error("Python email generation failed for \"{$name}\": " . $e->getMessage());
            return [];
        }
    }

    private function sendSuccessNotification(array $result): void
    {
        if (!$this->userId) {
            return;
        }

        $title = "Fast Email Validation Completed";
        $body = sprintf(
            "Fast validation completed for %s.\nProcessed: %d executives\nExecutives with valid emails: %d\nTotal emails: %d\nValid emails: %d\nValidation rate: %.1f%%\nSkipped: %d",
            $this->company->name,
            $result['executives_processed'],
            $result['predictions_generated'],
            $result['total_emails'],
            $result['valid_emails'],
            $result['validation_percentage'],
            $result['skipped_count']
        );

        Notification::make()
            ->title($title)
            ->body($body)
            ->success()
            ->duration(10000)
            ->sendToDatabase(\App\Models\User::find($this->userId));
    }

    private function sendErrorNotification(string $error): void
    {
        if (!$this->userId) {
            return;
        }

        Notification::make()
            ->title("Fast Email Validation Failed")
            ->body("Failed to validate emails for {$this->company->name}: {$error}")
            ->danger()
            ->duration(15000)
            ->sendToDatabase(\App\Models\User::find($this->userId));
    }
}
