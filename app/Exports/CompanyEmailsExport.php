<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Illuminate\Support\Collection;

class CompanyEmailsExport implements FromCollection, WithHeadings, WithMapping, WithColumnWidths, WithStyles
{
    protected $companies;

    public function __construct($companies)
    {
        $this->companies = $companies;
    }

    public function collection()
    {
        $exportData = collect();

        foreach ($this->companies as $company) {
            $executives = $company->executives ?? [];
            
            // Skip companies without executives
            if (empty($executives)) {
                continue;
            }

            $hasValidEmailPredictions = false;

            foreach ($executives as $executive) {
                $executiveName = $executive['name'] ?? 'Unknown';
                $position = $executive['position'] ?? '';
                $emailPredictions = $executive['email_predictions'] ?? [];

                // Skip executives without email predictions
                if (empty($emailPredictions)) {
                    continue;
                }

                $hasValidEmailPredictions = true;

                // Create one row for each email prediction
                foreach ($emailPredictions as $email) {
                    // Only include valid email addresses with at least 3 characters before @
                    $trimmedEmail = trim($email);
                    $atPosition = strpos($trimmedEmail, '@');
                    
                    if (!empty($trimmedEmail) && 
                        filter_var($trimmedEmail, FILTER_VALIDATE_EMAIL) &&
                        $atPosition !== false && $atPosition >= 3) {
                        
                        $exportData->push((object) [
                            'company_name' => $company->name,
                            'website' => $company->website,
                            'executive_name' => $executiveName,
                            'position' => $position,
                            'email' => $trimmedEmail
                        ]);
                    }
                }
            }

            // If company has no valid email predictions, it's already skipped by the continue statements above
        }

        return $exportData;
    }

    public function headings(): array
    {
        return [
            'Tên công ty',
            'Website URL', 
            'Tên lãnh đạo',
            'Chức danh',
            'Địa chỉ email'
        ];
    }

    public function map($row): array
    {
        return [
            $row->company_name,
            $row->website,
            $row->executive_name,
            $row->position,
            $row->email
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 30, // Company name
            'B' => 40, // Website URL
            'C' => 25, // Executive name
            'D' => 20, // Position
            'E' => 35, // Email
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            // Style the header row
            1 => [
                'font' => [
                    'bold' => true,
                    'size' => 12,
                ],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => [
                        'argb' => 'FFE2E8F0',
                    ],
                ],
            ],
            // Auto-wrap text for all cells
            'A:E' => [
                'alignment' => [
                    'wrapText' => true,
                    'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_TOP,
                ],
            ],
        ];
    }
} 