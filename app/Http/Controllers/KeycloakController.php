<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Department;
use App\Services\HrmService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Laravel\Socialite\Facades\Socialite;

class KeycloakController extends Controller
{
    /**
     * Redirect the user to the Keycloak authentication page.
     */
    public function redirectToKeycloak(Request $request)
    {
        // Lưu trữ URL trước đó để chuyển hướng sau khi đăng nhập
        if (!$request->has('redirect_uri')) {
            $request->session()->put('url.intended', url()->previous());
        }
        
        return Socialite::driver('keycloak')
            ->stateless()  // Sử dụng stateless để tránh lỗi InvalidStateException
            ->redirect();
    }

    /**
     * Obtain the user information from Keycloak.
     */
    public function handleKeycloakCallback(Request $request, HrmService $hrmService)
    {
        try {
            $keycloakUser = Socialite::driver('keycloak')->stateless()->user();
            
            // Tìm user dựa trên keycloak_id nếu đã tồn tại
            $user = User::where('keycloak_id', $keycloakUser->getId())->first();
            
            // Nếu không tìm thấy user bằng keycloak_id, tìm bằng email
            if (!$user) {
                $user = User::where('email', $keycloakUser->getEmail())->first();
            }
            
            // Nếu user tồn tại, cập nhật thông tin Keycloak
            if ($user) {
                // Lấy thông tin về role từ Keycloak
                $role = $this->extractRoleFromKeycloak($keycloakUser);
                
                $user->keycloak_id = $keycloakUser->getId();
                $user->is_keycloak_user = true;
                $user->role = $role; // Cập nhật role từ Keycloak
                $user->save();
            } else {
                // Lấy thông tin về role từ Keycloak
                $role = $this->extractRoleFromKeycloak($keycloakUser);
                
                // Tạo user mới nếu chưa tồn tại
                $user = User::create([
                    'name' => $keycloakUser->getName(),
                    'email' => $keycloakUser->getEmail(),
                    'password' => bcrypt(Str::random(32)), // Mật khẩu ngẫu nhiên
                    'keycloak_id' => $keycloakUser->getId(),
                    'is_keycloak_user' => true,
                    'role' => $role, // Lấy vai trò từ Keycloak
                ]);
            }

            // Đăng nhập
            Auth::login($user);

            // Lưu token từ Keycloak vào session nếu cần
            session([
                'keycloak_access_token' => $keycloakUser->token,
                'keycloak_refresh_token' => $keycloakUser->refreshToken,
            ]);
            
            // Đồng bộ thông tin phòng ban từ HRM
            $this->syncUserDepartments($user, $hrmService);

            // Chuyển hướng đến trang admin hoặc trang trước đó
            return redirect()->intended('/admin');
        } catch (\Exception $e) {
            // Log chi tiết lỗi
            \Log::error('Keycloak login error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'code' => $e->getCode(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
            ]);
            
            // Tạo thông báo lỗi chi tiết
            $errorMessage = __('auth.keycloak.login_failed', ['message' => $e->getMessage()]);
            
            // Xử lý lỗi 502
            if (strpos($e->getMessage(), '502') !== false) {
                $errorMessage = __('auth.keycloak.connection_error');
            }
            
            return redirect()->route('filament.admin.auth.login')->with('error', $errorMessage);
        }
    }
    
    /**
     * Extracts the user's role from Keycloak response.
     *
     * @param \Laravel\Socialite\Two\User $keycloakUser
     * @return string
     */
    protected function extractRoleFromKeycloak($keycloakUser)
    {
        // Mặc định role
        $defaultRole = 'user';
        
        try {
            // Decode JWT token để lấy thông tin chi tiết
            $tokenData = $this->decodeJwtToken($keycloakUser->token);
            
            if ($tokenData) {
                // Lấy client ID từ cấu hình
                $clientId = config('services.keycloak.client_id');
                
                // Ưu tiên mapping các roles từ Keycloak sang roles trong User model
                $roleMappings = [
                    'admin' => 'admin',          // admin trong Keycloak -> admin trong application
                    'super-admin' => 'admin',    // super-admin trong Keycloak -> admin trong application
                    'manager' => 'manager',      // manager trong Keycloak -> manager trong application
                    'cashier' => 'cashier', 
                    'accountant' => 'accountant', // accountant trong Keycloak -> accountant trong application
                    'approver' => 'manager',     // approver trong Keycloak -> manager trong application  
                    'user' => 'user',           // user trong Keycloak -> user trong application
                    'viewer' => 'user'          // viewer trong Keycloak -> user trong application
                ];
                
                // 1. Kiểm tra client-specific roles trong resource_access
                if (isset($tokenData['resource_access'][$clientId]['roles']) && 
                    is_array($tokenData['resource_access'][$clientId]['roles'])) {
                    
                    $roles = $tokenData['resource_access'][$clientId]['roles'];
                    
                    // Kiểm tra role cao nhất mà người dùng có từ client roles
                    foreach ($roleMappings as $keycloakRole => $appRole) {
                        if (in_array($keycloakRole, $roles)) {
                            return $appRole;
                        }
                    }
                    
                    // Nếu không tìm thấy trong mapping, trả về role đầu tiên
                    if (count($roles) > 0) {
                        return $roles[0];
                    }
                }
                
                // 2. Kiểm tra realm roles nếu không có client roles
                if (isset($tokenData['realm_access']['roles']) && 
                    is_array($tokenData['realm_access']['roles'])) {
                    
                    $roles = $tokenData['realm_access']['roles'];
                    
                    // Kiểm tra role cao nhất trong realm
                    if (in_array('admin', $roles)) {
                        return 'admin';
                    } else if (in_array('manager', $roles)) {
                        return 'manager';
                    } else if (in_array('accountant', $roles)) {
                        return 'accountant';
                    } else if (in_array('approver', $roles)) {
                        return 'manager';
                    }
                }
            }
            
            return $defaultRole;
            
        } catch (\Exception $e) {
            \Log::error('Error extracting role from Keycloak', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // Trả về role mặc định nếu có lỗi
            return $defaultRole;
        }
    }
    
    /**
     * Decode JWT token to extract payload data
     * 
     * @param string $token JWT token string
     * @return array|null Decoded payload or null on failure
     */
    protected function decodeJwtToken($token)
    {
        try {
            // JWT tokens are made of three parts: header.payload.signature
            $parts = explode('.', $token);
            
            if (count($parts) !== 3) {
                return null;
            }
            
            // Decode payload (middle part)
            $payload = $parts[1];
            
            // Base64 decode and convert to JSON
            $decodedPayload = base64_decode(strtr($payload, '-_', '+/'));
            if ($decodedPayload === false) {
                return null;
            }
            
            // Convert JSON to array
            $tokenData = json_decode($decodedPayload, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                return null;
            }
            
            return $tokenData;
            
        } catch (\Exception $e) {
            \Log::error('Error decoding JWT token', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }
    
    /**
     * Đồng bộ thông tin phòng ban cho người dùng từ HRM
     *
     * @param \App\Models\User $user
     * @param \App\Services\HrmService $hrmService
     * @return void
     */
    protected function syncUserDepartments(User $user, HrmService $hrmService)
    {
        try {
            // Lấy thông tin user từ HRM thông qua email
            $userInfo = $hrmService->getUserInfoByEmail($user->email);
            
            if (empty($userInfo) || !isset($userInfo['parts']) || !is_array($userInfo['parts'])) {
                Log::info(__('auth.keycloak.no_department_info', ['email' => $user->email]));
                return;
            }
            
            // Mảng lưu ID của các phòng ban thuộc về user
            $departmentIds = [];
            
            foreach ($userInfo['parts'] as $part) {
                if (!isset($part['id']) || !isset($part['name'])) {
                    continue;
                }
                
                $externalId = $part['id'];
                
                // Tìm phòng ban dựa trên external_id
                $department = Department::firstWhere('external_id', $externalId);
                
                // Nếu tìm thấy phòng ban, thêm vào danh sách
                if ($department) {
                    $departmentIds[] = $department->id;
                }
            }
            
            // Đồng bộ danh sách phòng ban cho user
            if (!empty($departmentIds)) {
                $user->departments()->sync($departmentIds);
                Log::info(__('auth.keycloak.departments_synced', ['count' => count($departmentIds), 'email' => $user->email]));
            } else {
                Log::info(__('auth.keycloak.no_departments_found', ['email' => $user->email]));
            }
            
        } catch (\Exception $e) {
            Log::error(__('auth.keycloak.department_sync_error'), [
                'user' => $user->email,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
}
