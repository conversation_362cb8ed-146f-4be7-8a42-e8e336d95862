<?php

namespace App\Policies;

use App\Models\Department;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class DepartmentPolicy
{
    /**
     * Xác định xem người dùng có thể xem danh sách phòng ban hay không.
     */
    public function viewAny(User $user): bool
    {
        // Tất cả người dùng đều có thể xem danh sách phòng ban
        return true;
    }

    /**
     * Xác định xem người dùng có thể xem chi tiết phòng ban hay không.
     */
    public function view(User $user, Department $department): bool
    {
        // Admin có thể xem tất cả phòng ban
        if ($user->isAdmin()) {
            return true;
        }

        // Người dùng có thể xem phòng ban của họ
        if ($user->belongsToDepartment($department->id)) {
            return true;
        }

        // Manager có thể xem phòng ban của mình quản lý
        return $user->id === $department->manager_id;
    }

    /**
     * Xác định xem người dùng có thể tạo phòng ban hay không.
     */
    public function create(User $user): bool
    {
        // Chỉ admin mới có thể tạo phòng ban
        return $user->isAdmin();
    }

    /**
     * Xác định xem người dùng có thể cập nhật phòng ban hay không.
     */
    public function update(User $user, Department $department): bool
    {
        // Chỉ admin mới có thể cập nhật thông tin phòng ban
        return $user->isAdmin();
    }

    /**
     * Xác định xem người dùng có thể xóa phòng ban hay không.
     */
    public function delete(User $user, Department $department): bool
    {
        // Chỉ admin mới có thể xóa phòng ban
        return $user->isAdmin();
    }
}
