<?php

namespace App\Policies;

use App\Models\Setting;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class SettingPolicy
{
    /**
     * Xác định xem người dùng có thể xem danh sách cấu hình hay không.
     */
    public function viewAny(User $user): bool
    {
        // Chỉ admin mới có thể xem danh sách cấu hình
        return $user->isAdmin();
    }

    /**
     * Xác định xem người dùng có thể xem chi tiết cấu hình hay không.
     */
    public function view(User $user, Setting $setting): bool
    {
        // Chỉ admin mới có thể xem chi tiết cấu hình
        return $user->isAdmin();
    }

    /**
     * Xác định xem người dùng có thể tạo cấu hình hay không.
     */
    public function create(User $user): bool
    {
        // Chỉ admin mới có thể tạo cấu hình
        return $user->isAdmin();
    }

    /**
     * Xác định xem người dùng có thể cập nhật cấu hình hay không.
     */
    public function update(User $user, Setting $setting): bool
    {
        // Chỉ admin mới có thể cập nhật cấu hình
        return $user->isAdmin();
    }

    /**
     * Xác định xem người dùng có thể xóa cấu hình hay không.
     */
    public function delete(User $user, Setting $setting): bool
    {
        // Chỉ admin mới có thể xóa cấu hình
        return $user->isAdmin();
    }
}
