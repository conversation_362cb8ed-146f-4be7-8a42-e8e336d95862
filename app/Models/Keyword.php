<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Keyword extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'keyword',
        'type',
        'position_level',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByPositionLevel($query, string $positionLevel)
    {
        return $query->where('position_level', $positionLevel);
    }

    public static function getCompanyInfoKeywords()
    {
        return self::active()
            ->byType('company_info_page')
            ->pluck('keyword')
            ->toArray();
    }

    public static function getExecutiveKeywords($positionLevel = null)
    {
        $query = self::active()->byType('executive');
        
        if ($positionLevel) {
            $query->byPositionLevel($positionLevel);
        }
        
        return $query->pluck('keyword')->toArray();
    }
}
