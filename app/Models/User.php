<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'keycloak_id',
        'is_keycloak_user',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'is_keycloak_user' => 'boolean',
        ];
    }

    /**
     * <PERSON><PERSON>y danh sách phòng ban của người dùng
     */
    public function departments(): BelongsToMany
    {
        return $this->belongsToMany(Department::class)->withTimestamps();
    }
    
    /**
     * Lấy phòng ban chính của người dùng (để tương thích ngược với code cũ)
     * @return Department|null
     */
    public function primaryDepartment()
    {
        return $this->departments()->first();
    }
    
    /**
     * Kiểm tra xem người dùng có thuộc phòng ban cụ thể hay không
     * 
     * @param int|Department $department ID phòng ban hoặc đối tượng Department
     * @return bool
     */
    public function belongsToDepartment($department): bool
    {
        $departmentId = $department instanceof Department ? $department->id : $department;
        return $this->departments()->where('departments.id', $departmentId)->exists();
    }



    /**
     * Lấy danh sách phòng ban do người dùng quản lý
     */
    public function managedDepartments(): HasMany
    {
        return $this->hasMany(Department::class, 'manager_id');
    }

    /**
     * Kiểm tra xem người dùng có phải là admin hay không
     */
    public function isAdmin(): bool
    {
        return $this->role === 'admin';
    }

    /**
     * Kiểm tra xem người dùng có phải là manager hay không
     */
    public function isManager(): bool
    {
        return $this->role === 'manager';
    }

        /**
     * Kiểm tra xem người dùng có phải là manager hay không
     */
    public function isAccountant(): bool
    {
        return $this->role === 'accountant';
    }

    /**
     * Kiểm tra xem người dùng có phải là thủ quỹ hay không
     */
    public function isCashier(): bool
    {
        return $this->role === 'cashier';
    }

    /**
     * Lấy danh sách các vai trò trong hệ thống
     */
    public static function getRoleOptions(): array
    {
        return [
            'admin' => __('users.roles.admin'),
            'manager' => __('users.roles.manager'),
            'accountant' => __('users.roles.accountant'),
            'cashier' => __('users.roles.cashier'),
            'user' => __('users.roles.user'),
        ];
    }
}
