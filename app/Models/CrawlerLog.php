<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class CrawlerLog extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'type',
        'status',
        'target_url',
        'crawlable_type',
        'crawlable_id',
        'metadata',
        'error_message',
        'duration_seconds',
        'started_at',
        'completed_at',
    ];

    protected $casts = [
        'metadata' => 'array',
        'duration_seconds' => 'integer',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
    ];

    public function crawlable(): MorphTo
    {
        return $this->morphTo();
    }

    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    public function scopeCompleted($query)
    {
        return $query->byStatus('completed');
    }

    public function scopeFailed($query)
    {
        return $query->byStatus('failed');
    }

    public function scopeRecent($query, int $days = 7)
    {
        return $query->where('started_at', '>=', now()->subDays($days));
    }

    public function markAsCompleted(array $metadata = []): void
    {
        $this->update([
            'status' => 'completed',
            'completed_at' => now(),
            'duration_seconds' => $this->started_at->diffInSeconds(now()),
            'metadata' => array_merge($this->metadata ?? [], $metadata),
        ]);
    }

    public function markAsFailed(string $errorMessage, array $metadata = []): void
    {
        $this->update([
            'status' => 'failed',
            'completed_at' => now(),
            'duration_seconds' => $this->started_at->diffInSeconds(now()),
            'error_message' => $errorMessage,
            'metadata' => array_merge($this->metadata ?? [], $metadata),
        ]);
    }

    public static function logCrawlStart(string $type, string $targetUrl, Model $crawlable, array $metadata = []): self
    {
        return self::create([
            'type' => $type,
            'status' => 'started',
            'target_url' => $targetUrl,
            'crawlable_type' => get_class($crawlable),
            'crawlable_id' => $crawlable->id,
            'metadata' => $metadata,
            'started_at' => now(),
        ]);
    }
}
