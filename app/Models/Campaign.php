<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Campaign extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'data_source_id',
        'type',
        'from_page',
        'to_page',
        'status',
        'total_pages',
        'completed_pages',
        'companies_found',
        'started_at',
        'completed_at',
        'crawl_metadata',
        'error_message',
    ];

    protected $casts = [
        'from_page' => 'integer',
        'to_page' => 'integer',
        'total_pages' => 'integer',
        'completed_pages' => 'integer',
        'companies_found' => 'integer',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'crawl_metadata' => 'array',
    ];

    public function dataSource(): BelongsTo
    {
        return $this->belongsTo(DataSource::class);
    }

    public function companies(): HasMany
    {
        return $this->hasMany(Company::class);
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeRunning($query)
    {
        return $query->where('status', 'running');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function getProgressPercentageAttribute(): float
    {
        if (!$this->total_pages || $this->total_pages === 0) {
            return 0;
        }
        
        return min(100, ($this->completed_pages / $this->total_pages) * 100);
    }

    public function getStatusBadgeColorAttribute(): string
    {
        return match ($this->status) {
            'pending' => 'gray',
            'running' => 'warning',
            'completed' => 'success',
            'failed' => 'danger',
            'cancelled' => 'secondary',
            default => 'gray'
        };
    }

    public function markAsRunning(): void
    {
        $this->update([
            'status' => 'running',
            'started_at' => now(),
        ]);
    }

    public function markAsCompleted(): void
    {
        $this->update([
            'status' => 'completed',
            'completed_at' => now(),
        ]);
    }

    public function markAsFailed(?string $errorMessage = null): void
    {
        $this->update([
            'status' => 'failed',
            'completed_at' => now(),
            'error_message' => $errorMessage,
        ]);
    }

    public function updateProgress(int $completedPages, ?int $companiesFound = null): void
    {
        $updateData = ['completed_pages' => $completedPages];
        
        if ($companiesFound !== null) {
            $updateData['companies_found'] = $companiesFound;
        }
        
        $this->update($updateData);
    }
}
