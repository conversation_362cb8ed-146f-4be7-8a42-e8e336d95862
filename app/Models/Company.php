<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Company extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'website',
        'source_url',
        'info_page_url',
        'industry',
        'description',
        'address',
        'phone',
        'email',
        'employee_count',
        'founded_year',
        'status',
        'crawl_attempts',
        'last_crawled_at',
        'executives',
        'crawl_metadata',
        'data_source_id',
        'campaign_id',
    ];

    protected $casts = [
        'last_crawled_at' => 'datetime',
        'founded_year' => 'integer',
        'crawl_attempts' => 'integer',
        'executives' => 'array',
        'crawl_metadata' => 'array',
    ];

    public function dataSource(): BelongsTo
    {
        return $this->belongsTo(DataSource::class);
    }

    public function campaign(): BelongsTo
    {
        return $this->belongsTo(Campaign::class);
    }

    public function crawlerLogs(): HasMany
    {
        return $this->hasMany(CrawlerLog::class, 'crawlable_id')
            ->where('crawlable_type', self::class);
    }

    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    public function scopePending($query)
    {
        return $query->byStatus('pending');
    }

    public function scopeCompleted($query)
    {
        return $query->byStatus('completed');
    }

    public function scopeFailed($query)
    {
        return $query->byStatus('failed');
    }

    public function scopeNeedsInfoPage($query)
    {
        return $query->whereNull('info_page_url');
    }

    public function scopeReadyForDetailCrawl($query)
    {
        return $query->whereNotNull('info_page_url')
            ->where('status', 'pending');
    }

    public function isBlacklisted(): bool
    {
        return Blacklist::active()
            ->where(function (Builder $query) {
                $query->where('company_name', 'like', '%' . $this->name . '%');
                
                if ($this->website) {
                    $domain = parse_url($this->website, PHP_URL_HOST);
                    if ($domain) {
                        $query->orWhere('domain', $domain);
                    }
                }
            })
            ->exists();
    }

    public function getExecutivesCountAttribute(): int
    {
        return count($this->executives ?? []);
    }

    public function getCLevelExecutivesAttribute(): array
    {
        if (!$this->executives) {
            return [];
        }

        return array_filter($this->executives, function ($executive) {
            return ($executive['level'] ?? '') === 'c_level';
        });
    }

    public function incrementCrawlAttempts(): void
    {
        $this->increment('crawl_attempts');
        $this->update(['last_crawled_at' => now()]);
    }

    public function markAsCompleted(): void
    {
        $this->update([
            'status' => 'completed',
            'last_crawled_at' => now(),
        ]);
    }

    public function markAsFailed(?string $reason = null): void
    {
        $metadata = $this->crawl_metadata ?? [];
        if ($reason) {
            $metadata['last_error'] = $reason;
        }

        $this->update([
            'status' => 'failed',
            'crawl_metadata' => $metadata,
            'last_crawled_at' => now(),
        ]);
    }
}
