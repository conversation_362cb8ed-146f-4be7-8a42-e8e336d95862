<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class DataSource extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'source_url',
        'has_pagination',
        'has_company_links',
        'company_name_selector',
        'company_link_selector',
        'pagination_format',
        'pagination_base_url',
        'max_pages',
        'is_active',
        'is_crawling',
        'last_crawled_at',
        'notes',
        'crawl_metadata',
    ];

    protected $casts = [
        'has_pagination' => 'boolean',
        'has_company_links' => 'boolean',
        'is_active' => 'boolean',
        'is_crawling' => 'boolean',
        'last_crawled_at' => 'datetime',
        'max_pages' => 'integer',
        'crawl_metadata' => 'array',
    ];

    public function companies(): HasMany
    {
        return $this->hasMany(Company::class);
    }

    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(Category::class, 'category_data_source');
    }

    public function crawlerLogs(): HasMany
    {
        return $this->hasMany(CrawlerLog::class, 'crawlable_id')
            ->where('crawlable_type', self::class);
    }

    public function campaigns(): HasMany
    {
        return $this->hasMany(Campaign::class);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function getStatusAttribute(): string
    {
        if (!$this->is_active) {
            return 'inactive';
        }

        if ($this->is_crawling) {
            return 'crawling';
        }

        // Check both direct crawls and campaign crawls
        $lastDirectCrawl = $this->last_crawled_at;
        $lastCampaignCrawl = $this->campaigns()
            ->where('status', 'completed')
            ->latest('completed_at')
            ->first()?->completed_at;
            
        $lastActivity = collect([$lastDirectCrawl, $lastCampaignCrawl])
            ->filter()
            ->max();

        if (!$lastActivity) {
            return 'never_crawled';
        }

        $daysSinceLastActivity = $lastActivity->diffInDays(now());
        
        if ($daysSinceLastActivity > 7) {
            return 'stale';
        }

        return 'active';
    }

    /**
     * Build pagination URL for given page number
     */
    public function buildPaginationUrl(int $pageNumber): string
    {
        $baseUrl = $this->pagination_base_url ?: $this->source_url;
        
        switch ($this->pagination_format) {
            case 'path_segment':
                // Format: page/{number}
                return rtrim($baseUrl, '/') . '/page/' . $pageNumber;
                
            case 'query_param':
                // Format: ?page={number}
                $separator = strpos($baseUrl, '?') !== false ? '&' : '?';
                return $baseUrl . $separator . 'page=' . $pageNumber;
                
            case 'query_p':
                // Format: ?p={number}
                $separator = strpos($baseUrl, '?') !== false ? '&' : '?';
                return $baseUrl . $separator . 'p=' . $pageNumber;
                
            case 'query_start':
                // Format: ?start={number*limit} (assuming 20 items per page, starting from 0)
                $separator = strpos($baseUrl, '?') !== false ? '&' : '?';
                $start = ($pageNumber - 1) * 20;
                return $baseUrl . $separator . 'start=' . $start;
                
            case 'offset':
                // Format: ?offset={number*limit} (assuming 20 items per page)
                $separator = strpos($baseUrl, '?') !== false ? '&' : '?';
                $offset = ($pageNumber - 1) * 20;
                return $baseUrl . $separator . 'offset=' . $offset;
                
            case 'custom_offset':
                // Format: ?from={number*limit} (assuming 20 items per page)
                $separator = strpos($baseUrl, '?') !== false ? '&' : '?';
                $from = ($pageNumber - 1) * 20;
                return $baseUrl . $separator . 'from=' . $from;
                
            default:
                // Default to query param
                $separator = strpos($baseUrl, '?') !== false ? '&' : '?';
                return $baseUrl . $separator . 'page=' . $pageNumber;
        }
    }

    /**
     * Get available pagination formats
     */
    public static function getPaginationFormats(): array
    {
        return [
            'query_param' => '?page={number} (Query Parameter)',
            'path_segment' => 'page/{number} (Path Segment)',
            'query_p' => '?p={number} (Short Query)',
            'query_start' => '?start={offset} (Start Index)',
            'offset' => '?offset={number} (Offset-based)',
            'custom_offset' => '?from={offset} (Custom Offset)',
        ];
    }
}
