<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class Blacklist extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'code',
        'name',
        'email',
        'associated_company',
        'website',
        'company_name',
        'domain',
        'reason',
        'added_by',
        'is_active',
        'note',
        'last_update',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'last_update' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();
        
        // Auto-generate code if not provided
        static::creating(function ($model) {
            if (empty($model->code)) {
                $model->code = self::generateCode();
            }
        });
        
        // Auto-update last_update timestamp when model is updated
        static::updating(function ($model) {
            $model->last_update = now();
        });
    }

    public function addedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'added_by');
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByReason($query, string $reason)
    {
        return $query->where('reason', $reason);
    }

    public function scopeByAssociatedCompany($query, string $company)
    {
        return $query->where('associated_company', 'like', '%' . $company . '%');
    }

    public function scopeByEmail($query, string $email)
    {
        return $query->where('email', $email);
    }

    public static function isPersonBlacklisted(?string $email = null, ?string $name = null, ?string $associatedCompany = null): bool
    {
        return self::active()
            ->where(function ($query) use ($email, $name, $associatedCompany) {
                if ($email) {
                    $query->where('email', $email);
                }
                
                if ($name) {
                    $query->orWhere('name', 'like', '%' . $name . '%');
                }
                
                if ($associatedCompany) {
                    $query->orWhere('associated_company', 'like', '%' . $associatedCompany . '%');
                }
            })
            ->exists();
    }

    public static function isCompanyBlacklisted(string $companyName, ?string $domain = null): bool
    {
        return self::active()
            ->where(function ($query) use ($companyName, $domain) {
                $query->where('company_name', 'like', '%' . $companyName . '%')
                      ->orWhere('associated_company', 'like', '%' . $companyName . '%');
                
                if ($domain) {
                    $query->orWhere('domain', $domain)
                          ->orWhere('website', 'like', '%' . $domain . '%');
                }
            })
            ->exists();
    }

    public static function addToBlacklist(array $data): self
    {
        // Ensure associated_company is set (required field)
        if (empty($data['associated_company'])) {
            throw new \InvalidArgumentException('Associated Company is required');
        }

        return self::create(array_merge($data, [
            'is_active' => true,
            'added_by' => $data['added_by'] ?? auth()->id(),
            'last_update' => now(),
        ]));
    }

    /**
     * Generate a unique code if not provided
     */
    protected static function generateCode(): string
    {
        do {
            $code = 'BL' . str_pad(rand(0, 999999), 6, '0', STR_PAD_LEFT);
        } while (self::where('code', $code)->exists());
        
        return $code;
    }
}
