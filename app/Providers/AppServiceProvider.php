<?php

namespace App\Providers;

use Filament\Pages\Dashboard;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Illuminate\Support\ServiceProvider;
use BezhanSalleh\FilamentLanguageSwitch\LanguageSwitch;
use Illuminate\Support\Facades\View;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->singleton(\App\Services\HrmService::class, function ($app) {
            return new \App\Services\HrmService();
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        LanguageSwitch::configureUsing(function (LanguageSwitch $switch) {
            $switch
                ->locales(['en','vi', 'ja']);
        });

        // Add noindex meta tags to all views for search engine prevention
        View::composer('*', function ($view) {
            if (!$view->offsetExists('noindexMetaTags')) {
                $view->with('noindexMetaTags', 
                    '<meta name="robots" content="noindex, nofollow, noarchive, nosnippet, notranslate, noimageindex">' . "\n" .
                    '<meta name="googlebot" content="noindex, nofollow, noarchive, nosnippet, notranslate, noimageindex">' . "\n" .
                    '<meta name="bingbot" content="noindex, nofollow, noarchive, nosnippet, notranslate, noimageindex">'
                );
            }
        });
    }
}
