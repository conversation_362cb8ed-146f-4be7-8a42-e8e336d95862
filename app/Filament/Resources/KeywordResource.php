<?php

namespace App\Filament\Resources;

use App\Filament\Resources\KeywordResource\Pages;
use App\Filament\Resources\KeywordResource\RelationManagers;
use App\Models\Keyword;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Filters\SelectFilter;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Section;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\BooleanColumn;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Actions\ImportAction;
use Filament\Forms\Components\FileUpload;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Storage;
use League\Csv\Reader;

class KeywordResource extends Resource
{
    protected static ?string $model = Keyword::class;

    protected static ?string $navigationIcon = 'heroicon-o-key';
    
    protected static ?string $navigationLabel = 'Keywords';
    
    protected static ?int $navigationSort = 30;

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Keyword Information')
                    ->schema([
                        TextInput::make('keyword')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('e.g., about-us, CEO, leadership'),
                        
                        Select::make('type')
                            ->required()
                            ->options([
                                'company_info_page' => 'Company Info Page',
                                'executive' => 'Executive/Leadership',
                            ])
                            ->reactive()
                            ->afterStateUpdated(function (callable $set, $state) {
                                if ($state !== 'executive') {
                                    $set('position_level', null);
                                }
                            }),
                        
                        Select::make('position_level')
                            ->label('Position Level')
                            ->options([
                                'c_level' => 'C-Level (CEO, CTO, CFO, etc.)',
                                'director' => 'Director Level',
                                'manager' => 'Manager Level',
                            ])
                            ->visible(fn (callable $get) => $get('type') === 'executive')
                            ->nullable(),
                        
                        Toggle::make('is_active')
                            ->label('Active')
                            ->default(true),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('keyword')
                    ->searchable()
                    ->sortable(),
                
                BadgeColumn::make('type')
                    ->colors([
                        'primary' => 'company_info_page',
                        'success' => 'executive',
                    ])
                    ->formatStateUsing(function ($state) {
                        return match($state) {
                            'company_info_page' => 'Company Info',
                            'executive' => 'Executive',
                            default => $state,
                        };
                    }),
                
                BadgeColumn::make('position_level')
                    ->label('Position Level')
                    ->colors([
                        'danger' => 'c_level',
                        'warning' => 'director', 
                        'info' => 'manager',
                    ])
                    ->formatStateUsing(function ($state) {
                        return match($state) {
                            'c_level' => 'C-Level',
                            'director' => 'Director',
                            'manager' => 'Manager',
                            default => $state,
                        };
                    })
                    ->placeholder('N/A'),
                
                BooleanColumn::make('is_active')
                    ->label('Active'),
                
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('type')
                    ->options([
                        'company_info_page' => 'Company Info Page',
                        'executive' => 'Executive',
                    ]),
                
                SelectFilter::make('position_level')
                    ->label('Position Level')
                    ->options([
                        'c_level' => 'C-Level',
                        'director' => 'Director',
                        'manager' => 'Manager',
                    ]),
                
                SelectFilter::make('is_active')
                    ->label('Status')
                    ->options([
                        1 => 'Active',
                        0 => 'Inactive',
                    ]),
                    
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\ForceDeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
            ])
            ->headerActions([
                Tables\Actions\Action::make('downloadTemplate')
                    ->label('Download CSV Template')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('success')
                    ->action(function () {
                        return response()->streamDownload(function () {
                            $handle = fopen('php://output', 'w');
                            
                            // CSV headers
                            fputcsv($handle, [
                                'type',
                                'keyword', 
                                'position_level',
                                'is_active'
                            ]);
                            
                            // Sample data
                            fputcsv($handle, ['company_info_page', 'about-us', '', '1']);
                            fputcsv($handle, ['executive', 'CEO', 'c_level', '1']);
                            fputcsv($handle, ['executive', 'director', 'director', '1']);
                            
                            fclose($handle);
                        }, 'keywords_template.csv', [
                            'Content-Type' => 'text/csv',
                        ]);
                    }),
                Tables\Actions\Action::make('importCsv')
                    ->label('Import CSV')
                    ->icon('heroicon-o-arrow-up-tray')
                    ->color('info')
                    ->form([
                        FileUpload::make('csv_file')
                            ->label('CSV File')
                            ->acceptedFileTypes(['text/csv', '.csv'])
                            ->disk('public')
                            ->directory('keyword-imports')
                            ->required()
                            ->helperText('CSV file should contain columns: type, keyword. Optional: position_level, is_active'),
                    ])
                    ->action(function (array $data) {
                        $file = $data['csv_file'];
                        
                        // Build full file path - file is stored in storage/app/public/keyword-imports/
                        $filePath = storage_path('app/public/' . $file);
                        
                        // Check if file exists
                        if (!file_exists($filePath)) {
                            Notification::make()
                                ->title("Import failed")
                                ->body("File [{$file}] does not exist at path: {$filePath}")
                                ->danger()
                                ->send();
                            return;
                        }
                        
                        $importedCount = self::importFromCsv($filePath);
                        
                        // Clean up the uploaded file after processing
                        try {
                            if (file_exists($filePath)) {
                                unlink($filePath);
                            }
                        } catch (\Exception $e) {
                            // Ignore cleanup errors
                        }
                        
                        if ($importedCount > 0) {
                            Notification::make()
                                ->title("Import completed successfully!")
                                ->body("Imported {$importedCount} keywords from CSV.")
                                ->success()
                                ->send();
                        }
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                    Tables\Actions\BulkAction::make('activate')
                        ->label('Activate')
                        ->icon('heroicon-o-check')
                        ->color('success')
                        ->action(function ($records) {
                            $records->each->update(['is_active' => true]);
                        }),
                    Tables\Actions\BulkAction::make('deactivate')
                        ->label('Deactivate')
                        ->icon('heroicon-o-x-mark')
                        ->color('danger')
                        ->action(function ($records) {
                            $records->each->update(['is_active' => false]);
                        }),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListKeywords::route('/'),
            'create' => Pages\CreateKeyword::route('/create'),
            'edit' => Pages\EditKeyword::route('/{record}/edit'),
        ];
    }

    protected static function importFromCsv(string $filePath): int
    {
        try {
            // Check if file exists
            if (!file_exists($filePath)) {
                Notification::make()
                    ->title("Import failed")
                    ->body("CSV file not found: " . $filePath)
                    ->danger()
                    ->send();
                return 0;
            }

            $csv = Reader::createFromPath($filePath, 'r');
            $csv->setHeaderOffset(0);
            
            $records = $csv->getRecords();
            $importedCount = 0;
            $errors = [];
            $row = 1; // Start from 1 (header is row 0)
            
            foreach ($records as $record) {
                $row++;
                
                // Validate required fields
                if (empty($record['type']) || empty($record['keyword'])) {
                    $errors[] = "Row {$row}: Missing required fields (type or keyword)";
                    continue;
                }
                
                // Validate type
                $validTypes = ['company_info_page', 'executive'];
                if (!in_array($record['type'], $validTypes)) {
                    $errors[] = "Row {$row}: Invalid type '{$record['type']}'. Must be 'company_info_page' or 'executive'";
                    continue;
                }
                
                // Prepare data
                $keywordData = [
                    'keyword' => trim($record['keyword']),
                    'type' => trim($record['type']),
                    'is_active' => isset($record['is_active']) 
                        ? filter_var($record['is_active'], FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE) ?? true
                        : true,
                ];
                
                // Add position_level if type is executive
                if ($record['type'] === 'executive' && isset($record['position_level']) && !empty($record['position_level'])) {
                    $validPositionLevels = ['c_level', 'director', 'manager'];
                    if (in_array(trim($record['position_level']), $validPositionLevels)) {
                        $keywordData['position_level'] = trim($record['position_level']);
                    } else {
                        $errors[] = "Row {$row}: Invalid position_level '{$record['position_level']}'. Must be 'c_level', 'director', or 'manager'";
                        continue;
                    }
                }
                
                // Create or update keyword
                try {
                    Keyword::updateOrCreate(
                        [
                            'keyword' => $keywordData['keyword'],
                            'type' => $keywordData['type'],
                        ],
                        $keywordData
                    );
                    
                    $importedCount++;
                } catch (\Exception $e) {
                    $errors[] = "Row {$row}: Database error - " . $e->getMessage();
                }
            }
            
            // Show errors if any
            if (!empty($errors)) {
                $errorMessage = "Import completed with errors:\n" . implode("\n", array_slice($errors, 0, 5));
                if (count($errors) > 5) {
                    $errorMessage .= "\n... and " . (count($errors) - 5) . " more errors";
                }
                
                Notification::make()
                    ->title("Import completed with errors")
                    ->body($errorMessage)
                    ->warning()
                    ->send();
            }
            
            return $importedCount;
            
        } catch (\Exception $e) {
            Notification::make()
                ->title("Import failed")
                ->body("Error reading CSV file: " . $e->getMessage())
                ->danger()
                ->send();
            
            return 0;
        }
    }
}
