<?php

namespace App\Filament\Resources;

use App\Filament\Resources\DepartmentResource\Pages;
use App\Models\Department;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class DepartmentResource extends Resource
{
    protected static ?string $model = Department::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-office';
    
    protected static ?int $navigationSort = 1;
    
    protected static ?string $navigationGroup = 'System';
    
    public static function getNavigationLabel(): string
    {
        return __('departments.navigation.label');
    }
    
    public static function getModelLabel(): string
    {
        return __('departments.resource.label');
    }
    
    public static function getPluralModelLabel(): string
    {
        return __('departments.resource.plural_label');
    }
    
    // Ẩn menu Phòng ban trong thanh điều hướng
    public static function shouldRegisterNavigation(): bool
    {
        return false;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make(__('departments.resource.label'))
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label(__('departments.form.fields.name.label'))
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('code')
                            ->label(__('departments.form.fields.code.label'))
                            ->required()
                            ->maxLength(50)
                            ->unique(ignoreRecord: true),
                        Forms\Components\Textarea::make('description')
                            ->label(__('departments.form.fields.description.label'))
                            ->rows(3)
                            ->columnSpanFull(),
                    ])
                    ->columns(2),
                    
                Forms\Components\Section::make(__('departments.form.sections.manager_info'))
                    ->schema([
                        Forms\Components\TextInput::make('manager_name')
                            ->label(__('departments.form.fields.manager_name.label'))
                            ->maxLength(255),
                        Forms\Components\Select::make('manager_id')
                            ->label(__('departments.form.fields.manager.label'))
                            ->relationship('manager', 'name')
                            ->searchable()
                            ->preload()
                            ->optionsLimit(10),
                        Forms\Components\Toggle::make('is_active')
                            ->label(__('departments.form.fields.is_active.label'))
                            ->default(true),
                    ])
                    ->columns(3),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('code')
                    ->label(__('departments.table.columns.code.label'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('name')
                    ->label(__('departments.table.columns.name.label'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('manager.name')
                    ->label(__('departments.table.columns.manager.label'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_active')
                    ->label(__('departments.table.columns.is_active.label'))
                    ->boolean()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('departments.table.columns.created_at.label'))
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('departments.table.columns.updated_at.label'))
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('is_active')
                    ->label(__('departments.filters.is_active.label'))
                    ->options([
                        '1' => __('departments.filters.is_active.options.active'),
                        '0' => __('departments.filters.is_active.options.inactive'),
                    ]),
                Tables\Filters\SelectFilter::make('manager_id')
                    ->label(__('departments.filters.manager.label'))
                    ->relationship('manager', 'name')
                    ->searchable()
                    ->preload(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDepartments::route('/'),
            'create' => Pages\CreateDepartment::route('/create'),
            'edit' => Pages\EditDepartment::route('/{record}/edit'),
        ];
    }
}
