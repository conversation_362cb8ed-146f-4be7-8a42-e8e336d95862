<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SettingResource\Pages;
use App\Models\Setting;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class SettingResource extends Resource
{
    protected static ?string $model = Setting::class;

    protected static ?string $navigationIcon = 'heroicon-o-cog-8-tooth';
    
    protected static ?int $navigationSort = 99;

    public static function getNavigationLabel(): string
    {
        return __('settings.navigation_label');
    }
    
    public static function getModelLabel(): string
    {
        return __('settings.model_label');
    }
    
    public static function getPluralModelLabel(): string
    {
        return __('settings.plural_model_label');
    }

    public static function getNavigationGroup(): ?string
    {
        return __('settings.navigation_group');
    }

    public static function canViewAny(): bool
    {
        // Admin luôn có quyền xem
        if (auth()->user()->isAdmin()) {
            return true;
        }
        
        return auth()->user()->can('viewAny', Setting::class);
    }

    public static function canCreate(): bool
    {
        // Admin luôn có quyền tạo mới
        if (auth()->user()->isAdmin()) {
            return true;
        }
        
        return auth()->user()->can('create', Setting::class);
    }

    public static function canEdit($record): bool
    {
        // Admin luôn có quyền chỉnh sửa
        if (auth()->user()->isAdmin()) {
            return true;
        }
        
        return auth()->user()->can('update', $record);
    }

    public static function canDelete($record): bool
    {
        // Admin luôn có quyền xóa
        if (auth()->user()->isAdmin()) {
            return true;
        }
        
        return auth()->user()->can('delete', $record);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make(__('settings.form.section_title'))
                    ->schema([
                        Forms\Components\TextInput::make('key')
                            ->label(__('settings.form.key.label'))
                            ->required()
                            ->maxLength(255)
                            ->unique(Setting::class, 'key', ignoreRecord: true)
                            ->rules(['alpha_dash'])
                            ->helperText(__('settings.form.key.helper_text')),
                        Forms\Components\Select::make('type')
                            ->label(__('settings.form.type.label'))
                            ->options(function () {
                                return [
                                    'string' => __('settings.types.string'),
                                    'number' => __('settings.types.number'),
                                    'boolean' => __('settings.types.boolean'),
                                    'json' => __('settings.types.json'),
                                ];
                            })
                            ->required()
                            ->reactive(),
                        Forms\Components\TextInput::make('value')
                            ->label(__('settings.form.value.label'))
                            ->required()
                            ->reactive()
                            ->rules(function ($get) {
                                $rules = ['required'];
                                
                                if ($get('type') === 'number') {
                                    $rules[] = 'numeric';
                                } elseif ($get('type') === 'boolean') {
                                    $rules[] = 'boolean';
                                } elseif ($get('type') === 'json') {
                                    $rules[] = 'json';
                                }
                                
                                return $rules;
                            })
                            ->helperText(function ($get) {
                                switch ($get('type')) {
                                    case 'number':
                                        return __('settings.form.value.helper_text.number');
                                    case 'boolean':
                                        return __('settings.form.value.helper_text.boolean');
                                    case 'json':
                                        return __('settings.form.value.helper_text.json');
                                    default:
                                        return __('settings.form.value.helper_text.string');
                                }
                            }),
                        Forms\Components\Textarea::make('description')
                            ->label(__('settings.form.description.label'))
                            ->rows(3)
                            ->columnSpanFull()
                            ->helperText(__('settings.form.description.helper_text')),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('key')
                    ->label(__('settings.table.key'))
                    ->searchable()
                    ->sortable()
                    ->copyable()
                    ->weight('medium'),
                Tables\Columns\TextColumn::make('value')
                    ->label(__('settings.table.value'))
                    ->limit(50)
                    ->tooltip(function ($record) {
                        return $record->value;
                    })
                    ->formatStateUsing(function ($state, $record) {
                        if ($record->type === 'boolean') {
                            return $state ? __('settings.table.boolean_yes') : __('settings.table.boolean_no');
                        }
                        return $state;
                    }),
                Tables\Columns\TextColumn::make('type')
                    ->label(__('settings.table.type'))
                    ->badge()
                    ->formatStateUsing(function (string $state): string {
                        return match($state) {
                            'string' => __('settings.types.string'),
                            'number' => __('settings.types.number'),
                            'boolean' => __('settings.types.boolean'),
                            'json' => __('settings.types.json'),
                            default => $state,
                        };
                    })
                    ->color(fn (string $state): string => 
                        match($state) {
                            'string' => 'gray',
                            'number' => 'success',
                            'boolean' => 'warning',
                            'json' => 'info',
                            default => 'gray',
                        }
                    ),
                Tables\Columns\TextColumn::make('description')
                    ->label(__('settings.table.description'))
                    ->limit(80)
                    ->tooltip(function ($record) {
                        return $record->description;
                    })
                    ->wrap(),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('settings.table.updated_at'))
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->defaultSort('key', 'asc')
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->label(__('settings.filters.type'))
                    ->options([
                        'string' => __('settings.types.string'),
                        'number' => __('settings.types.number'),
                        'boolean' => __('settings.types.boolean'),
                        'json' => __('settings.types.json'),
                    ]),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->requiresConfirmation(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->requiresConfirmation(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSettings::route('/'),
            'create' => Pages\CreateSetting::route('/create'),
            'edit' => Pages\EditSetting::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery();
    }
}
