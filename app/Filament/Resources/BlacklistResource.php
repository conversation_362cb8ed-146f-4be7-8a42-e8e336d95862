<?php

namespace App\Filament\Resources;

use App\Filament\Resources\BlacklistResource\Pages;
use App\Filament\Resources\BlacklistResource\RelationManagers;
use App\Models\Blacklist;
use App\Models\User;
use App\Imports\BlacklistImport;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Filters\SelectFilter;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\DateTimePicker;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\BooleanColumn;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Actions\ImportAction;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Maatwebsite\Excel\Excel;

class BlacklistResource extends Resource
{
    protected static ?string $model = Blacklist::class;

    protected static ?string $navigationIcon = 'heroicon-o-no-symbol';
    
    protected static ?string $navigationLabel = 'Blacklist Management';
    
    protected static ?int $navigationSort = 40;

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Basic Information')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextInput::make('code')
                                    ->label('Code')
                                    ->placeholder('Auto-generated if empty')
                                    ->maxLength(50),
                                
                                TextInput::make('name')
                                    ->label('Name (Tên người)')
                                    ->maxLength(255),
                                
                                TextInput::make('email')
                                    ->label('Email')
                                    ->email()
                                    ->maxLength(255),
                                
                                TextInput::make('associated_company')
                                    ->label('Associated Company')
                                    ->required()
                                    ->maxLength(255)
                                    ->helperText('This field is required'),
                            ]),
                    ]),

                Section::make('Website & Company Information')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextInput::make('website')
                                    ->label('Website')
                                    ->url()
                                    ->maxLength(255),
                                
                                TextInput::make('company_name')
                                    ->label('Company Name')
                                    ->maxLength(255),
                                
                                TextInput::make('domain')
                                    ->label('Domain')
                                    ->placeholder('e.g., example.com')
                                    ->maxLength(255),
                                
                                Select::make('reason')
                                    ->label('Reason')
                                    ->options([
                                        'duplicate' => 'Duplicate',
                                        'invalid' => 'Invalid',
                                        'not_target' => 'Not Target',
                                        'manual_request' => 'Manual Request',
                                    ])
                                    ->default('manual_request'),
                            ]),
                    ]),

                Section::make('Management Information')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                Select::make('added_by')
                                    ->label('Added By')
                                    ->relationship('addedBy', 'name')
                                    ->default(auth()->id())
                                    ->required(),
                                
                                Toggle::make('is_active')
                                    ->label('Active')
                                    ->default(true),
                                
                                DateTimePicker::make('last_update')
                                    ->label('Last Update')
                                    ->default(now())
                                    ->disabled(),
                            ]),
                        
                        Textarea::make('note')
                            ->label('Note')
                            ->rows(3)
                            ->columnSpanFull(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('code')
                    ->label('Code')
                    ->searchable()
                    ->sortable()
                    ->copyable()
                    ->weight('bold'),
                
                TextColumn::make('name')
                    ->label('Name')
                    ->searchable()
                    ->placeholder('N/A'),
                
                TextColumn::make('email')
                    ->label('Email')
                    ->searchable()
                    ->copyable()
                    ->placeholder('N/A'),
                
                TextColumn::make('associated_company')
                    ->label('Associated Company')
                    ->searchable()
                    ->sortable()
                    ->weight('medium')
                    ->wrap(),
                
                TextColumn::make('website')
                    ->label('Website')
                    ->limit(30)
                    ->placeholder('N/A'),
                
                TextColumn::make('company_name')
                    ->label('Company Name')
                    ->searchable()
                    ->placeholder('N/A')
                    ->toggleable(isToggledHiddenByDefault: true),
                
                TextColumn::make('domain')
                    ->label('Domain')
                    ->searchable()
                    ->placeholder('N/A')
                    ->toggleable(isToggledHiddenByDefault: true),
                
                BadgeColumn::make('reason')
                    ->colors([
                        'warning' => 'duplicate',
                        'danger' => 'invalid',
                        'info' => 'not_target',
                        'secondary' => 'manual_request',
                    ])
                    ->formatStateUsing(function ($state) {
                        return match($state) {
                            'duplicate' => 'Duplicate',
                            'invalid' => 'Invalid',
                            'not_target' => 'Not Target',
                            'manual_request' => 'Manual Request',
                            default => $state,
                        };
                    }),
                
                TextColumn::make('addedBy.name')
                    ->label('Added By')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                
                BooleanColumn::make('is_active')
                    ->label('Active'),
                
                TextColumn::make('last_update')
                    ->label('Last Update')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                
                TextColumn::make('created_at')
                    ->label('Added Date')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                SelectFilter::make('reason')
                    ->options([
                        'duplicate' => 'Duplicate',
                        'invalid' => 'Invalid',
                        'not_target' => 'Not Target',
                        'manual_request' => 'Manual Request',
                    ]),
                
                SelectFilter::make('is_active')
                    ->label('Status')
                    ->options([
                        1 => 'Active',
                        0 => 'Inactive',
                    ]),
                
                SelectFilter::make('added_by')
                    ->label('Added By')
                    ->relationship('addedBy', 'name')
                    ->preload(),
                    
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\ForceDeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
                Tables\Actions\Action::make('toggle_status')
                    ->label(fn ($record) => $record->is_active ? 'Deactivate' : 'Activate')
                    ->icon(fn ($record) => $record->is_active ? 'heroicon-o-x-mark' : 'heroicon-o-check')
                    ->color(fn ($record) => $record->is_active ? 'danger' : 'success')
                    ->requiresConfirmation()
                    ->action(function ($record) {
                        $newStatus = !$record->is_active;
                        $record->update(['is_active' => $newStatus]);
                        
                        Notification::make()
                            ->title($newStatus ? 'Activated' : 'Deactivated')
                            ->body(($record->name ?: $record->associated_company) . " has been " . ($newStatus ? 'activated' : 'deactivated'))
                            ->success()
                            ->send();
                    }),
            ])
            ->headerActions([
                Tables\Actions\Action::make('import_excel')
                    ->label('Import Excel')
                    ->icon('heroicon-o-arrow-up-tray')
                    ->color('success')
                    ->form([
                        Forms\Components\FileUpload::make('excel_file')
                            ->label('Excel File')
                            ->acceptedFileTypes([
                                'application/vnd.ms-excel',
                                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                                'text/csv'
                            ])
                            ->disk('public')
                            ->directory('blacklist-imports')
                            ->required()
                            ->helperText('Upload Excel (.xlsx, .xls) or CSV file with columns: code, name, email, associated_company, website, company_name, domain, reason, note, is_active'),
                    ])
                    ->action(function (array $data) {
                        $file = $data['excel_file'];
                        
                        try {
                            // Filament already includes directory in filename, so build path directly
                            $filePath = storage_path('app/public/' . $file);
                            
                            // Check if file exists
                            if (!file_exists($filePath)) {
                                throw new \Exception("File [{$file}] does not exist at path: {$filePath}");
                            }
                            
                            // Check class exists
                            if (!class_exists(BlacklistImport::class)) {
                                throw new \Exception('BlacklistImport class does not exist');
                            }
                            
                            // Create import instance
                            $import = new BlacklistImport();
                            
                            // Check if import method exists
                            if (!method_exists($import, 'import')) {
                                throw new \Exception('import method does not exist on BlacklistImport');
                            }
                            
                            // Import file
                            $import->import($filePath);
                            
                            $summary = $import->getImportSummary();
                            
                            $title = "Import Completed";
                            $body = "Imported: {$summary['imported']}, Skipped: {$summary['skipped']}";
                            
                            // Add detailed error information
                            $hasErrors = false;
                            if (!empty($summary['custom_errors'])) {
                                $hasErrors = true;
                                $body .= "\n\nCustom Errors:";
                                foreach (array_slice($summary['custom_errors'], 0, 5) as $error) {
                                    $body .= "\n- " . $error;
                                }
                                if (count($summary['custom_errors']) > 5) {
                                    $body .= "\n- ... and " . (count($summary['custom_errors']) - 5) . " more errors";
                                }
                            }
                            
                            if (!empty($summary['validation_failures'])) {
                                $hasErrors = true;
                                $body .= "\n\nValidation Failures:";
                                foreach (array_slice($summary['validation_failures'], 0, 3) as $failure) {
                                    $rowInfo = isset($failure['row']) ? "Row {$failure['row']}" : "Unknown row";
                                    $errors = isset($failure['errors']) ? implode(', ', $failure['errors']) : 'Unknown error';
                                    $body .= "\n- {$rowInfo}: {$errors}";
                                }
                                if (count($summary['validation_failures']) > 3) {
                                    $body .= "\n- ... and " . (count($summary['validation_failures']) - 3) . " more validation errors";
                                }
                            }
                            
                            Notification::make()
                                ->title($title)
                                ->body($body)
                                ->color($hasErrors ? 'warning' : 'success')
                                ->persistent()
                                ->send();
                                
                            // Clean up uploaded file
                            try {
                                if (file_exists($filePath)) {
                                    unlink($filePath);
                                }
                            } catch (\Exception $e) {
                                // Ignore cleanup errors
                            }
                                
                        } catch (\Exception $e) {
                            \Log::error('Blacklist Import Failed', [
                                'error_message' => $e->getMessage(),
                                'error_code' => $e->getCode(),
                                'error_file' => $e->getFile(),
                                'error_line' => $e->getLine(),
                                'stack_trace' => $e->getTraceAsString(),
                                'file' => $file ?? 'unknown',
                                'user_id' => auth()->id()
                            ]);
                            
                            Notification::make()
                                ->title('Import Failed')
                                ->body('Error: ' . $e->getMessage())
                                ->danger()
                                ->persistent()
                                ->send();
                        }
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                    Tables\Actions\BulkAction::make('activate')
                        ->label('Activate')
                        ->icon('heroicon-o-check')
                        ->color('success')
                        ->action(function ($records) {
                            $records->each->update(['is_active' => true]);
                        }),
                    Tables\Actions\BulkAction::make('deactivate')
                        ->label('Deactivate')
                        ->icon('heroicon-o-x-mark')
                        ->color('danger')
                        ->action(function ($records) {
                            $records->each->update(['is_active' => false]);
                        }),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBlacklists::route('/'),
            'create' => Pages\CreateBlacklist::route('/create'),
            'edit' => Pages\EditBlacklist::route('/{record}/edit'),
        ];
    }
}
