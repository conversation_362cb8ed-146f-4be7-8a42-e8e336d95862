<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CampaignResource\Pages;
use App\Filament\Resources\CampaignResource\RelationManagers;
use App\Models\Campaign;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Grid;
use Filament\Tables\Filters\SelectFilter;

class CampaignResource extends Resource
{
    protected static ?string $model = Campaign::class;

    protected static ?string $navigationIcon = 'heroicon-o-play';
    
    protected static ?string $navigationLabel = 'Crawl Campaigns';
    
    protected static ?string $modelLabel = 'Campaign';
    
    protected static ?string $pluralModelLabel = 'Campaigns';
    
    protected static ?int $navigationSort = 3;
    
    protected static ?string $navigationGroup = 'Data Management';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Campaign Details')
                    ->schema([
                        TextInput::make('name')
                            ->required()
                            ->maxLength(255)
                            ->columnSpanFull(),
                            
                        Select::make('data_source_id')
                            ->label('Data Source')
                            ->relationship('dataSource', 'name')
                            ->required()
                            ->searchable()
                            ->preload(),
                            
                        Select::make('type')
                            ->options([
                                'full' => 'Full Crawl',
                                'pagination_range' => 'Pagination Range',
                            ])
                            ->required()
                            ->default('full'),
                            
                        Grid::make(2)
                            ->schema([
                                TextInput::make('from_page')
                                    ->label('From Page')
                                    ->numeric()
                                    ->rules(['min:1'])
                                    ->visible(fn ($get) => $get('type') === 'pagination_range'),
                                    
                                TextInput::make('to_page')
                                    ->label('To Page')
                                    ->numeric()
                                    ->rules(['min:1'])
                                    ->visible(fn ($get) => $get('type') === 'pagination_range'),
                            ]),
                    ])->columns(2),
                    
                Forms\Components\Section::make('Status & Progress')
                    ->schema([
                        Select::make('status')
                            ->options([
                                'pending' => 'Pending',
                                'running' => 'Running',
                                'completed' => 'Completed',
                                'failed' => 'Failed',
                                'cancelled' => 'Cancelled',
                            ])
                            ->required()
                            ->default('pending'),
                            
                        Grid::make(3)
                            ->schema([
                                TextInput::make('total_pages')
                                    ->label('Total Pages')
                                    ->numeric()
                                    ->rules(['min:0']),
                                    
                                TextInput::make('completed_pages')
                                    ->label('Completed Pages')
                                    ->numeric()
                                    ->rules(['min:0'])
                                    ->default(0),
                                    
                                TextInput::make('companies_found')
                                    ->label('Companies Found')
                                    ->numeric()
                                    ->rules(['min:0'])
                                    ->default(0),
                            ]),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->limit(30)
                    ->tooltip(function ($record) {
                        return $record->name;
                    }),
                    
                TextColumn::make('dataSource.name')
                    ->label('Data Source')
                    ->searchable()
                    ->sortable()
                    ->limit(20)
                    ->url(function ($record) {
                        if (!$record || !$record->data_source_id) return null;
                        return route('filament.admin.resources.data-sources.edit', $record->data_source_id);
                    })
                    ->color('info'),
                    
                BadgeColumn::make('type')
                    ->colors([
                        'success' => 'full',
                        'warning' => 'pagination_range',
                    ])
                    ->formatStateUsing(function ($state) {
                        return match ($state) {
                            'full' => 'Full',
                            'pagination_range' => 'Range',
                            default => ucfirst($state)
                        };
                    }),
                    
                TextColumn::make('page_range')
                    ->label('Pages')
                    ->getStateUsing(function ($record) {
                        if (!$record) return 'N/A';
                        
                        if ($record->type === 'pagination_range') {
                            return "{$record->from_page}-{$record->to_page}";
                        }
                        return 'Full';
                    })
                    ->badge()
                    ->color('gray'),
                    
                TextColumn::make('progress_percentage')
                    ->label('Progress')
                    ->getStateUsing(function ($record) {
                        if (!$record) return '0%';
                        
                        $percentage = $record->progress_percentage ?? 0;
                        $completed = $record->completed_pages ?? 0;
                        $total = $record->total_pages ?? 0;
                        
                        if ($total > 0) {
                            return "{$completed}/{$total} ({$percentage}%)";
                        }
                        
                        return $record->status === 'completed' ? '100%' : '0%';
                    })
                    ->badge()
                    ->color(function ($record) {
                        if (!$record) return 'gray';
                        
                        $percentage = $record->progress_percentage ?? 0;
                        
                        if ($record->status === 'completed') return 'success';
                        if ($record->status === 'failed') return 'danger';
                        if ($record->status === 'running') {
                            if ($percentage >= 75) return 'success';
                            if ($percentage >= 50) return 'warning';
                            if ($percentage >= 25) return 'info';
                            return 'gray';
                        }
                        return 'gray';
                    })
                    ->tooltip(function ($record) {
                        if (!$record) return 'No data';
                        
                        $percentage = $record->progress_percentage ?? 0;
                        $completed = $record->completed_pages ?? 0;
                        $total = $record->total_pages ?? 0;
                        $companies = $record->companies_found ?? 0;
                        
                        return "Progress: {$percentage}%\nPages: {$completed}/{$total}\nCompanies found: {$companies}";
                    }),
                    
                BadgeColumn::make('status')
                    ->colors([
                        'gray' => 'pending',
                        'warning' => 'running',
                        'success' => 'completed',
                        'danger' => 'failed',
                        'secondary' => 'cancelled',
                    ])
                    ->formatStateUsing(fn ($state) => ucfirst($state)),
                    
                TextColumn::make('companies_found')
                    ->label('Companies')
                    ->badge()
                    ->color(fn ($state) => $state > 0 ? 'success' : 'gray')
                    ->url(function ($record) {
                        if (!$record || !$record->companies_found || $record->companies_found <= 0) {
                            return null;
                        }
                        return route('filament.admin.resources.companies.index', [
                            'tableFilters[campaign_id][value]' => $record->id
                        ]);
                    }),
                    
                TextColumn::make('started_at')
                    ->label('Started')
                    ->dateTime()
                    ->sortable()
                    ->placeholder('Not started')
                    ->since(),
                    
                TextColumn::make('completed_at')
                    ->label('Completed')
                    ->dateTime()
                    ->sortable()
                    ->placeholder('Not completed')
                    ->since()
                    ->visible(fn ($record) => $record && $record->status === 'completed'),
                    
                TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->sortable()
                    ->since(),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options([
                        'pending' => 'Pending',
                        'running' => 'Running',
                        'completed' => 'Completed',
                        'failed' => 'Failed',
                        'cancelled' => 'Cancelled',
                    ]),
                    
                SelectFilter::make('type')
                    ->options([
                        'full' => 'Full Crawl',
                        'pagination_range' => 'Pagination Range',
                    ]),
                    
                SelectFilter::make('data_source_id')
                    ->label('Data Source')
                    ->relationship('dataSource', 'name')
                    ->preload(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
                    ->visible(fn ($record) => $record && in_array($record->status, ['pending', 'failed'])),
                Tables\Actions\DeleteAction::make()
                    ->visible(fn ($record) => $record && in_array($record->status, ['pending', 'failed', 'cancelled'])),
                    
                Tables\Actions\Action::make('cancel')
                    ->label('Cancel')
                    ->icon('heroicon-o-x-mark')
                    ->color('danger')
                    ->visible(fn ($record) => $record && in_array($record->status, ['pending', 'running']))
                    ->action(function ($record) {
                        if ($record) {
                            $record->update(['status' => 'cancelled']);
                        }
                    })
                    ->requiresConfirmation(),
                    
                Tables\Actions\Action::make('restart')
                    ->label('Restart')
                    ->icon('heroicon-o-arrow-path')
                    ->color('warning')
                    ->visible(fn ($record) => $record && in_array($record->status, ['failed', 'cancelled']))
                    ->action(function ($record) {
                        if ($record) {
                            $record->update([
                                'status' => 'pending',
                                'completed_pages' => 0,
                                'companies_found' => 0,
                                'started_at' => null,
                                'completed_at' => null,
                                'error_message' => null,
                            ]);
                            
                            // Dispatch the job again
                            \App\Jobs\CrawlCampaignJob::dispatch($record, auth()->id())->onQueue('campaigns');
                        }
                    })
                    ->requiresConfirmation(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc')
            ->poll('30s'); // Auto-refresh every 30 seconds
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCampaigns::route('/'),
            'create' => Pages\CreateCampaign::route('/create'),
            'view' => Pages\ViewCampaign::route('/{record}'),
            'edit' => Pages\EditCampaign::route('/{record}/edit'),
        ];
    }
}
