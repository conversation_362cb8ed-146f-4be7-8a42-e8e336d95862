<?php

namespace App\Filament\Resources\DataSourceResource\Pages;

use App\Filament\Resources\DataSourceResource;
use App\Jobs\CrawlCompanyListJob;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Log;

class CreateDataSource extends CreateRecord
{
    protected static string $resource = DataSourceResource::class;
    
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
    
    protected function afterCreate(): void
    {
        // Automatically crawl the data source to get company count after creation
        $this->crawlDataSourceAndShowResults();
    }
    
    protected function crawlDataSourceAndShowResults(): void
    {
        try {
            // Use default values from data source configuration for auto-crawl after creation
            $maxPages = min($this->record->max_pages ?? 1, 2); // Auto-crawl: max 2 pages for initial test
            $maxCompanies = 50; // Default limit for initial crawl
            
            // Dispatch CrawlCompanyListJob instead of running command
            CrawlCompanyListJob::dispatch(
                $this->record,
                $maxPages,
                $maxCompanies,
                auth()->id()
            )->onQueue('crawler');
            
            Notification::make()
                ->success()
                ->title('Data Source Created Successfully!')
                ->body(
                    "Data source '{$this->record->name}' has been created and crawling job has been queued. " . 
                    "You'll receive a notification when crawling is complete."
                )
                ->actions([
                    \Filament\Notifications\Actions\Action::make('view_data_source')
                        ->button()
                        ->color('info')
                        ->url(route('filament.admin.resources.data-sources.edit', ['record' => $this->record->id]))
                        ->label('View Data Source'),
                ])
                ->duration(10000)
                ->send();
                
        } catch (\Exception $e) {
            Log::error('Auto-crawl after data source creation failed', [
                'data_source_id' => $this->record->id,
                'error' => $e->getMessage()
            ]);
            
            Notification::make()
                ->danger()
                ->title('Data Source Created, Crawling Failed')
                ->body(
                    "Data source '{$this->record->name}' has been created but automatic crawling failed: " . 
                    $e->getMessage()
                )
                ->actions([
                    \Filament\Notifications\Actions\Action::make('retry_crawl')
                        ->button()
                        ->color('danger')
                        ->action(fn () => $this->crawlDataSourceAndShowResults())
                        ->label('Retry Crawling'),
                ])
                ->duration(12000)
                ->send();
        }
    }
    
    protected function getCreatedNotification(): ?Notification
    {
        // Return null because we're handling notifications in afterCreate()
        return null;
    }
}
