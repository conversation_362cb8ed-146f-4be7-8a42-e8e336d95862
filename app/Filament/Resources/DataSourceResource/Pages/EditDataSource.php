<?php

namespace App\Filament\Resources\DataSourceResource\Pages;

use App\Filament\Resources\DataSourceResource;
use App\Jobs\CrawlCompanyListJob;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Cache;

class EditDataSource extends EditRecord
{
    protected static string $resource = DataSourceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('crawl_now')
                ->label('Crawl Now')
                ->icon('heroicon-o-arrow-down-tray')
                ->color('success')
                ->form([
                    \Filament\Forms\Components\Section::make('Crawling Options')
                        ->schema([
                            \Filament\Forms\Components\Toggle::make('run_in_background')
                                ->label('Run in Background')
                                ->default(true)
                                ->helperText('Run as background job (recommended) or wait for completion'),
                        ])
                ])
                ->modalHeading('Start Crawling')
                ->modalDescription('This will crawl companies from the configured data source.')
                ->modalSubmitActionLabel('Start Crawling')
                ->action(function (array $data) {
                    $runInBackground = $data['run_in_background'] ?? true;
                    
                    if ($runInBackground) {
                        $this->startBackgroundCrawl();
                    } else {
                        $this->runSynchronousCrawl();
                    }
                }),
            Actions\Action::make('view_stats')
                ->label('View Company Stats')
                ->icon('heroicon-o-chart-bar')
                ->color('info')
                ->modalHeading(fn () => 'Company Statistics for: ' . $this->record->name)
                ->modalContent(function () {
                    $companiesCount = $this->record->companies()->count();
                    $latestCompany = $this->record->companies()->latest()->first();
                    $oldestCompany = $this->record->companies()->oldest()->first();
                    $lastCrawled = $this->record->last_crawled_at;
                    
                    return view('filament.components.data-source-stats', [
                        'companiesCount' => $companiesCount,
                        'latestCompany' => $latestCompany,
                        'oldestCompany' => $oldestCompany,
                        'lastCrawled' => $lastCrawled,
                        'dataSource' => $this->record,
                    ]);
                })
                ->modalSubmitAction(false)
                ->modalCancelActionLabel('Close'),
            Actions\DeleteAction::make(),
        ];
    }
    
    protected function getSavedNotification(): ?Notification
    {
        $companiesCount = $this->record->companies()->count();
        
        return Notification::make()
            ->success()
            ->title('Data Source Updated Successfully!')
            ->body(
                "Data source '{$this->record->name}' has been updated. " . 
                "Current companies: {$companiesCount}. " . 
                ($companiesCount === 0 ? "Run the crawler to start collecting company data." : "Latest crawl: " . ($this->record->last_crawled_at ? $this->record->last_crawled_at->diffForHumans() : 'Never'))
            )
            ->duration(8000);
    }
    
    /**
     * Start background crawl job
     */
    protected function startBackgroundCrawl(): void
    {
        try {
            // Use unlimited crawling - no page or company limits
            $maxPages = $this->record->max_pages ?? 0; // 0 means no limit
            $maxCompanies = 0; // 0 means no limit
            
            // Dispatch the background job
            CrawlCompanyListJob::dispatch(
                $this->record, 
                $maxPages, 
                $maxCompanies, 
                auth()->id()
            )->onQueue('crawler');
            
            Notification::make()
                ->info()
                ->title('Full Crawling Started!')
                ->body("Background crawling job has been queued for '{$this->record->name}' without limits. You'll receive a notification when it's complete.")
                ->actions([
                    \Filament\Notifications\Actions\Action::make('view_progress')
                        ->button()
                        ->action('checkProgress')
                        ->label('Check Progress'),
                    \Filament\Notifications\Actions\Action::make('view_queue')
                        ->button()
                        ->url('/horizon')
                        ->openUrlInNewTab()
                        ->label('View Queue')
                        ->visible(config('queue.default') === 'redis'),
                ])
                ->duration(10000)
                ->send();
                
        } catch (\Exception $e) {
            Notification::make()
                ->danger()
                ->title('Failed to Start Crawling')
                ->body('Error: ' . $e->getMessage())
                ->duration(8000)
                ->send();
        }
    }
    
    /**
     * Run synchronous crawl
     */
    protected function runSynchronousCrawl(): void
    {
        try {
            // Use unlimited crawling - no page or company limits
            $maxPages = $this->record->max_pages ?? 0; // 0 means no limit
            $maxCompanies = 0; // 0 means no limit
            
            // Show immediate progress notification
            Notification::make()
                ->info()
                ->title('Full Crawling Started')
                ->body('Running synchronous crawl without limits. This may take a while...')
                ->duration(5000)
                ->send();
            
            // Dispatch job and wait for completion (synchronous behavior)
            CrawlCompanyListJob::dispatchSync(
                $this->record,
                $maxPages ?: 999,
                $maxCompanies ?: 99999,
                auth()->id()
            );
            
            // Refresh the record to get updated company count
            $this->record->refresh();
            $companiesCount = $this->record->companies()->count();
            
            // Update the last_crawled_at timestamp
            $this->record->update(['last_crawled_at' => now()]);
            
            Notification::make()
                ->success()
                ->title('Full Crawling Completed Successfully!')
                ->body("Found and imported {$companiesCount} companies from '{$this->record->name}' without limits.")
                ->actions([
                    \Filament\Notifications\Actions\Action::make('view_companies')
                        ->button()
                        ->url(route('filament.admin.resources.companies.index', ['tableFilters[data_source_id][value]' => $this->record->id]))
                        ->visible($companiesCount > 0)
                        ->label('View Companies'),
                ])
                ->duration(10000)
                ->send();
            
        } catch (\Exception $e) {
            Notification::make()
                ->danger()
                ->title('Crawling Error')
                ->body('Error: ' . $e->getMessage())
                ->duration(8000)
                ->send();
        }
    }
    
    /**
     * Check crawling progress
     */
    public function checkProgress(): void
    {
        $cacheKey = "crawl_progress_{$this->record->id}";
        $progress = Cache::get($cacheKey);
        
        if ($progress) {
            $percent = $progress['percent'] ?? 0;
            $message = $progress['message'] ?? 'Processing...';
            $companiesFound = $progress['companies_found'] ?? 0;
            $pagesProcessed = $progress['pages_processed'] ?? 0;
            $isComplete = $progress['is_complete'] ?? false;
            
            if ($isComplete) {
                // Job is complete, refresh the record and show final stats
                $this->record->refresh();
                $totalCompanies = $this->record->companies()->count();
                
                Notification::make()
                    ->success()
                    ->title('Crawling Completed!')
                    ->body("Successfully crawled {$totalCompanies} companies from '{$this->record->name}'.")
                    ->actions([
                        \Filament\Notifications\Actions\Action::make('view_companies')
                            ->button()
                            ->url(route('filament.admin.resources.companies.index', ['tableFilters[data_source_id][value]' => $this->record->id]))
                            ->visible($totalCompanies > 0)
                            ->label('View Companies'),
                        \Filament\Notifications\Actions\Action::make('refresh_page')
                            ->button()
                            ->action(function () {
                                $this->redirect(request()->header('Referer'));
                            })
                            ->label('Refresh Page'),
                    ])
                    ->duration(10000)
                    ->send();
                    
                // Clear the progress cache
                Cache::forget($cacheKey);
            } else {
                Notification::make()
                    ->info()
                    ->title('Crawling in Progress')
                    ->body("Progress: {$percent}% - {$message}" . 
                           ($companiesFound > 0 ? " ({$companiesFound} companies found)" : "") .
                           ($pagesProcessed > 0 ? " ({$pagesProcessed} pages processed)" : ""))
                    ->actions([
                        \Filament\Notifications\Actions\Action::make('check_again')
                            ->button()
                            ->action('checkProgress')
                            ->label('Check Again'),
                    ])
                    ->duration(8000)
                    ->send();
            }
        } else {
            // Check if there are any companies and when was the last crawl
            $companiesCount = $this->record->companies()->count();
            $lastCrawled = $this->record->last_crawled_at;
            
            Notification::make()
                ->warning()
                ->title('No Active Crawling Job')
                ->body('No active crawling job found for this data source. ' . 
                       ($companiesCount > 0 ? 
                        "Current companies: {$companiesCount}. " . 
                        ($lastCrawled ? "Last crawled: {$lastCrawled->diffForHumans()}." : "Never crawled.") :
                        "No companies found yet."))
                ->actions([
                    \Filament\Notifications\Actions\Action::make('start_crawl')
                        ->button()
                        ->action(function () {
                            // Trigger the crawl now action
                            $this->mountAction('crawl_now');
                        })
                        ->label('Start Crawling'),
                ])
                ->duration(8000)
                ->send();
        }
    }
}
