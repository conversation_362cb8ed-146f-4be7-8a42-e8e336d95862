<?php

namespace App\Filament\Resources\DataSourceResource\Pages;

use App\Filament\Resources\DataSourceResource;
use App\Models\DataSource;
use App\Models\Company;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Notifications\Notification;

class ListDataSources extends ListRecords
{
    protected static string $resource = DataSourceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
            Actions\Action::make('refresh_all_stats')
                ->label('Refresh All')
                ->icon('heroicon-o-arrow-path')
                ->color('success')
                ->action(function () {
                    $this->redirect(request()->header('Referer'));
                })
        ];
    }
}
