<?php

namespace App\Filament\Resources\SettingResource\Pages;

use App\Filament\Resources\SettingResource;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Cache;

class CreateSetting extends CreateRecord
{
    protected static string $resource = SettingResource::class;

    public function getTitle(): string
    {
        return __('settings.pages.create.title');
    }

    protected function afterCreate(): void
    {
        // Clear cache when creating new setting
        Cache::forget("setting_{$this->record->key}");
    }
}
