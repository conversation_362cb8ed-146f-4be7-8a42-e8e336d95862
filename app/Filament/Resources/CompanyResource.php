<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CompanyResource\Pages;
use App\Filament\Resources\CompanyResource\RelationManagers;
use App\Models\Company;
use App\Models\DataSource;
use App\Models\Category;
use App\Models\Keyword;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Actions\ActionGroup;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\KeyValue;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\BadgeColumn;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Services\WebCrawlerService;
use App\Services\ExecutiveEmailService;
use Filament\Notifications\Notification;
use Symfony\Component\DomCrawler\Crawler;
use App\Imports\CompanyImport;
use App\Exports\CompanyEmailsExport;
use Filament\Forms\Components\FileUpload;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Log;

class CompanyResource extends Resource
{
    protected static ?string $model = Company::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-office';

    protected static ?string $navigationLabel = 'Companies';

    protected static ?int $navigationSort = 10;

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Tabs::make('Company Information')
                    ->tabs([
                        Tabs\Tab::make('Basic Info')
                            ->schema([
                                Section::make('Company Details')
                                    ->schema([
                                        TextInput::make('name')
                                            ->required()
                                            ->maxLength(255),

                                        TextInput::make('website')
                                            ->url()
                                            ->maxLength(255),

                                        Select::make('data_source_id')
                                            ->label('Data Source')
                                            ->relationship('dataSource', 'name')
                                            ->required(),

                                        TextInput::make('industry')
                                            ->maxLength(255),

                                        TextInput::make('employee_count')
                                            ->label('Employee Count')
                                            ->maxLength(255),

                                        TextInput::make('founded_year')
                                            ->label('Founded Year')
                                            ->numeric()
                                            ->minValue(1800)
                                            ->maxValue(date('Y')),
                                    ])->columns(2),

                                Section::make('Contact Information')
                                    ->schema([
                                        Textarea::make('address')
                                            ->rows(2)
                                            ->columnSpanFull(),

                                        TextInput::make('phone')
                                            ->tel()
                                            ->maxLength(255),

                                        TextInput::make('email')
                                            ->email()
                                            ->maxLength(255),
                                    ])->columns(2),

                                Section::make('Description')
                                    ->schema([
                                        Textarea::make('description')
                                            ->rows(4)
                                            ->columnSpanFull(),
                                    ]),
                            ]),

                        Tabs\Tab::make('Executives')
                            ->schema([
                                Repeater::make('executives')
                                    ->label('Company Executives')
                                    ->schema([
                                        TextInput::make('name')
                                            ->required()
                                            ->maxLength(255),

                                        TextInput::make('position')
                                            ->required()
                                            ->maxLength(255),

                                        Select::make('level')
                                            ->options([
                                                'c_level' => 'C-Level',
                                                'director' => 'Director',
                                                'manager' => 'Manager',
                                            ]),

                                        TextInput::make('email')
                                            ->email()
                                            ->maxLength(255),

                                        TextInput::make('phone')
                                            ->tel()
                                            ->maxLength(255),

                                        TextInput::make('name_romaji')
                                            ->label('Name (Romaji)')
                                            ->maxLength(255)
                                            ->disabled(),



                                        Forms\Components\Hidden::make('email_predictions')
                                            ->dehydrateStateUsing(fn($state) => is_array($state) ? $state : []),

                                        Forms\Components\Placeholder::make('email_predictions_display')
                                            ->label('Email Predictions')
                                            ->content(function ($get) {
                                                $predictions = $get('email_predictions') ?? [];
                                                if (empty($predictions) || !is_array($predictions)) {
                                                    return new \Illuminate\Support\HtmlString('
                                                        <div class="text-gray-500 text-sm italic">
                                                            No email predictions generated yet. Run executive crawl to generate predictions.
                                                        </div>
                                                    ');
                                                }

                                                $html = '<div class="space-y-2">';
                                                $html .= '<p class="text-sm text-gray-600 mb-3">📧 Generated ' . count($predictions) . ' email predictions:</p>';

                                                foreach ($predictions as $index => $prediction) {
                                                    $html .= '<div class="flex items-center justify-between p-2 bg-gray-50 rounded text-sm border">';
                                                    $html .= '<div class="flex items-center space-x-2">';
                                                    $html .= '<span class="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-800 rounded-full flex items-center justify-center text-xs font-medium">' . ($index + 1) . '</span>';
                                                    $html .= '<code class="font-mono text-gray-900">' . htmlspecialchars($prediction) . '</code>';
                                                    $html .= '</div>';
                                                    $html .= '<button type="button" onclick="navigator.clipboard.writeText(\'' . htmlspecialchars($prediction) . '\').then(() => { this.textContent = \'Copied!\'; setTimeout(() => this.textContent = \'Copy\', 2000); })" class="text-blue-600 hover:text-blue-800 text-xs px-2 py-1 rounded border border-blue-200 hover:bg-blue-50 transition-colors">Copy</button>';
                                                    $html .= '</div>';
                                                }

                                                $html .= '</div>';

                                                return new \Illuminate\Support\HtmlString($html);
                                            })
                                            ->columnSpanFull(),

                                        Textarea::make('bio')
                                            ->label('Biography')
                                            ->rows(2)
                                            ->columnSpanFull(),
                                    ])
                                    ->columns(2)
                                    ->collapsible()
                                    ->itemLabel(fn(array $state): ?string => $state['name'] ?? null),
                            ]),

                        Tabs\Tab::make('Crawl Data')
                            ->schema([
                                Section::make('URLs')
                                    ->schema([
                                        TextInput::make('source_url')
                                            ->label('Source URL')
                                            ->url()
                                            ->maxLength(255)
                                            ->columnSpanFull(),

                                        TextInput::make('info_page_url')
                                            ->label('Info Page URL')
                                            ->url()
                                            ->maxLength(255)
                                            ->columnSpanFull(),
                                    ]),

                                Section::make('Status & Metadata')
                                    ->schema([
                                        Select::make('status')
                                            ->options([
                                                'pending' => 'Pending',
                                                'processing' => 'Processing',
                                                'completed' => 'Completed',
                                                'failed' => 'Failed',
                                            ])
                                            ->default('pending'),

                                        TextInput::make('crawl_attempts')
                                            ->label('Crawl Attempts')
                                            ->numeric()
                                            ->default(0),

                                        KeyValue::make('crawl_metadata')
                                            ->label('Crawl Metadata')
                                            ->keyLabel('Key')
                                            ->valueLabel('Value')
                                            ->reorderable(false)
                                            ->default([])
                                            ->formatStateUsing(function ($state) {
                                                if (!is_array($state) || empty($state)) {
                                                    return [];
                                                }

                                                // Convert nested arrays to string representation
                                                $flattened = [];
                                                foreach ($state as $key => $value) {
                                                    if (is_array($value)) {
                                                        $flattened[$key] = json_encode($value);
                                                    } elseif (is_bool($value)) {
                                                        $flattened[$key] = $value ? 'true' : 'false';
                                                    } elseif ($value === null) {
                                                        $flattened[$key] = 'null';
                                                    } else {
                                                        $flattened[$key] = (string) $value;
                                                    }
                                                }
                                                return $flattened;
                                            })
                                            ->dehydrateStateUsing(function ($state) {
                                                if (!is_array($state) || empty($state)) {
                                                    return null;
                                                }

                                                // Convert JSON strings back to arrays where appropriate
                                                $result = [];
                                                foreach ($state as $key => $value) {
                                                    if (is_string($value)) {
                                                        // Try to parse as JSON first
                                                        $decoded = json_decode($value, true);
                                                        if (json_last_error() === JSON_ERROR_NONE) {
                                                            $result[$key] = $decoded;
                                                        } elseif ($value === 'true') {
                                                            $result[$key] = true;
                                                        } elseif ($value === 'false') {
                                                            $result[$key] = false;
                                                        } elseif ($value === 'null') {
                                                            $result[$key] = null;
                                                        } elseif (is_numeric($value)) {
                                                            $result[$key] = is_float($value) ? (float) $value : (int) $value;
                                                        } else {
                                                            $result[$key] = $value;
                                                        }
                                                    } else {
                                                        $result[$key] = $value;
                                                    }
                                                }
                                                return $result;
                                            })
                                            ->columnSpanFull(),
                                    ])->columns(2),
                            ]),
                    ])
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),

                TextColumn::make('website')
                    ->limit(30)
                    ->tooltip(function (TextColumn $column): ?string {
                        $state = $column->getState();
                        return strlen($state) > 30 ? $state : null;
                    })
                    ->url(fn($record) => $record->website)
                    ->openUrlInNewTab(),

                TextColumn::make('industry')
                    ->searchable()
                    ->placeholder('N/A'),

                BadgeColumn::make('status')
                    ->colors([
                        'warning' => 'pending',
                        'info' => 'processing',
                        'success' => 'completed',
                        'danger' => 'failed',
                    ]),

                TextColumn::make('dataSource.name')
                    ->label('Data Source')
                    ->sortable(),

                TextColumn::make('campaign.name')
                    ->label('Campaign')
                    ->limit(20)
                    ->placeholder('No campaign')
                    ->url(fn($record) => $record->campaign ? route('filament.admin.resources.campaigns.view', $record->campaign) : null)
                    ->color('info')
                    ->tooltip(function ($record) {
                        if (!$record->campaign) {
                            return 'Not part of any campaign';
                        }
                        $campaign = $record->campaign;
                        return "Campaign: {$campaign->name}\nType: {$campaign->type}\nStatus: {$campaign->status}";
                    }),

                TextColumn::make('executives_count')
                    ->label('Executives')
                    ->getStateUsing(fn($record) => count($record->executives ?? []))
                    ->tooltip(function ($record): ?string {
                        $executives = $record->executives ?? [];
                        if (empty($executives)) {
                            return 'No executives found';
                        }

                        $tooltip = "Executives:\n";
                        $count = 0;
                        foreach ($executives as $exec) {
                            if ($count >= 5) {
                                $tooltip .= "... and " . (count($executives) - 5) . " more";
                                break;
                            }
                            $name = $exec['name'] ?? 'Unknown';
                            $position = $exec['position'] ?? '';
                            $romaji = isset($exec['name_romaji']) && !empty($exec['name_romaji']) ? " ({$exec['name_romaji']})" : '';

                            $tooltip .= "• {$name}{$romaji}\n";
                            if ($position) {
                                $tooltip .= "  Position: {$position}\n";
                            }
                            $tooltip .= "\n";
                            $count++;
                        }

                        return trim($tooltip);
                    }),

                TextColumn::make('email_predictions')
                    ->label('Email Predictions')
                    ->getStateUsing(function ($record): string {
                        $executives = $record->executives ?? [];
                        if (empty($executives)) {
                            return 'No executives';
                        }

                        $allEmails = [];
                        foreach ($executives as $exec) {
                            $predictions = $exec['email_predictions'] ?? [];
                            foreach ($predictions as $email) {
                                if (!in_array($email, $allEmails)) {
                                    $allEmails[] = $email;
                                }
                            }
                        }

                        if (empty($allEmails)) {
                            return 'No predictions';
                        }

                        // Show all emails directly in the column
                        return implode("\n", $allEmails);
                    })
                    ->html()
                    ->formatStateUsing(function (string $state): \Illuminate\Support\HtmlString {
                        if ($state === 'No executives' || $state === 'No predictions') {
                            return new \Illuminate\Support\HtmlString(
                                '<span class="text-gray-500 italic">' . $state . '</span>'
                            );
                        }

                        $emails = explode("\n", $state);
                        $html = '<div class="space-y-1 max-w-sm">';

                        foreach ($emails as $email) {
                            if (!empty(trim($email))) {
                                $html .= '<div class="text-xs font-mono bg-blue-50 text-blue-800 px-2 py-1 rounded border break-all">' .
                                    htmlspecialchars(trim($email)) . '</div>';
                            }
                        }

                        $html .= '</div>';

                        return new \Illuminate\Support\HtmlString($html);
                    })
                    ->width('300px')
                    ->wrap(),

                TextColumn::make('employee_count')
                    ->label('Employees')
                    ->placeholder('N/A'),

                TextColumn::make('last_crawled_at')
                    ->label('Last Crawled')
                    ->dateTime()
                    ->sortable()
                    ->placeholder('Never'),

                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options([
                        'pending' => 'Pending',
                        'completed' => 'Completed',
                        'failed' => 'Failed',
                    ]),

                SelectFilter::make('data_source_id')
                    ->label('Data Source')
                    ->relationship('dataSource', 'name')
                    ->searchable()
                    ->preload(),

                SelectFilter::make('campaign_id')
                    ->label('Campaign')
                    ->relationship('campaign', 'name')
                    ->searchable()
                    ->preload()
                    ->placeholder('All campaigns'),

                SelectFilter::make('email_prediction_status')
                    ->label('Email Predictions')
                    ->options([
                        'has_predictions' => 'Has Email Predictions',
                        'no_predictions' => 'No Email Predictions',
                        'has_executives_no_predictions' => 'Has Executives but No Predictions',
                    ])
                    ->placeholder('All companies')
                    ->default('has_predictions')
                    ->query(function (Builder $query, array $data): Builder {
                        $status = $data['value'] ?? null;

                        if (!$status) {
                            return $query;
                        }

                        return match ($status) {
                            'has_predictions' => $query->whereRaw("
                                JSON_LENGTH(executives) > 0
                                AND JSON_UNQUOTE(JSON_EXTRACT(executives, '$[*].email_predictions')) != 'null'
                                AND JSON_EXTRACT(executives, '$[*].email_predictions[0]') IS NOT NULL
                            "),

                            'no_predictions' => $query->where(function (Builder $subQuery) {
                                $subQuery->whereNull('executives')
                                    ->orWhereRaw('JSON_LENGTH(executives) = 0')
                                    ->orWhereRaw("JSON_EXTRACT(executives, '$[*].email_predictions[0]') IS NULL");
                            }),

                            'has_executives_no_predictions' => $query->whereRaw('JSON_LENGTH(executives) > 0')
                                ->whereRaw("JSON_EXTRACT(executives, '$[*].email_predictions[0]') IS NULL"),

                            default => $query,
                        };
                    }),

                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                // Main actions (always visible)
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),

                // Grouped actions dropdown
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\Action::make('view_executives')
                        ->label('View Executives')
                        ->icon('heroicon-o-users')
                        ->color('info')
                        ->visible(fn($record) => !empty($record->executives))
                        ->modalHeading(fn($record) => "Executives - {$record->name}")
                        ->modalContent(fn($record) => view('filament.components.executives-detail', [
                            'executives' => $record->executives ?? [],
                            'company' => $record
                        ]))
                        ->modalWidth('7xl')
                        ->action(function () {
                            // No action needed, just showing modal
                        }),
                    Tables\Actions\Action::make('predict_emails')
                        ->label('🔮 Predict Emails')
                        ->icon('heroicon-o-at-symbol')
                        ->color('warning')
                        ->visible(function ($record) {
                            // Show only if company has executives and website
                            return !empty($record->executives) && !empty($record->website);
                        })
                        ->form([
                            Forms\Components\Section::make('Email Prediction Settings')
                                ->schema([
                                    Forms\Components\Toggle::make('overwrite_existing')
                                        ->label('Overwrite Existing Predictions')
                                        ->helperText('If enabled, will regenerate predictions for executives who already have them')
                                        ->default(true),
                                    Forms\Components\Placeholder::make('executives_info')
                                        ->label('Executives Summary')
                                        ->content(function ($record) {
                                            $executives = $record->executives ?? [];
                                            $total = count($executives);
                                            $withPredictions = 0;

                                            foreach ($executives as $exec) {
                                                if (!empty($exec['email_predictions'])) {
                                                    $withPredictions++;
                                                }
                                            }

                                            $withoutPredictions = $total - $withPredictions;

                                            return "Total executives: {$total}\nWith predictions: {$withPredictions}\nWithout predictions: {$withoutPredictions}";
                                        }),
                                ])
                        ])
                        ->modalHeading(fn($record) => "Generate Email Predictions - {$record->name}")
                        ->modalSubmitActionLabel('Queue Email Prediction Job')
                        ->modalWidth('lg')
                        ->requiresConfirmation()
                        ->action(function ($record, array $data) {
                            // Dispatch job to queue
                            \App\Jobs\PredictCompanyEmailsJob::dispatch(
                                $record,
                                \Illuminate\Support\Facades\Auth::id(),
                                $data['overwrite_existing'] ?? true
                            );

                            // Show notification
                            Notification::make()
                                ->title('Email Prediction Job Queued')
                                ->body("Email prediction job has been queued for {$record->name}. You will receive a notification when it's completed.")
                                ->success()
                                ->duration(8000)
                                ->send();
                        }),
                    Tables\Actions\Action::make('recrawl')
                        ->label('Recrawl')
                        ->icon('heroicon-o-arrow-path')
                        ->color('info')
                        ->requiresConfirmation()
                        ->modalHeading('Recrawl Company Details')
                        ->modalDescription('This will queue a job to fetch the latest company details and executives data.')
                        ->action(function ($record) {
                            // Dispatch job to queue
                            \App\Jobs\CrawlCompanyExecutivesJob::dispatch($record);

                            // Update status to processing
                            $record->update(['status' => 'processing']);

                            // Show notification
                            Notification::make()
                                ->title('Recrawl Queued')
                                ->body("Recrawl job has been queued for {$record->name}")
                                ->success()
                                ->duration(5000)
                                ->send();
                        }),
                ])
                    ->label('Actions')
                    ->icon('heroicon-m-ellipsis-horizontal')
                    ->size('sm')
                    ->color('gray')
                    ->button(),

                // Dangerous actions dropdown
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\DeleteAction::make(),
                    Tables\Actions\Action::make('add_to_blacklist')
                        ->label('Add to Blacklist')
                        ->icon('heroicon-o-no-symbol')
                        ->color('danger')
                        ->requiresConfirmation()
                        ->modalHeading('Add to Blacklist')
                        ->modalDescription('This will prevent the company from being crawled in future operations.')
                        ->action(function ($record) {
                            // Add company to blacklist
                            \App\Models\Blacklist::create([
                                'company_name' => $record->name,
                                'domain' => $record->website ? parse_url($record->website, PHP_URL_HOST) : null,
                                'reason' => 'Added via admin action',
                                'is_active' => true,
                            ]);

                            Notification::make()
                                ->title('Added to Blacklist')
                                ->body("{$record->name} has been added to blacklist")
                                ->success()
                                ->send();
                        }),
                    Tables\Actions\ForceDeleteAction::make(),
                    Tables\Actions\RestoreAction::make(),
                ])
                    ->label('Danger')
                    ->icon('heroicon-m-exclamation-triangle')
                    ->size('sm')
                    ->color('danger')
                    ->button(),
            ])
            ->headerActions([
                Tables\Actions\Action::make('import_companies')
                    ->label('Import Companies')
                    ->icon('heroicon-o-arrow-up-tray')
                    ->color('primary')
                    ->form([
                        Section::make('Data Source Configuration')
                            ->description('Select or create a data source for the imported companies')
                            ->schema([
                                Select::make('data_source_option')
                                    ->label('Data Source Option')
                                    ->options([
                                        'existing' => 'Use Existing Data Source',
                                        'create' => 'Create New Data Source',
                                    ])
                                    ->default('existing')
                                    ->required()
                                    ->reactive(),

                                Select::make('existing_data_source_id')
                                    ->label('Select Data Source')
                                    ->relationship('dataSource', 'name')
                                    ->options(DataSource::active()->pluck('name', 'id'))
                                    ->searchable()
                                    ->preload()
                                    ->visible(fn($get) => $get('data_source_option') === 'existing')
                                    ->required(fn($get) => $get('data_source_option') === 'existing'),

                                // New data source fields
                                TextInput::make('new_data_source_name')
                                    ->label('Data Source Name')
                                    ->placeholder('e.g., Excel Import - December 2024')
                                    ->visible(fn($get) => $get('data_source_option') === 'create')
                                    ->required(fn($get) => $get('data_source_option') === 'create')
                                    ->maxLength(255),

                                Select::make('new_data_source_categories')
                                    ->label('Categories')
                                    ->multiple()
                                    ->options(\App\Models\Category::active()->pluck('name', 'id'))
                                    ->searchable()
                                    ->preload()
                                    ->visible(fn($get) => $get('data_source_option') === 'create')
                                    ->helperText('Select categories for this data source'),

                                Textarea::make('new_data_source_notes')
                                    ->label('Notes')
                                    ->placeholder('Description of this data source...')
                                    ->visible(fn($get) => $get('data_source_option') === 'create')
                                    ->rows(2),
                            ]),

                        Section::make('File Upload')
                            ->schema([
                                FileUpload::make('file')
                                    ->label('Select Excel/CSV File')
                                    ->acceptedFileTypes(['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel', 'text/csv'])
                                    ->disk('public')
                                    ->directory('company-imports')
                                    ->helperText('Upload an Excel (.xlsx, .xls) or CSV file with columns: name, website')
                                    ->required()
                            ]),
                    ])
                    ->modalWidth('2xl')
                    ->action(function (array $data) {
                        try {
                            $file = $data['file'];

                            // Determine data source ID
                            $dataSourceId = null;

                            if ($data['data_source_option'] === 'existing') {
                                $dataSourceId = $data['existing_data_source_id'];
                            } else {
                                // Create new data source
                                $newDataSource = DataSource::create([
                                    'name' => $data['new_data_source_name'],
                                    'source_url' => 'Import From Excel',
                                    'notes' => $data['new_data_source_notes'] ?? null,
                                    'has_pagination' => false,
                                    'has_company_links' => false,
                                    'company_name_selector' => '.company-name', // Default selector
                                    'is_active' => true,
                                ]);

                                // Attach categories if selected
                                if (!empty($data['new_data_source_categories'])) {
                                    $newDataSource->categories()->sync($data['new_data_source_categories']);
                                }

                                $dataSourceId = $newDataSource->id;

                                Notification::make()
                                    ->title('Data Source Created')
                                    ->body("New data source '{$newDataSource->name}' has been created")
                                    ->info()
                                    ->duration(5000)
                                    ->send();
                            }

                            // Build full file path - file is stored in storage/app/public/company-imports/
                            $filePath = storage_path('app/public/' . $file);

                            // Check if file exists
                            if (!file_exists($filePath)) {
                                throw new \Exception("File [{$file}] does not exist at path: {$filePath}");
                            }

                            $import = new CompanyImport($dataSourceId);
                            Excel::import($import, $filePath);

                            $summary = $import->getImportSummary();

                            // Create notification based on results
                            $message = sprintf(
                                "Import completed: %d companies imported, %d skipped, %d blacklisted",
                                $summary['imported'],
                                $summary['skipped'],
                                $summary['blacklisted']
                            );

                            Notification::make()
                                ->title('Company Import Completed')
                                ->body($message)
                                ->success()
                                ->duration(8000)
                                ->send();

                            // Clean up uploaded file
                            try {
                                if (file_exists($filePath)) {
                                    unlink($filePath);
                                }
                            } catch (\Exception $e) {
                                // Ignore cleanup errors
                            }

                            // Show detailed errors if any
                            if (!empty($summary['custom_errors']) || !empty($summary['validation_failures'])) {
                                $errorMessage = "Some rows had issues:\n";

                                foreach (array_slice($summary['custom_errors'], 0, 5) as $error) {
                                    $errorMessage .= "• $error\n";
                                }

                                if (count($summary['custom_errors']) > 5) {
                                    $errorMessage .= "• ... and " . (count($summary['custom_errors']) - 5) . " more errors\n";
                                }

                                Notification::make()
                                    ->title('Import Warnings')
                                    ->body($errorMessage)
                                    ->warning()
                                    ->duration(10000)
                                    ->send();
                            }
                        } catch (\Exception $e) {
                            Notification::make()
                                ->title('Import Failed')
                                ->body('Error: ' . $e->getMessage())
                                ->danger()
                                ->duration(8000)
                                ->send();
                        }
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                    Tables\Actions\BulkAction::make('download_emails')
                        ->label('Download Emails')
                        ->icon('heroicon-o-arrow-down-tray')
                        ->color('success')
                        ->requiresConfirmation()
                        ->modalHeading('Download Company Emails')
                        ->modalDescription('This will download an Excel file containing ONLY companies with valid email predictions. Companies without executives or email predictions will be excluded. Each executive email will be on a separate row.')
                        ->modalSubmitActionLabel('Download Excel')
                        ->action(function (\Illuminate\Database\Eloquent\Collection $records) {
                            // Filter companies with executives and email predictions
                            $companiesWithEmails = $records->filter(function ($company) {
                                $executives = $company->executives ?? [];
                                if (empty($executives)) {
                                    return false;
                                }

                                // Check if any executive has email predictions
                                foreach ($executives as $exec) {
                                    if (!empty($exec['email_predictions'] ?? [])) {
                                        return true;
                                    }
                                }
                                return false;
                            });

                            $totalSelected = $records->count();
                            $companiesWithEmailsCount = $companiesWithEmails->count();
                            $excludedCount = $totalSelected - $companiesWithEmailsCount;

                            if ($companiesWithEmails->isEmpty()) {
                                Notification::make()
                                    ->title('No Email Data Found')
                                    ->body("None of the {$totalSelected} selected companies have email predictions. Please crawl executive data first.")
                                    ->warning()
                                    ->duration(8000)
                                    ->send();
                                return;
                            }

                            // Create filename with timestamp
                            $timestamp = now()->format('Y-m-d_H-i-s');
                            $filename = "company_emails_{$timestamp}.xlsx";

                            // Download the Excel file
                            try {
                                $result = Excel::download(
                                    new CompanyEmailsExport($records),
                                    $filename
                                );

                                // Show success notification with details
                                $message = "Excel file generated successfully! Included {$companiesWithEmailsCount} companies with email predictions.";
                                if ($excludedCount > 0) {
                                    $message .= " Excluded {$excludedCount} companies without email predictions.";
                                }

                                Notification::make()
                                    ->title('Email Export Completed')
                                    ->body($message)
                                    ->success()
                                    ->duration(8000)
                                    ->send();

                                return $result;
                            } catch (\Exception $e) {
                                Notification::make()
                                    ->title('Export Failed')
                                    ->body('Error generating Excel file: ' . $e->getMessage())
                                    ->danger()
                                    ->duration(8000)
                                    ->send();
                            }
                        }),
                    Tables\Actions\BulkAction::make('bulk_crawl')
                        ->label('Crawl Executive Data')
                        ->icon('heroicon-o-magnifying-glass')
                        ->color('info')
                        ->requiresConfirmation()
                        ->modalHeading('Crawl Executive Data for Selected Companies')
                        ->modalDescription('This will queue jobs to crawl executive information for all selected companies. Only companies with valid websites will be processed.')
                        ->modalSubmitActionLabel('Start Crawling')
                        ->action(function (\Illuminate\Database\Eloquent\Collection $records) {
                            $queuedCount = 0;
                            $skippedCount = 0;
                            $skippedReasons = [];

                            foreach ($records as $company) {
                                // Check if company has a valid website
                                if (!$company->website || empty(trim($company->website))) {
                                    $skippedCount++;
                                    $skippedReasons[] = "{$company->name}: No website";
                                    continue;
                                }

                                // Check if company is blacklisted
                                if ($company->isBlacklisted()) {
                                    $skippedCount++;
                                    $skippedReasons[] = "{$company->name}: Blacklisted";
                                    continue;
                                }

                                // Dispatch crawl job
                                \App\Jobs\CrawlCompanyExecutivesJob::dispatch($company);

                                // Update company status
                                $company->update(['status' => 'processing']);

                                $queuedCount++;
                            }

                            // Show success notification
                            $message = "Queued {$queuedCount} companies for executive crawling";
                            if ($skippedCount > 0) {
                                $message .= ", skipped {$skippedCount} companies";
                            }

                            Notification::make()
                                ->title('Bulk Crawl Initiated')
                                ->body($message)
                                ->success()
                                ->duration(8000)
                                ->send();

                            // Show detailed skip reasons if any
                            if (!empty($skippedReasons)) {
                                $skipMessage = "Skipped companies:\n";
                                foreach (array_slice($skippedReasons, 0, 5) as $reason) {
                                    $skipMessage .= "• $reason\n";
                                }

                                if (count($skippedReasons) > 5) {
                                    $skipMessage .= "• ... and " . (count($skippedReasons) - 5) . " more";
                                }

                                Notification::make()
                                    ->title('Skipped Companies')
                                    ->body($skipMessage)
                                    ->warning()
                                    ->duration(10000)
                                    ->send();
                            }
                        }),
                    Tables\Actions\BulkAction::make('bulk_predict_emails')
                        ->label('🔮 Predict Emails')
                        ->icon('heroicon-o-at-symbol')
                        ->color('warning')
                        ->form([
                            Forms\Components\Section::make('Bulk Email Prediction Settings')
                                ->schema([
                                    Forms\Components\Toggle::make('overwrite_existing')
                                        ->label('Overwrite Existing Predictions')
                                        ->helperText('If enabled, will regenerate predictions for executives who already have them')
                                        ->default(true),
                                    Forms\Components\Placeholder::make('batch_info')
                                        ->label('Batch Information')
                                        ->content(function (\Illuminate\Database\Eloquent\Collection $records) {
                                            $total = $records->count();
                                            $withExecutives = 0;
                                            $withWebsite = 0;
                                            $processable = 0;

                                            foreach ($records as $company) {
                                                if (!empty($company->executives)) {
                                                    $withExecutives++;
                                                }
                                                if (!empty($company->website)) {
                                                    $withWebsite++;
                                                }
                                                if (!empty($company->executives) && !empty($company->website)) {
                                                    $processable++;
                                                }
                                            }

                                            return "Selected companies: {$total}\nWith executives: {$withExecutives}\nWith website: {$withWebsite}\nProcessable: {$processable}";
                                        }),
                                ])
                        ])
                        ->requiresConfirmation()
                        ->modalHeading('Generate Email Predictions for Selected Companies')
                        ->modalDescription('This will queue email prediction jobs for all selected companies. Only companies with executives and websites will be processed.')
                        ->modalSubmitActionLabel('Queue Prediction Jobs')
                        ->action(function (\Illuminate\Database\Eloquent\Collection $records, array $data) {
                            $queuedCount = 0;
                            $skippedCount = 0;
                            $skippedReasons = [];

                            foreach ($records as $company) {
                                // Check if company has executives and website
                                if (empty($company->executives) || empty($company->website)) {
                                    $skippedCount++;
                                    $reason = "{$company->name}: ";
                                    if (empty($company->executives)) {
                                        $reason .= "No executives";
                                    } else {
                                        $reason .= "No website";
                                    }
                                    $skippedReasons[] = $reason;
                                    continue;
                                }

                                // Dispatch job to queue
                                \App\Jobs\PredictCompanyEmailsJob::dispatch(
                                    $company,
                                    \Illuminate\Support\Facades\Auth::id(),
                                    $data['overwrite_existing'] ?? true
                                );

                                $queuedCount++;
                            }

                            // Show success notification
                            $message = "Queued {$queuedCount} email prediction jobs";
                            if ($skippedCount > 0) {
                                $message .= ", skipped {$skippedCount} companies";
                            }

                            Notification::make()
                                ->title('Bulk Email Prediction Jobs Queued')
                                ->body($message . ". You will receive notifications as jobs complete.")
                                ->success()
                                ->duration(8000)
                                ->send();

                            // Show detailed skip reasons if any
                            if (!empty($skippedReasons)) {
                                $skipMessage = "Skipped companies:\n";
                                foreach (array_slice($skippedReasons, 0, 5) as $reason) {
                                    $skipMessage .= "• $reason\n";
                                }

                                if (count($skippedReasons) > 5) {
                                    $skipMessage .= "• ... and " . (count($skippedReasons) - 5) . " more";
                                }

                                Notification::make()
                                    ->title('Skipped Companies')
                                    ->body($skipMessage)
                                    ->warning()
                                    ->duration(10000)
                                    ->send();
                            }
                        }),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCompanies::route('/'),
            'create' => Pages\CreateCompany::route('/create'),
            'edit' => Pages\EditCompany::route('/{record}/edit'),
        ];
    }



    private static function callPythonEmailPrediction(string $name, string $domain): array
    {
        try {
            if (!$name || !$domain || trim($name) === '' || trim($domain) === '') {
                return [];
            }

            // Path to the Python email prediction script (matching crawl-executives.js logic)
            $scriptsPath = base_path('crawl-executives-scripts');
            $scriptPath = $scriptsPath . '/generate_email_predictions.py';

            if (!file_exists($scriptPath)) {
                throw new \Exception("Python script not found at: {$scriptPath}");
            }

            // Execute Python script with virtual environment but NO SMTP validation
            // (exactly matching the crawl-executives.js logic)
            $venvPath = $scriptsPath . '/venv';
            $pythonPath = $venvPath . '/bin/python3';

            // Clean the name and domain for command (prevent injection)
            $cleanName = str_replace('"', '\\"', $name);
            $cleanDomain = str_replace('"', '\\"', $domain);

            // Skip SMTP validation in Python script (matching JS logic)
            // Redirect stderr to /dev/null to avoid log messages mixing with JSON
            $command = 'cd "' . $scriptsPath . '" && "' . $pythonPath . '" generate_email_predictions.py "' . $cleanName . '" "' . $cleanDomain . '" --no-validation 2>/dev/null';

            Log::info("📧 Generating email patterns for \"{$name}\" at {$domain} using Python (no SMTP)", [
                'command' => $command,
                'name' => $name,
                'domain' => $domain
            ]);

            $output = shell_exec($command);

            if ($output === null) {
                throw new \Exception("Failed to execute Python script");
            }

            // Parse JSON response (output should be clean now with stderr redirected)
            $jsonResponse = json_decode(trim($output), true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::error("Failed to parse Python script JSON", [
                    'output' => $output,
                    'json_error' => json_last_error_msg(),
                    'command' => $command
                ]);
                throw new \Exception("Invalid JSON output from Python script");
            }

            // Check if script was successful (matching JS logic)
            if (!isset($jsonResponse['success']) || !$jsonResponse['success']) {
                $error = $jsonResponse['error'] ?? 'Unknown error';
                Log::warning("Python email prediction script failed for \"{$name}\": {$error}");
                return [];
            }

            // Extract all email predictions (no SMTP filtering needed) - matching JS logic
            $predictions = $jsonResponse['predictions'] ?? [];
            $allEmails = [];
            foreach ($predictions as $p) {
                $email = is_array($p) ? ($p['email'] ?? '') : $p;
                if ($email && strlen($email) > 0) {
                    $allEmails[] = $email;
                }
            }

            Log::info("📧 Python generated " . count($allEmails) . " email patterns for \"{$name}\"");

            // Apply API validation like crawl-executives.js does
            if (!empty($allEmails)) {
                $validatedEmails = self::validateEmailsWithAPI($allEmails, $name);
                Log::info("📧 API validation completed for \"{$name}\": " . count($validatedEmails) . "/" . count($allEmails) . " emails passed");
                return $validatedEmails;
            }

            return $allEmails;
        } catch (\Exception $e) {
            Log::error("Python email generation failed for \"{$name}\": " . $e->getMessage(), [
                'name' => $name,
                'domain' => $domain,
                'error' => $e->getMessage()
            ]);

            // Fallback to PHP-based prediction if Python fails
            return self::fallbackEmailPrediction($name, $domain);
        }
    }

    private static function validateEmailsWithAPI(array $emails, string $executiveName): array
    {
        if (empty($emails)) {
            return [];
        }

        try {
            Log::info("🔍 Running API email validation for \"{$executiveName}\" on " . count($emails) . " emails");

            $apiUrl = 'http://14.224.203.110:3472/api/validate-emails';
            $apiKey = 'SrmnTK1itOO8vJzEVFQiEG9kltXHxG9B';

            // Prepare curl command with proper escaping (matching crawl-executives.js)
            $payload = json_encode(['emails' => $emails]);
            $escapedPayload = str_replace('"', '\\"', $payload);

            $curlCommand = sprintf(
                'curl -X POST "%s" -H "Content-Type: application/json" -H "X-API-Key: %s" -d "%s" --connect-timeout 30 --max-time 60 --silent',
                $apiUrl,
                $apiKey,
                $escapedPayload
            );

            // Execute curl command
            $result = shell_exec($curlCommand);

            if ($result === null) {
                Log::error("❌ API validation failed for \"{$executiveName}\": curl command failed");
                Log::warning("⚠️ Skipping API validation, returning original emails");
                return $emails; // Return original emails if API fails
            }

            // Parse API response
            $response = json_decode(trim($result), true);

            if (json_last_error() !== JSON_ERROR_NONE || !isset($response['results'])) {
                Log::error("❌ Invalid API response format for \"{$executiveName}\"", [
                    'response' => $result,
                    'json_error' => json_last_error_msg()
                ]);
                Log::warning("⚠️ Skipping API validation, returning original emails");
                return $emails; // Return original emails if parsing fails
            }

            // Extract valid emails (matching crawl-executives.js logic)
            $validatedEmails = [];
            foreach ($response['results'] as $resultItem) {
                if (isset($resultItem['is_valid']) && $resultItem['is_valid'] === true) {
                    $validatedEmails[] = $resultItem['email'];
                }
            }

            // Log validation summary
            $summary = $response['summary'] ?? [];
            $validCount = $summary['valid'] ?? count($validatedEmails);
            $totalCount = $summary['total'] ?? count($emails);

            Log::info("📧 API validation completed for \"{$executiveName}\": {$validCount}/{$totalCount} emails passed");

            if (!empty($validatedEmails)) {
                Log::debug("✅ Valid emails found", ['emails' => array_slice($validatedEmails, 0, 3)]);
            } else {
                Log::warning("❌ No valid emails found for \"{$executiveName}\"");
            }

            return $validatedEmails;
        } catch (\Exception $e) {
            Log::error("❌ Failed to validate emails via API: " . $e->getMessage(), [
                'executive' => $executiveName,
                'emails_count' => count($emails),
                'error' => $e->getMessage()
            ]);
            Log::warning("⚠️ Skipping API validation, returning original emails");
            return $emails; // Return original emails if validation fails
        }
    }

    private static function fallbackEmailPrediction(string $name, string $domain): array
    {
        try {
            // Use the existing ExecutiveEmailService as fallback
            $emailService = new \App\Services\ExecutiveEmailService();

            // Clean domain (remove protocol and www)
            $cleanDomain = str_replace(['https://', 'http://', 'www.'], '', $domain);
            $cleanDomain = strtok($cleanDomain, '/'); // Remove path

            $predictions = $emailService->generateEmailPredictions($name, $cleanDomain);

            // Apply API validation to fallback predictions too
            if (!empty($predictions)) {
                $validatedPredictions = self::validateEmailsWithAPI($predictions, $name);

                Log::info("Fallback email prediction successful", [
                    'name' => $name,
                    'domain' => $cleanDomain,
                    'predictions_count' => count($predictions),
                    'validated_count' => count($validatedPredictions)
                ]);

                return $validatedPredictions;
            }

            Log::info("Fallback email prediction successful", [
                'name' => $name,
                'domain' => $cleanDomain,
                'predictions_count' => count($predictions)
            ]);

            return $predictions;
        } catch (\Exception $e) {
            Log::error("Fallback email prediction failed", [
                'name' => $name,
                'domain' => $domain,
                'error' => $e->getMessage()
            ]);

            return [];
        }
    }

    private static function callPythonEmailPredictionRaw(string $name, string $domain): array
    {
        try {
            if (!$name || !$domain || trim($name) === '' || trim($domain) === '') {
                return [];
            }

            // Path to the Python email prediction script (matching crawl-executives.js logic)
            $scriptsPath = base_path('crawl-executives-scripts');
            $scriptPath = $scriptsPath . '/generate_email_predictions.py';

            if (!file_exists($scriptPath)) {
                throw new \Exception("Python script not found at: {$scriptPath}");
            }

            // Execute Python script with virtual environment but NO SMTP validation
            // (exactly matching the crawl-executives.js logic)
            $venvPath = $scriptsPath . '/venv';
            $pythonPath = $venvPath . '/bin/python3';

            // Clean the name and domain for command (prevent injection)
            $cleanName = str_replace('"', '\\"', $name);
            $cleanDomain = str_replace('"', '\\"', $domain);

            // Skip SMTP validation in Python script (matching JS logic)
            // Redirect stderr to /dev/null to avoid log messages mixing with JSON
            $command = 'cd "' . $scriptsPath . '" && "' . $pythonPath . '" generate_email_predictions.py "' . $cleanName . '" "' . $cleanDomain . '" --no-validation 2>/dev/null';

            Log::info("📧 Generating email patterns for \"{$name}\" at {$domain} using Python (raw, no validation)", [
                'command' => $command,
                'name' => $name,
                'domain' => $domain
            ]);

            $output = shell_exec($command);

            if ($output === null) {
                throw new \Exception("Failed to execute Python script");
            }

            // Parse JSON response (output should be clean now with stderr redirected)
            $jsonResponse = json_decode(trim($output), true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::error("Failed to parse Python script JSON", [
                    'output' => $output,
                    'json_error' => json_last_error_msg(),
                    'command' => $command
                ]);
                throw new \Exception("Invalid JSON output from Python script");
            }

            // Check if script was successful (matching JS logic)
            if (!isset($jsonResponse['success']) || !$jsonResponse['success']) {
                $error = $jsonResponse['error'] ?? 'Unknown error';
                Log::warning("Python email prediction script failed for \"{$name}\": {$error}");
                return [];
            }

            // Extract all email predictions (no SMTP filtering needed) - matching JS logic
            $predictions = $jsonResponse['predictions'] ?? [];
            $allEmails = [];
            foreach ($predictions as $p) {
                $email = is_array($p) ? ($p['email'] ?? '') : $p;
                if ($email && strlen($email) > 0) {
                    $allEmails[] = $email;
                }
            }

            Log::info("📧 Python generated " . count($allEmails) . " email patterns for \"{$name}\" (raw)");

            return $allEmails;
        } catch (\Exception $e) {
            Log::error("Python email generation failed for \"{$name}\": " . $e->getMessage(), [
                'name' => $name,
                'domain' => $domain,
                'error' => $e->getMessage()
            ]);

            // Fallback to PHP-based prediction if Python fails
            return self::fallbackEmailPredictionRaw($name, $domain);
        }
    }

    private static function fallbackEmailPredictionRaw(string $name, string $domain): array
    {
        try {
            // Use the existing ExecutiveEmailService as fallback
            $emailService = new \App\Services\ExecutiveEmailService();

            // Clean domain (remove protocol and www)
            $cleanDomain = str_replace(['https://', 'http://', 'www.'], '', $domain);
            $cleanDomain = strtok($cleanDomain, '/'); // Remove path

            $predictions = $emailService->generateEmailPredictions($name, $cleanDomain);

            Log::info("Fallback email prediction successful (raw)", [
                'name' => $name,
                'domain' => $cleanDomain,
                'predictions_count' => count($predictions)
            ]);

            return $predictions;
        } catch (\Exception $e) {
            Log::error("Fallback email prediction failed", [
                'name' => $name,
                'domain' => $domain,
                'error' => $e->getMessage()
            ]);

            return [];
        }
    }

    private static function predictEmailsForCompanyRecord($record)
    {
        $executives = $record->executives ?? [];
        $website = $record->website;

        if (empty($executives) || empty($website)) {
            \Filament\Notifications\Notification::make()
                ->title('Cannot Generate Predictions')
                ->body('No executives found or website is missing.')
                ->danger()
                ->send();
            return;
        }

        // Extract domain from website
        $domain = parse_url($website, PHP_URL_HOST);
        if (!$domain) {
            \Filament\Notifications\Notification::make()
                ->title('Invalid Website')
                ->body('Could not extract domain from website URL.')
                ->danger()
                ->send();
            return;
        }

        $processedCount = 0;
        $skippedCount = 0;
        $errorCount = 0;
        $noValidationCount = 0;
        $updatedExecutives = [];

        foreach ($executives as $index => $executive) {
            $executiveName = trim($executive['name'] ?? '');

            if (empty($executiveName)) {
                Log::debug("Skipping executive without name", ['index' => $index]);
                $skippedCount++;
                $updatedExecutives[] = $executive;
                continue;
            }

            try {
                // Call Python email prediction script (but get raw predictions first)
                $rawPredictions = self::callPythonEmailPredictionRaw($executiveName, $domain);

                if (!empty($rawPredictions)) {
                    // Now validate the predictions
                    $validatedPredictions = self::validateEmailsWithAPI($rawPredictions, $executiveName);

                    if (!empty($validatedPredictions)) {
                        $executive['email_predictions'] = $validatedPredictions;
                        $processedCount++;

                        Log::info("Generated and validated email predictions for executive", [
                            'name' => $executiveName,
                            'domain' => $domain,
                            'raw_count' => count($rawPredictions),
                            'validated_count' => count($validatedPredictions)
                        ]);
                    } else {
                        // Predictions were generated but none passed validation
                        Log::warning("Predictions generated but no emails passed validation for executive", [
                            'name' => $executiveName,
                            'raw_count' => count($rawPredictions)
                        ]);
                        $noValidationCount++;
                        // Keep empty predictions array
                        $executive['email_predictions'] = [];
                    }
                } else {
                    Log::warning("No predictions generated for executive", ['name' => $executiveName]);
                    $errorCount++;
                }

                $updatedExecutives[] = $executive;
            } catch (\Exception $e) {
                Log::error("Failed to generate predictions for executive", [
                    'name' => $executiveName,
                    'error' => $e->getMessage()
                ]);
                $errorCount++;
                $updatedExecutives[] = $executive;
            }
        }

        // Update the company record
        $record->update(['executives' => $updatedExecutives]);

        // Show notification with results
        $message = "Email prediction completed! Processed: {$processedCount}, Skipped: {$skippedCount}";
        if ($errorCount > 0) {
            $message .= ", Errors: {$errorCount}";
        }
        if ($noValidationCount > 0) {
            $message .= ", No valid emails: {$noValidationCount}";
        }

        $notificationType = 'success';
        $title = 'Email Predictions Generated';

        // Show warning if all predictions failed validation
        if ($processedCount === 0 && $noValidationCount > 0) {
            $notificationType = 'warning';
            $title = 'Email Predictions Generated - No Valid Emails';
            $message = "Predictions generated but no emails passed validation. Generated for {$noValidationCount} executive(s) but all emails were invalid.";
        }

        \Filament\Notifications\Notification::make()
            ->title($title)
            ->body($message)
            ->{$notificationType}()
            ->duration(5000)
            ->send();
    }
}
