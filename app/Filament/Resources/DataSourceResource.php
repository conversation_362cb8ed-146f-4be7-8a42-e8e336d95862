<?php

namespace App\Filament\Resources;

use App\Filament\Resources\DataSourceResource\Pages;
use App\Filament\Resources\DataSourceResource\RelationManagers;
use App\Jobs\CrawlCompanyListJob;
use App\Models\DataSource;
use App\Models\Category;
use App\Services\WebCrawlerService;
use App\Services\EnhancedWebCrawlerService;
use Illuminate\Bus\Batch;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Actions\ActionGroup;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\BooleanColumn;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class DataSourceResource extends Resource
{
    protected static ?string $model = DataSource::class;

    protected static ?string $navigationIcon = 'heroicon-o-globe-alt';
    
    protected static ?string $navigationLabel = 'Data Sources';
    
    protected static ?string $pluralModelLabel = 'Data Sources';
    
    protected static ?int $navigationSort = 20;

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Basic Information')
                    ->schema([
                        TextInput::make('name')
                            ->required()
                            ->maxLength(255),
                        
                        TextInput::make('source_url')
                            ->label('Source URL')
                            ->required()
                            ->url()
                            ->maxLength(255),
                        
                        Select::make('categories')
                            ->label('Categories')
                            ->multiple()
                            ->relationship('categories', 'name')
                            ->options(Category::active()->pluck('name', 'id'))
                            ->searchable()
                            ->preload()
                            ->createOptionForm([
                                TextInput::make('name')
                                    ->required()
                                    ->maxLength(255),
                                Textarea::make('description')
                                    ->rows(3),
                                Forms\Components\ColorPicker::make('color')
                                    ->default('#3B82F6'),
                                Toggle::make('is_active')
                                    ->default(true),
                            ])
                            ->columnSpanFull(),
                        
                        Textarea::make('notes')
                            ->rows(3)
                            ->columnSpanFull(),
                    ])->columns(2),
                
                Section::make('Crawling Configuration')
                    ->schema([
                        Toggle::make('has_pagination')
                            ->label('Has Pagination')
                            ->default(false)
                            ->reactive()
                            ->afterStateUpdated(fn ($set, $state) => !$state ? $set('pagination_format', null) : null),
                        
                        Toggle::make('has_company_links')
                            ->label('Has Company Links')
                            ->default(false),
                        
                        Toggle::make('is_active')
                            ->label('Active')
                            ->default(true),
                        
                        Forms\Components\Placeholder::make('is_crawling_status')
                            ->label('Crawling Status')
                            ->content(fn ($record) => $record ? ($record->is_crawling ? '🔄 Currently Crawling...' : '✓ Not Crawling') : '✓ Not Crawling')
                            ->visible(fn ($livewire) => $livewire instanceof \Filament\Resources\Pages\EditRecord),
                    ])->columns(3),

                Section::make('Pagination Configuration')
                    ->schema([
                        Forms\Components\Select::make('pagination_format')
                            ->label('Pagination Format')
                            ->options(\App\Models\DataSource::getPaginationFormats())
                            ->default('query_param')
                            ->helperText('Choose how pagination URLs are structured')
                            ->visible(fn ($get) => $get('has_pagination'))
                            ->reactive(),
                        
                        TextInput::make('pagination_base_url')
                            ->label('Pagination Base URL')
                            ->placeholder('Leave empty to use Source URL')
                            ->helperText('Optional: Different base URL for pagination if needed')
                            ->visible(fn ($get) => $get('has_pagination'))
                            ->reactive()
                            ->columnSpanFull(),
                        
                        Forms\Components\Placeholder::make('pagination_preview')
                            ->label('URL Preview (Page 2)')
                            ->content(function ($get) {
                                $sourceUrl = $get('source_url');
                                $paginationFormat = $get('pagination_format') ?? 'query_param';
                                $baseUrl = $get('pagination_base_url') ?: $sourceUrl;
                                
                                if (!$baseUrl) {
                                    return 'Enter Source URL to see preview';
                                }
                                
                                // Create a temporary DataSource object for preview
                                $tempSource = new \App\Models\DataSource([
                                    'source_url' => $sourceUrl,
                                    'pagination_format' => $paginationFormat,
                                    'pagination_base_url' => $get('pagination_base_url'),
                                ]);
                                
                                return $tempSource->buildPaginationUrl(2);
                            })
                            ->visible(fn ($get) => $get('has_pagination'))
                            ->columnSpanFull(),
                        
                        TextInput::make('max_pages')
                            ->label('Max Pages to Crawl')
                            ->numeric()
                            ->default(5)
                            ->placeholder('Leave empty for unlimited')
                            ->visible(fn ($get) => $get('has_pagination')),
                    ])->columns(2)
                    ->visible(fn ($get) => $get('has_pagination')),
                
                Section::make('CSS Selectors')
                    ->schema([
                        TextInput::make('company_name_selector')
                            ->label('Company Name Selector')
                            ->required()
                            ->placeholder('e.g., .company-name, h2.title')
                            ->columnSpanFull(),
                        
                        TextInput::make('company_link_selector')
                            ->label('Company Link Selector')
                            ->placeholder('e.g., .company-link a, .title-link')
                            ->columnSpanFull(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->poll('5s') // Refresh table every 5 seconds to show crawling status
            ->columns([
                TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
                
                TextColumn::make('source_url')
                    ->label('Source URL')
                    ->limit(50)
                    ->tooltip(function (TextColumn $column): ?string {
                        $state = $column->getState();
                        return strlen($state) > 50 ? $state : null;
                    }),
                
                TextColumn::make('categories.name')
                    ->label('Categories')
                    ->badge()
                    ->getStateUsing(function ($record) {
                        return $record->categories->pluck('name')->toArray();
                    })
                    ->color('info')
                    ->tooltip(function ($record) {
                        if ($record->categories->isEmpty()) {
                            return 'No categories assigned';
                        }
                        return 'Categories: ' . $record->categories->pluck('name')->join(', ');
                    }),
                
                BadgeColumn::make('status')
                    ->getStateUsing(function ($record) {
                        $status = $record->status;
                        if ($status === 'crawling') {
                            return '🔄 Crawling';
                        }
                        return ucfirst(str_replace('_', ' ', $status));
                    })
                    ->colors([
                        'success' => 'active',
                        'warning' => 'stale',
                        'danger' => 'inactive',
                        'secondary' => 'never_crawled',
                        'info' => 'crawling',
                    ]),
                
                BooleanColumn::make('has_pagination')
                    ->label('Pagination')
                    ->tooltip(function ($record) {
                        if (!$record->has_pagination) {
                            return 'No pagination';
                        }
                        $format = $record->pagination_format ?? 'query_param';
                        $formatLabel = \App\Models\DataSource::getPaginationFormats()[$format] ?? $format;
                        return "Format: {$formatLabel}";
                    })
                    ->hidden(),
                
                BooleanColumn::make('has_company_links')
                    ->label('Company Links')
                    ->hidden(),
                
                TextColumn::make('companies_count')
                    ->label('Companies')
                    ->badge()
                    ->counts('companies')
                    ->color(fn ($state) => $state > 0 ? 'success' : 'gray')
                    ->tooltip(function ($record) {
                        $count = $record->companies()->count();
                        if ($count === 0) {
                            return 'No companies crawled yet';
                        }
                        $latest = $record->companies()->latest()->first();
                        return $count . ' companies found. Latest: ' . ($latest ? $latest->name : 'N/A');
                    }),
                
                TextColumn::make('executive_progress')
                    ->label('Executive Progress')
                    ->getStateUsing(function ($record) {
                        $total = $record->companies()->count();
                        if ($total === 0) return 'N/A';
                        
                        // Count all companies that have been processed (completed + failed)
                        $completed = $record->companies()->where('status', 'completed')->count();
                        $failed = $record->companies()->where('status', 'failed')->count();
                        $processing = $record->companies()->where('status', 'processing')->count();
                        
                        $processed = $completed + $failed; // Total processed (successful + failed)
                        
                        return "{$processed}/{$total}";
                    })
                    ->badge()
                    ->color(function ($record) {
                        $total = $record->companies()->count();
                        if ($total === 0) return 'gray';
                        
                        $completed = $record->companies()->where('status', 'completed')->count();
                        $failed = $record->companies()->where('status', 'failed')->count();
                        $processing = $record->companies()->where('status', 'processing')->count();
                        
                        // Color based on status
                        if ($processing > 0) return 'warning'; // Something is running
                        if ($completed === $total) return 'success'; // All completed successfully
                        if ($failed === $total) return 'danger'; // All failed
                        if ($completed + $failed === $total) return 'info'; // Mix of completed and failed
                        return 'gray'; // Not started or partial
                    })
                    ->tooltip(function ($record) {
                        $total = $record->companies()->count();
                        if ($total === 0) return 'No companies to process';
                        
                        $completed = $record->companies()->where('status', 'completed')->count();
                        $processing = $record->companies()->where('status', 'processing')->count();
                        $failed = $record->companies()->where('status', 'failed')->count();
                        
                        return "Total: {$total}; Successful: {$completed}; Running: {$processing}; Failed: {$failed}";
                    }),
                
                TextColumn::make('last_crawled_at')
                    ->label('Last Crawled')
                    ->dateTime()
                    ->sortable()
                    ->placeholder('Never'),
                
                BooleanColumn::make('is_active')
                    ->label('Active'),
            ])
            ->filters([
                SelectFilter::make('is_active')
                    ->label('Status')
                    ->options([
                        1 => 'Active',
                        0 => 'Inactive',
                    ]),
                    
                SelectFilter::make('has_pagination')
                    ->label('Has Pagination')
                    ->options([
                        1 => 'Yes',
                        0 => 'No',
                    ]),
                    
                SelectFilter::make('categories')
                    ->label('Category')
                    ->relationship('categories', 'name')
                    ->multiple()
                    ->preload(),
                    
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                // Main action (always visible)
                Tables\Actions\EditAction::make(),
                
                // Crawling actions dropdown
                ActionGroup::make([
                    Tables\Actions\Action::make('crawl_now')
                        ->label('Crawl list companies')
                        ->icon('heroicon-o-arrow-down-tray')
                        ->color('success')
                        ->tooltip('Start crawl campaign for this data source')
                        ->form(function ($record) {
                            if ($record->has_pagination) {
                                return [
                                    Forms\Components\TextInput::make('campaign_name')
                                        ->label('Campaign Name')
                                        ->required()
                                        ->default(fn () => 'Campaign ' . $record->name . ' - ' . now()->format('Y-m-d H:i'))
                                        ->maxLength(255),
                                        
                                    Forms\Components\Grid::make(2)
                                        ->schema([
                                            Forms\Components\TextInput::make('from_page')
                                                ->label('From Page')
                                                ->required()
                                                ->numeric()
                                                ->default(1)
                                                ->rules(['min:1'])
                                                ->helperText('Starting page number for crawling'),
                                                
                                            Forms\Components\TextInput::make('to_page')
                                                ->label('To Page')
                                                ->required()
                                                ->numeric()
                                                ->default(fn ($get) => $record->max_pages ?: 10)
                                                ->rules([
                                                    'min:1',
                                                    fn ($get): \Closure => function (string $attribute, $value, \Closure $fail) use ($get) {
                                                        if ($value < $get('from_page')) {
                                                            $fail('To page must be greater than or equal to from page.');
                                                        }
                                                    },
                                                ])
                                                ->helperText('Ending page number for crawling'),
                                        ]),
                                        
                                    Forms\Components\Placeholder::make('pagination_info')
                                        ->label('Pagination Info')
                                        ->content(function ($record, $get) {
                                            $fromPage = $get('from_page') ?: 1;
                                            $toPage = $get('to_page') ?: ($record->max_pages ?: 10);
                                            $totalPages = max(0, $toPage - $fromPage + 1);
                                            
                                            $format = $record->pagination_format ?? 'query_param';
                                            $formatLabel = \App\Models\DataSource::getPaginationFormats()[$format] ?? $format;
                                            
                                            return "Will crawl {$totalPages} pages using {$formatLabel} format";
                                        })
                                        ->columnSpanFull(),
                                ];
                            } else {
                                return [
                                    Forms\Components\TextInput::make('campaign_name')
                                        ->label('Campaign Name')
                                        ->required()
                                        ->default(fn () => 'Campaign ' . $record->name . ' - ' . now()->format('Y-m-d H:i'))
                                        ->maxLength(255),
                                        
                                    Forms\Components\Placeholder::make('crawl_info')
                                        ->label('Crawl Info')
                                        ->content('This will crawl the single page data source.')
                                        ->columnSpanFull(),
                                ];
                            }
                        })
                        ->modalHeading(fn ($record) => 'Start Crawl Campaign: ' . $record->name)
                        ->modalDescription(fn ($record) => $record->has_pagination 
                            ? 'Configure the page range for this pagination-enabled data source.' 
                            : 'Start crawling this single-page data source.')
                        ->modalSubmitActionLabel('Start Campaign')
                        ->action(function ($record, array $data) {
                            static::startCrawlCampaign($record, $data);
                        }),

                    Tables\Actions\Action::make('crawl_executives')
                        ->label('Crawl Executives')
                        ->icon('heroicon-o-users')
                        ->color('warning')
                        ->tooltip('Crawl executives for all companies in this data source')
                        ->modalHeading(fn ($record) => 'Crawl Executives: ' . $record->name)
                        ->modalDescription(fn ($record) => "This will crawl executives for all {$record->companies()->count()} companies from this data source as background jobs.")
                        ->modalSubmitActionLabel('Start Executive Crawling')
                        ->visible(fn ($record) => $record->companies()->count() > 0)
                        ->action(function ($record) {
                            static::startExecutiveCrawl($record);
                        }),
                        
                    Tables\Actions\Action::make('view_batch_progress')
                        ->label('Batch Progress')
                        ->icon('heroicon-o-chart-bar-square')
                        ->color('info')
                        ->tooltip('View executive crawl batch progress')
                        ->visible(function ($record) {
                            $batchId = $record->crawl_metadata['last_executive_batch_id'] ?? null;
                            return $batchId && Bus::findBatch($batchId);
                        })
                        ->modalHeading(fn ($record) => 'Batch Progress: ' . $record->name)
                        ->modalContent(function ($record) {
                            $batchId = $record->crawl_metadata['last_executive_batch_id'] ?? null;
                            $batch = $batchId ? Bus::findBatch($batchId) : null;
                            
                            if (!$batch) {
                                return view('filament.components.batch-not-found');
                            }
                            
                            return view('filament.components.batch-progress', [
                                'batch' => $batch,
                                'dataSource' => $record,
                            ]);
                        })
                        ->modalSubmitAction(false)
                        ->modalCancelActionLabel('Close'),
                        
                    Tables\Actions\Action::make('cancel_batch')
                        ->label('Cancel Batch')
                        ->icon('heroicon-o-x-circle')
                        ->color('danger')
                        ->tooltip('Cancel running executive crawl batch')
                        ->visible(function ($record) {
                            $batchId = $record->crawl_metadata['last_executive_batch_id'] ?? null;
                            $batch = $batchId ? Bus::findBatch($batchId) : null;
                            return $batch && !$batch->finished() && !$batch->cancelled();
                        })
                        ->requiresConfirmation()
                        ->modalHeading('Cancel Executive Crawl Batch')
                        ->modalDescription('Are you sure you want to cancel the running executive crawl batch? This cannot be undone.')
                        ->modalSubmitActionLabel('Yes, Cancel Batch')
                        ->action(function ($record) {
                            $batchId = $record->crawl_metadata['last_executive_batch_id'] ?? null;
                            $batch = $batchId ? Bus::findBatch($batchId) : null;
                            
                            if ($batch && !$batch->finished() && !$batch->cancelled()) {
                                $batch->cancel();
                                
                                Notification::make()
                                    ->warning()
                                    ->title('Batch Cancelled')
                                    ->body("Executive crawl batch for '{$record->name}' has been cancelled.")
                                    ->duration(8000)
                                    ->send();
                            } else {
                                Notification::make()
                                    ->danger()
                                    ->title('Cannot Cancel Batch')
                                    ->body('Batch is not running or has already finished.')
                                    ->duration(5000)
                                    ->send();
                            }
                        }),
                ])
                ->label('Crawling')
                ->icon('heroicon-m-arrow-down-tray')
                ->size('sm')
                ->color('success')
                ->button(),
                
                // Information actions dropdown
                ActionGroup::make([
                    Tables\Actions\Action::make('view_companies')
                        ->label('View Companies')
                        ->icon('heroicon-o-eye')
                        ->color('info')
                        ->url(fn ($record) => route('filament.admin.resources.companies.index', ['tableFilters[data_source_id][value]' => $record->id]))
                        ->visible(fn ($record) => $record->companies()->count() > 0),
                        
                    Tables\Actions\Action::make('test_connection')
                        ->label('Test Connection')
                        ->icon('heroicon-o-link')
                        ->color('info')
                        ->action(function ($record) {
                            $crawlerService = app(WebCrawlerService::class);
                            $result = $crawlerService->testConnection($record);
                            
                            if ($result['success']) {
                                Notification::make()
                                    ->title('Connection Successful')
                                    ->body("Status: {$result['status_code']} - Connection to {$record->name} is working")
                                    ->success()
                                    ->send();
                            } else {
                                Notification::make()
                                    ->title('Connection Failed')
                                    ->body("Error: {$result['error']} (Status: {$result['status_code']})")
                                    ->danger()
                                    ->send();
                            }
                        }),
                ])
                ->label('Info')
                ->icon('heroicon-m-information-circle')
                ->size('sm')
                ->color('info')
                ->button(),
                
                // Dangerous actions dropdown
                ActionGroup::make([
                    Tables\Actions\DeleteAction::make(),
                    Tables\Actions\ForceDeleteAction::make(),
                    Tables\Actions\RestoreAction::make(),
                ])
                ->label('Danger')
                ->icon('heroicon-m-exclamation-triangle')
                ->size('sm')
                ->color('danger')
                ->button(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                    Tables\Actions\BulkAction::make('activate')
                        ->label('Activate')
                        ->icon('heroicon-o-check')
                        ->color('success')
                        ->action(function ($records) {
                            $records->each->update(['is_active' => true]);
                        }),
                    Tables\Actions\BulkAction::make('deactivate')
                        ->label('Deactivate')
                        ->icon('heroicon-o-x-mark')
                        ->color('danger')
                        ->action(function ($records) {
                            $records->each->update(['is_active' => false]);
                        }),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\CategoriesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDataSources::route('/'),
            'create' => Pages\CreateDataSource::route('/create'),
            'edit' => Pages\EditDataSource::route('/{record}/edit'),
        ];
    }
    
    /**
     * Start background crawl job without limits
     */
    protected static function startBackgroundCrawl($record): void
    {
        try {
            // Use unlimited crawling - no page or company limits
            $maxPages = $record->max_pages ?? 0; // 0 means no limit
            $maxCompanies = 0; // 0 means no limit
            
            // Dispatch the background job
            \App\Jobs\CrawlCompanyListJob::dispatch(
                $record, 
                $maxPages, 
                $maxCompanies, 
                auth()->id()
            );
            
            Notification::make()
                ->info()
                ->title('Full Crawling Started!')
                ->body("Background crawling job has been queued for '{$record->name}' without limits. You'll receive a notification when it's complete.")
                ->actions([
                    \Filament\Notifications\Actions\Action::make('view_queue')
                        ->button()
                        ->url('/horizon')
                        ->openUrlInNewTab()
                        ->label('View Queue')
                        ->visible(config('queue.default') === 'redis'),
                ])
                ->duration(10000)
                ->send();
                
        } catch (\Exception $e) {
            Notification::make()
                ->danger()
                ->title('Failed to Start Crawling')
                ->body('Error: ' . $e->getMessage())
                ->duration(8000)
                ->send();
        }
    }
    
    /**
     * Start crawl campaign with pagination range support
     */
    protected static function startCrawlCampaign($record, array $data): void
    {
        try {
            // Create campaign
            $campaign = \App\Models\Campaign::create([
                'name' => $data['campaign_name'],
                'data_source_id' => $record->id,
                'type' => $record->has_pagination ? 'pagination_range' : 'full',
                'from_page' => $record->has_pagination ? $data['from_page'] : null,
                'to_page' => $record->has_pagination ? $data['to_page'] : null,
                'total_pages' => $record->has_pagination ? max(0, $data['to_page'] - $data['from_page'] + 1) : 1,
                'status' => 'pending',
                'crawl_metadata' => [
                    'pagination_format' => $record->pagination_format,
                    'max_pages_setting' => $record->max_pages,
                    'created_by' => auth()->id(),
                    'created_at' => now()->toISOString(),
                ],
            ]);
            
            // Dispatch campaign crawl job  
            \App\Jobs\CrawlCampaignJob::dispatch($campaign, auth()->id());
            
            $totalPages = $campaign->total_pages;
            $pageText = $totalPages > 1 ? "pages {$data['from_page']}-{$data['to_page']}" : "single page";
            
            Notification::make()
                ->success()
                ->title('Campaign Started!')
                ->body("Campaign '{$data['campaign_name']}' has been started for {$pageText}. You'll receive notifications as it progresses.")
                ->actions([
                    \Filament\Notifications\Actions\Action::make('view_queue')
                        ->button()
                        ->url('/horizon')
                        ->openUrlInNewTab()
                        ->label('View Queue')
                        ->visible(config('queue.default') === 'redis'),
                ])
                ->duration(10000)
                ->send();
                
        } catch (\Exception $e) {
            Notification::make()
                ->danger()
                ->title('Failed to Start Campaign')
                ->body('Error: ' . $e->getMessage())
                ->duration(8000)
                ->send();
        }
    }
    
    /**
     * Run synchronous crawl without limits
     */
    protected static function runSynchronousCrawl($record): void
    {
        try {
            // Use unlimited crawling - no page or company limits
            $maxPages = $record->max_pages ?? 0; // 0 means no limit
            $maxCompanies = 0; // 0 means no limit
            
            // Show immediate progress notification
            Notification::make()
                ->info()
                ->title('Full Crawling Started')
                ->body('Running synchronous crawl without limits. This may take a while...')
                ->duration(5000)
                ->send();
            
            // Dispatch job synchronously
            CrawlCompanyListJob::dispatchSync(
                $record,
                $maxPages ?: 999,
                $maxCompanies ?: 99999,
                auth()->id()
            );
            
            // Refresh the record to get updated company count
            $record->refresh();
            $companiesCount = $record->companies()->count();
            
            // Update the last_crawled_at timestamp
            $record->update(['last_crawled_at' => now()]);
            
            Notification::make()
                ->success()
                ->title('Full Crawling Completed Successfully!')
                ->body("Found and imported {$companiesCount} companies from '{$record->name}' without limits.")
                ->actions([
                    \Filament\Notifications\Actions\Action::make('view_companies')
                        ->button()
                        ->url(route('filament.admin.resources.companies.index', ['tableFilters[data_source_id][value]' => $record->id]))
                        ->visible($companiesCount > 0)
                        ->label('View Companies'),
                ])
                ->duration(10000)
                ->send();
                
        } catch (\Exception $e) {
            Notification::make()
                ->danger()
                ->title('Crawling Error')
                ->body('Error: ' . $e->getMessage())
                ->duration(8000)
                ->send();
        }
    }
    
    /**
     * Start executive crawl for all companies in data source
     */
    protected static function startExecutiveCrawl($record): void
    {
        try {
            $companies = $record->companies()->get();
            $totalCompanies = $companies->count();
            
            if ($totalCompanies === 0) {
                Notification::make()
                    ->warning()
                    ->title('No Companies Found')
                    ->body("No companies found in '{$record->name}' to crawl executives for.")
                    ->duration(5000)
                    ->send();
                return;
            }
            
            // Create batch of executive crawl jobs
            $jobs = [];
            foreach ($companies as $company) {
                $jobs[] = new \App\Jobs\CrawlCompanyExecutivesJob($company);
            }
            
            $batch = Bus::batch($jobs)
                ->name("Executive Crawl: {$record->name}")
                ->onQueue('executives')
                ->allowFailures()
                ->then(function (Batch $batch) use ($record) {
                    // Called when all jobs complete successfully
                    Log::info("Executive crawl batch completed for data source: {$record->name}");
                })
                ->catch(function (Batch $batch, \Throwable $e) use ($record) {
                    // Called when first job failure is encountered
                    Log::error("Executive crawl batch failed for data source: {$record->name}. Error: " . $e->getMessage());
                })
                ->finally(function (Batch $batch) use ($record) {
                    // Called when batch finishes (regardless of success/failure)
                    Log::info("Executive crawl batch finished for data source: {$record->name}. " .
                             "Total: {$batch->totalJobs}, " .
                             "Processed: " . ($batch->totalJobs - $batch->pendingJobs) . ", " .
                             "Failed: {$batch->failedJobs}");
                    
                    // Send completion notification
                    $completed = $batch->totalJobs - $batch->pendingJobs - $batch->failedJobs;
                    $message = "Executive crawl batch finished for '{$record->name}': " .
                              "{$completed}/{$batch->totalJobs} completed successfully";
                    
                    if ($batch->failedJobs > 0) {
                        $message .= ", {$batch->failedJobs} failed";
                    }
                    
                    Notification::make()
                        ->title('Executive Crawl Batch Finished')
                        ->body($message)
                        ->success($batch->failedJobs === 0)
                        ->warning($batch->failedJobs > 0 && $completed > 0)
                        ->danger($batch->failedJobs > 0 && $completed === 0)
                        ->duration(10000)
                        ->send();
                })
                ->dispatch();
                
            // Store batch ID in data source metadata for tracking
            $record->update([
                'crawl_metadata' => array_merge($record->crawl_metadata ?? [], [
                    'last_executive_batch_id' => $batch->id,
                    'last_executive_batch_started' => now()->toISOString(),
                ])
            ]);
            
            Notification::make()
                ->info()
                ->title('Executive Crawl Batch Started!')
                ->body("Created batch with {$totalCompanies} executive crawl jobs for '{$record->name}'. You can monitor progress in the table.")
                ->actions([
                    \Filament\Notifications\Actions\Action::make('view_companies')
                        ->button()
                        ->url(route('filament.admin.resources.companies.index', ['tableFilters[data_source_id][value]' => $record->id]))
                        ->label('View Companies'),
                    \Filament\Notifications\Actions\Action::make('view_queue')
                        ->button()
                        ->url('/horizon')
                        ->openUrlInNewTab()
                        ->label('View Queue')
                        ->visible(config('queue.default') === 'redis'),
                ])
                ->duration(12000)
                ->send();
                
        } catch (\Exception $e) {
            Notification::make()
                ->danger()
                ->title('Failed to Start Executive Crawling')
                ->body('Error: ' . $e->getMessage())
                ->duration(8000)
                ->send();
        }
    }
}
