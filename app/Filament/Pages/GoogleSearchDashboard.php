<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use App\Models\Company;
use App\Models\CrawlerLog;
use App\Jobs\BatchGoogleSearchJob;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Filament\Notifications\Notification;

class GoogleSearchDashboard extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-magnifying-glass';
    protected static ?string $navigationLabel = 'Google Search Monitor';
    protected static ?string $title = 'Google Search Dashboard';
    protected static string $view = 'filament.pages.google-search-dashboard';
    protected static ?int $navigationSort = 80;

    public static function getNavigationGroup(): ?string
    {
        return __('settings.navigation_group'); // Use same group as Settings
    }

    public function getViewData(): array
    {
        return [
            'stats' => $this->getGoogleSearchStats(),
            'recentSearches' => $this->getRecentSearches(),
            'companiesNeedingSearch' => $this->getCompaniesNeedingSearch(),
            'successfulSearches' => $this->getSuccessfulSearches(),
        ];
    }

    private function getGoogleSearchStats(): array
    {
        $totalCompanies = Company::count();
        $companiesWithWebsite = Company::whereNotNull('website')
            ->where('website', '!=', '')
            ->where('website', 'not like', '%example%')
            ->where('website', '!=', '#')
            ->count();
        
        $companiesNeedingSearch = $totalCompanies - $companiesWithWebsite;
        
        $googleSearchFoundCount = Company::whereJsonContains('crawl_metadata->google_search_found', true)->count();
        
        return [
            'total_companies' => $totalCompanies,
            'companies_with_website' => $companiesWithWebsite,
            'companies_needing_search' => $companiesNeedingSearch,
            'google_search_found' => $googleSearchFoundCount,
            'completion_rate' => $totalCompanies > 0 ? round(($companiesWithWebsite / $totalCompanies) * 100, 1) : 0,
            'google_success_rate' => $companiesNeedingSearch > 0 ? round(($googleSearchFoundCount / $companiesNeedingSearch) * 100, 1) : 0,
        ];
    }

    private function getRecentSearches(): array
    {
        return Company::whereJsonContains('crawl_metadata->google_search_found', true)
            ->orderBy('updated_at', 'desc')
            ->limit(10)
            ->get(['name', 'website', 'updated_at', 'crawl_metadata'])
            ->map(function ($company) {
                return [
                    'name' => $company->name,
                    'website' => $company->website,
                    'found_at' => $company->updated_at->format('Y-m-d H:i:s'),
                    'previous_website' => $company->crawl_metadata['previous_website'] ?? 'null',
                ];
            })
            ->toArray();
    }

    private function getCompaniesNeedingSearch(): array
    {
        return Company::where(function ($query) {
                $query->whereNull('website')
                    ->orWhere('website', '')
                    ->orWhere('website', 'like', '%example%')
                    ->orWhere('website', '#')
                    ->orWhere('website', 'javascript:')
                    ->orWhere('website', 'mailto:');
            })
            ->limit(20)
            ->get(['id', 'name', 'website', 'source_url'])
            ->toArray();
    }

    private function getSuccessfulSearches(): array
    {
        $dailyStats = Company::whereJsonContains('crawl_metadata->google_search_found', true)
            ->selectRaw('DATE(updated_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date', 'desc')
            ->limit(7)
            ->get()
            ->toArray();

        return $dailyStats;
    }

    public function triggerBatchSearch(): void
    {
        // Dispatch the batch Google search job
        BatchGoogleSearchJob::dispatch(50, auth()->id());
        
        Notification::make()
            ->success()
            ->title('Batch Google Search Started')
            ->body('Google search job has been queued and will process up to 50 companies. You will receive notifications on progress.')
            ->send();
    }

    public function clearSearchCache(): void
    {
        // Clear cache for companies that have had Google searches
        $companies = Company::whereJsonContains('crawl_metadata->google_search_found', true)
            ->pluck('name');
        
        $cleared = 0;
        
        foreach ($companies as $companyName) {
            $cacheKey = 'google_search_' . md5($companyName);
            if (Cache::has($cacheKey)) {
                Cache::forget($cacheKey);
                $cleared++;
            }
        }

        Notification::make()
            ->success()
            ->title('Cache Cleared')
            ->body("Cleared Google search cache for {$cleared} companies!")
            ->send();
    }
}
