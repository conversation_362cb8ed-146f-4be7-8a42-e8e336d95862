<?php

namespace App\Filament\Pages;

use Filament\Pages\Dashboard as BaseDashboard;

class Dashboard extends BaseDashboard
{
    public static function getNavigationLabel(): string
    {
        return __('Dashboard');
    }
    
    public function getTitle(): string 
    {
        return __('Dashboard');
    }

    protected static ?string $navigationIcon = 'heroicon-o-home';

    public static function canAccess(): bool
    {
        return true; // Allow all authenticated users to access dashboard
    }
}
