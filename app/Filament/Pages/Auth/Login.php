<?php

namespace App\Filament\Pages\Auth;

use App\Models\User;
use <PERSON><PERSON><PERSON>rin\LivewireRateLimiting\Exceptions\TooManyRequestsException;
use <PERSON><PERSON><PERSON>rin\LivewireRateLimiting\WithRateLimiting;
use Filament\Actions\Action;
use Filament\Events\Auth\Registered;
use Filament\Facades\Filament;
use Filament\Forms\Components\Component;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Http\Responses\Auth\Contracts\LoginResponse;
use Filament\Notifications\Notification;
use Filament\Pages\Auth\Login as BaseLogin;
use Illuminate\Support\Facades\Route;
use Illuminate\Validation\ValidationException;

class Login extends BaseLogin
{
    public function getViewData(): array
    {
        return parent::getViewData();
    }

    public function getView(): string
    {
        return 'vendor.filament.pages.auth.login';
    }
}
