<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        \App\Console\Commands\SyncDepartmentsCommand::class,
        \App\Console\Commands\CleanupChromeCommand::class,
        \App\Console\Commands\GoogleSearchCompaniesCommand::class,
        \App\Console\Commands\BatchGoogleSearchCommand::class,
    ];
    
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // Schedule the department sync to run daily at 2:00 AM
        $schedule->command('app:sync-departments')
                ->dailyAt('02:00')
                ->appendOutputTo(storage_path('logs/department-sync.log'));
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
