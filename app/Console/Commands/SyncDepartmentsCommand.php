<?php

namespace App\Console\Commands;

use App\Services\HrmService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class SyncDepartmentsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-departments';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Synchronize departments from HRM system to local database';

    /**
     * Execute the console command.
     */
    public function handle(HrmService $hrmService)
    {
        $this->info('Starting department synchronization...');
        
        try {
            $result = $hrmService->syncDepartments();
            
            $this->info('Synchronization completed successfully!');
            $this->table(
                ['Total', 'Created', 'Updated', 'Deactivated', 'Skipped'],
                [[
                    $result['total'],
                    $result['created'],
                    $result['updated'],
                    $result['deactivated'],
                    $result['skipped'],
                ]]
            );
            
            Log::info('Department sync completed', $result);
            
            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('Synchronization failed: ' . $e->getMessage());
            Log::error('Department sync failed', ['error' => $e->getMessage()]);
            
            return Command::FAILURE;
        }
    }
}
