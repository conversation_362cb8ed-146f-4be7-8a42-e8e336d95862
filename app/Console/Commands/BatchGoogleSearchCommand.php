<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Jobs\BatchGoogleSearchJob;

class BatchGoogleSearchCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'google:batch-search 
                            {--size=50 : Number of companies to search in batch}
                            {--sync : Run synchronously instead of queuing}';

    /**
     * The console command description.
     */
    protected $description = 'Start a batch Google search job for companies without websites using JavaScript-based search';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $batchSize = (int) $this->option('size');
        $sync = $this->option('sync');

        $this->info("🚀 Starting batch Google search for {$batchSize} companies using JavaScript...");

        if ($sync) {
            $this->info("Running synchronously...");
            $job = new BatchGoogleSearchJob($batchSize);
            $job->handle();
        } else {
            $this->info("Queuing job...");
            BatchGoogleSearchJob::dispatch($batchSize);
            $this->info("✅ Job queued successfully! Check the queue worker to see progress.");
        }

        return self::SUCCESS;
    }
}
