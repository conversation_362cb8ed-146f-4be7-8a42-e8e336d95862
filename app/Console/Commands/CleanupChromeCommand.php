<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\EnhancedWebCrawlerService;

class CleanupChromeCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'chrome:cleanup {--force : Force cleanup even if jobs are running}';

    /**
     * The console command description.
     */
    protected $description = 'Cleanup Chrome browser processes and temp files';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $force = $this->option('force');
        
        if (!$force) {
            if (!$this->confirm('Are you sure you want to cleanup all Chrome processes? This may interrupt running crawl jobs.')) {
                $this->info('Chrome cleanup cancelled.');
                return 0;
            }
        }
        
        $this->info('Starting Chrome cleanup...');
        
        try {
            EnhancedWebCrawlerService::forceCleanupAllChrome();
            $this->info('✅ Chrome cleanup completed successfully.');
            
            // Show process count after cleanup
            $chromeProcesses = shell_exec('pgrep -f "chrome\|chromium" | wc -l') ?? '0';
            $this->info("Chrome processes remaining: " . trim($chromeProcesses));
            
        } catch (\Exception $e) {
            $this->error('❌ Chrome cleanup failed: ' . $e->getMessage());
            return 1;
        }
        
        return 0;
    }
}
