<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Company;
use Illuminate\Support\Facades\Log;
use Symfony\Component\Process\Process;

class GoogleSearchCompaniesCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'google:search-companies 
                            {--limit=10 : Number of companies to search} 
                            {--force : Force search even if cache exists}
                            {--clear-cache : Clear all Google search cache}
                            {--stats : Show cache statistics only}';

    /**
     * The console command description.
     */
    protected $description = 'Search Google for company websites that are missing or invalid using JavaScript-based search';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        if ($this->option('clear-cache')) {
            return $this->clearCache();
        }

        if ($this->option('stats')) {
            return $this->showStats();
        }

        return $this->searchMissingWebsites();
    }

    /**
     * Search for missing company websites using JavaScript Google search
     */
    private function searchMissingWebsites(): int
    {
        $limit = (int) $this->option('limit');
        $force = $this->option('force');

        $this->info("🔍 Searching for companies without valid websites using JavaScript...");

        // Find companies without valid websites
        $companies = Company::query()
            ->where(function ($query) {
                $query->whereNull('website')
                    ->orWhere('website', '')
                    ->orWhere('website', 'like', '%example%')
                    ->orWhere('website', '#')
                    ->orWhere('website', 'javascript:')
                    ->orWhere('website', 'mailto:');
            })
            ->limit($limit)
            ->get();

        if ($companies->isEmpty()) {
            $this->info("✅ No companies found without valid websites!");
            return self::SUCCESS;
        }

        $this->info("Found {$companies->count()} companies without valid websites");

        $progressBar = $this->output->createProgressBar($companies->count());
        $progressBar->start();

        $updated = 0;
        $cached = 0;
        $failed = 0;

        foreach ($companies as $company) {
            $this->line("");
            $this->info("Searching for: {$company->name}");

            if (!$force) {
                // Check cache first
                $cacheKey = 'google_search_' . md5($company->name);
                if (cache()->has($cacheKey)) {
                    $cachedResult = cache()->get($cacheKey);
                    if ($cachedResult) {
                        $company->update(['website' => $cachedResult]);
                        $this->info("📦 Used cached result: {$cachedResult}");
                        $cached++;
                        $updated++;
                        $progressBar->advance();
                        continue;
                    }
                }
            }

            $foundWebsite = $this->executeJavaScriptGoogleSearch($company->name);

            if ($foundWebsite) {
                // Cache the result
                $cacheKey = 'google_search_' . md5($company->name);
                cache()->put($cacheKey, $foundWebsite, now()->addHours(24));
                
                $company->update([
                    'website' => $foundWebsite,
                    'crawl_metadata' => array_merge($company->crawl_metadata ?? [], [
                        'google_search_found' => true,
                        'google_search_method' => 'javascript_command',
                        'google_search_at' => now()->toISOString(),
                        'previous_website' => $company->website,
                    ])
                ]);

                $this->info("✅ Found: {$foundWebsite}");
                $updated++;
            } else {
                $this->warn("❌ No website found for: {$company->name}");
                $failed++;
            }

            $progressBar->advance();

            // Rate limiting
            sleep(2);
        }

        $progressBar->finish();
        $this->line("");

        // Summary
        $this->info("📊 Google Search Summary:");
        $this->table(
            ['Metric', 'Count'],
            [
                ['Companies processed', $companies->count()],
                ['Websites found', $updated],
                ['From cache', $cached],
                ['Failed searches', $failed],
                ['Success rate', round(($updated / $companies->count()) * 100, 1) . '%'],
            ]
        );

        return self::SUCCESS;
    }

    /**
     * Execute JavaScript Google search script
     */
    private function executeJavaScriptGoogleSearch(string $companyName): ?string
    {
        $scriptPath = base_path('scripts/google-search.js');
        $outputPath = storage_path('app/google-search-' . md5($companyName) . '.json');

        $command = [
            'node',
            $scriptPath,
            '--company-name', $companyName,
            '--output', $outputPath
        ];

        try {
            Log::debug("🔍 Executing Google search script from command", [
                'company' => $companyName,
                'script' => $scriptPath
            ]);

            $process = new Process($command);
            $process->setTimeout(60); // 1 minute timeout
            $process->setWorkingDirectory(base_path());
            $process->run();

            if (!$process->isSuccessful()) {
                Log::warning("❌ Google search script failed from command", [
                    'company' => $companyName,
                    'exit_code' => $process->getExitCode(),
                    'error' => $process->getErrorOutput()
                ]);
                return null;
            }

            // Parse JSON output from stdout
            $output = trim($process->getOutput());
            if ($output) {
                $result = json_decode($output, true);
                
                if ($result && isset($result['success']) && $result['success'] && isset($result['website'])) {
                    Log::info("✅ Google search script found website from command", [
                        'company' => $companyName,
                        'website' => $result['website']
                    ]);
                    
                    return $result['website'];
                }
            }

            Log::debug("ℹ️ Google search script completed but no website found from command", [
                'company' => $companyName,
                'output' => $output
            ]);

            return null;

        } catch (\Exception $e) {
            Log::error("❌ Error executing Google search script from command", [
                'company' => $companyName,
                'error' => $e->getMessage()
            ]);
            return null;
        } finally {
            // Clean up output file
            if (file_exists($outputPath)) {
                unlink($outputPath);
            }
        }
    }

    /**
     * Show cache statistics
     */
    private function showStats(): int
    {
        $this->info("📊 Google Search Cache Statistics:");

        // Get companies without websites
        $companiesWithoutWebsites = Company::query()
            ->where(function ($query) {
                $query->whereNull('website')
                    ->orWhere('website', '')
                    ->orWhere('website', 'like', '%example%')
                    ->orWhere('website', '#');
            })
            ->pluck('name')
            ->toArray();

        if (empty($companiesWithoutWebsites)) {
            $this->info("No companies found without websites.");
            return self::SUCCESS;
        }

        $cached = 0;
        $notCached = 0;
        
        foreach ($companiesWithoutWebsites as $companyName) {
            $cacheKey = 'google_search_' . md5($companyName);
            if (cache()->has($cacheKey)) {
                $cached++;
            } else {
                $notCached++;
            }
        }

        $total = count($companiesWithoutWebsites);
        $cacheHitRate = $total > 0 ? round(($cached / $total) * 100, 1) : 0;

        $this->table(
            ['Metric', 'Value'],
            [
                ['Total companies without websites', $total],
                ['Cached search results', $cached],
                ['Not cached', $notCached],
                ['Cache hit rate', $cacheHitRate . '%'],
            ]
        );

        return self::SUCCESS;
    }

    /**
     * Clear Google search cache
     */
    private function clearCache(): int
    {
        $this->warn("🗑️ Clearing Google search cache...");

        // Get companies without websites to clear their cache
        $companiesWithoutWebsites = Company::query()
            ->where(function ($query) {
                $query->whereNull('website')
                    ->orWhere('website', '')
                    ->orWhere('website', 'like', '%example%')
                    ->orWhere('website', '#');
            })
            ->pluck('name')
            ->toArray();

        $clearedCount = 0;
        foreach ($companiesWithoutWebsites as $companyName) {
            $cacheKey = 'google_search_' . md5($companyName);
            if (cache()->has($cacheKey)) {
                cache()->forget($cacheKey);
                $clearedCount++;
            }
        }

        $this->info("✅ Cleared {$clearedCount} cached Google search results.");

        return self::SUCCESS;
    }
}
