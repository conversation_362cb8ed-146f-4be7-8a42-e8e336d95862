<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class StartQueueWorkerCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'queue:start {--timeout=300 : Worker timeout in seconds} {--memory=512 : Memory limit in MB} {--sleep=3 : Sleep time between jobs}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Start queue worker for email prediction jobs';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $timeout = $this->option('timeout');
        $memory = $this->option('memory');
        $sleep = $this->option('sleep');

        $this->info('Starting queue worker for email prediction jobs...');
        $this->info("Configuration: timeout={$timeout}s, memory={$memory}MB, sleep={$sleep}s");

        // Start the queue worker
        $exitCode = $this->call('queue:work', [
            '--timeout' => $timeout,
            '--memory' => $memory,
            '--sleep' => $sleep,
            '--tries' => 2,
            '--verbose' => true,
        ]);

        return $exitCode;
    }
}
