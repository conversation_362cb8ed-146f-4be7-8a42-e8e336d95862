<?php

namespace App\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Symfony\Component\DomCrawler\Crawler;
use App\Models\DataSource;
use App\Models\Company;
use App\Models\Keyword;
use Illuminate\Support\Facades\Log;
use Symfony\Component\Panther\Client as PantherClient;

class WebCrawlerService
{
    private Client $httpClient;
    private ?PantherClient $pantherClient = null;
    protected int $requestDelay = 1; // seconds between requests
    private int $timeout = 30; // request timeout
    private array $userAgents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    ];

    public function __construct()
    {
        $this->httpClient = new Client([
            'timeout' => $this->timeout,
            'headers' => [
                'User-Agent' => $this->userAgents[array_rand($this->userAgents)],
                'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language' => 'vi-VN,vi;q=0.9,en;q=0.8',
                'Accept-Encoding' => 'gzip, deflate, br',
                'Connection' => 'keep-alive',
                'Upgrade-Insecure-Requests' => '1',
            ],
        ]);
    }

    /**
     * Get page content using Panther browser automation for JavaScript-rendered pages
     */
    private function getPageContentWithWebDriver(string $url): ?string
    {
        try {
            Log::debug("🌐 Loading page with Panther browser automation", [
                'url' => $url,
                'timeout' => 30,
            ]);

            // Initialize Panther client if not already done
            if (!$this->pantherClient) {
                // Set ChromeDriver path from BDI
                $chromedriverPath = base_path('drivers/chromedriver');
                if (!file_exists($chromedriverPath)) {
                    // Fallback to project root chromedriver
                    $chromedriverPath = base_path('chromedriver');
                }
                
                if (file_exists($chromedriverPath)) {
                    putenv("PANTHER_CHROME_DRIVER_BINARY={$chromedriverPath}");
                }
                
                $this->pantherClient = PantherClient::createChromeClient(null, [
                    '--headless',
                    '--no-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-gpu',
                    '--window-size=1920,1080',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--disable-extensions',
                    '--disable-plugins',
                    '--disable-images', // Speed up loading
                    '--disable-javascript-harmony-shipping',
                    '--disable-background-timer-throttling',
                    '--disable-renderer-backgrounding',
                    '--disable-backgrounding-occluded-windows',
                    '--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                ]);
            }

            // Navigate to URL
            $crawler = $this->pantherClient->request('GET', $url);
            
            // Wait for basic page structure
            $this->pantherClient->waitFor('body');
            
            // Wait for common JavaScript content indicators
            $this->waitForJavaScriptContent($url);

            // Get HTML source
            $html = $this->pantherClient->getPageSource();
            
            Log::debug("✅ Successfully loaded page with Panther", [
                'url' => $url,
                'html_length' => strlen($html),
                'title' => $this->pantherClient->getTitle(),
            ]);

            return $html;

        } catch (\Exception $e) {
            Log::error("❌ Panther browser automation failed", [
                'url' => $url,
                'error' => $e->getMessage(),
                'type' => get_class($e),
            ]);

            // Cleanup failed client
            if ($this->pantherClient) {
                try {
                    $this->pantherClient->quit();
                    $this->pantherClient = null;
                } catch (\Exception $quitError) {
                    // Ignore quit errors
                }
            }

            return null;
        }
    }

    /**
     * Get page content with fallback strategy
     */
    private function getPageContent(string $url): ?string
    {
        // Use smart detection for optimal performance
        $content = $this->getPageContentSmart($url);
        
        if ($content) {
            return $content;
        }

        // Final fallback to WebDriver if smart method failed
        Log::warning("⚠️ Smart detection failed, trying WebDriver as last resort", [
            'url' => $url,
        ]);

        return $this->getPageContentWithWebDriver($url);
    }

    /**
     * Test connection to a data source
     */
    public function testConnection(DataSource $dataSource): array
    {
        try {
            $response = $this->httpClient->get($dataSource->source_url);
            $statusCode = $response->getStatusCode();
            $responseTime = 0; // You can measure this if needed
            
            return [
                'success' => $statusCode >= 200 && $statusCode < 300,
                'status_code' => $statusCode,
                'response_time' => $responseTime,
                'error' => null,
            ];
        } catch (RequestException $e) {
            return [
                'success' => false,
                'status_code' => $e->getCode(),
                'response_time' => 0,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Crawl company list from a data source
     */
    public function crawlCompanyList(DataSource $dataSource, int $maxPages = 5): array
    {
        $companies = [];
        $errors = [];
        $page = 1; // Initialize page counter outside try block

        try {
            for ($page = 1; $page <= $maxPages; $page++) {
                $url = $this->buildPaginatedUrl($dataSource->source_url, $page, $dataSource);
                
                Log::info("Crawling company list page {$page}: {$url}");
                
                // Use enhanced page content method with WebDriver fallback
                $html = $this->getPageContent($url);
                if (!$html) {
                    $errors[] = "Failed to fetch page {$page}: No content received";
                    continue;
                }
                $crawler = new Crawler($html);

                // Extract companies using configured selectors
                $pageCompanies = $this->extractCompaniesFromPage($crawler, $dataSource);
                
                if (empty($pageCompanies)) {
                    Log::info("No companies found on page {$page}, continuing pagination to respect max_pages setting");
                } else {
                    $companies = array_merge($companies, $pageCompanies);
                    Log::info("Found " . count($pageCompanies) . " companies on page {$page}");
                }
                
                // Respect rate limiting
                if ($page < $maxPages) {
                    sleep($this->requestDelay);
                }
            }
        } catch (RequestException $e) {
            $errors[] = "Request failed: " . $e->getMessage();
            Log::error("Failed to crawl company list", ['error' => $e->getMessage(), 'url' => $url ?? $dataSource->source_url]);
        } catch (\Exception $e) {
            $errors[] = "Parsing failed: " . $e->getMessage();
            Log::error("Failed to parse company list", ['error' => $e->getMessage()]);
        }

        return [
            'companies' => $companies,
            'errors' => $errors,
            'pages_crawled' => min($page - 1, $maxPages),
            'total_found' => count($companies),
        ];
    }

    /**
     * Crawl detailed information for a company
     */
    public function crawlCompanyDetails(Company $company): array
    {
        $details = [];
        $errors = [];

        try {
            // First, try to find the company info page
            $infoPageUrl = $this->findCompanyInfoPage($company);
            
            if (!$infoPageUrl) {
                throw new \Exception("Could not find company info page");
            }

            Log::info("Crawling company details: {$infoPageUrl}");
            
            // Use enhanced page content method with WebDriver fallback
            $html = $this->getPageContent($infoPageUrl);
            if (!$html) {
                throw new \Exception("Failed to fetch page content");
            }
            $crawler = new Crawler($html);

            // Extract company details
            $details = $this->extractCompanyDetails($crawler, $company);
            $details['info_page_url'] = $infoPageUrl;
            
            // Extract executives
            $executives = $this->extractExecutives($crawler, $company);
            $details['executives'] = $executives;

        } catch (RequestException $e) {
            $errors[] = "Request failed: " . $e->getMessage();
            Log::error("Failed to crawl company details", ['company' => $company->name, 'error' => $e->getMessage()]);
        } catch (\Exception $e) {
            $errors[] = "Processing failed: " . $e->getMessage();
            Log::error("Failed to process company details", ['company' => $company->name, 'error' => $e->getMessage()]);
        }

        return [
            'details' => $details,
            'errors' => $errors,
            'success' => empty($errors),
        ];
    }

    /**
     * Build paginated URL based on DataSource pagination format
     */
    protected function buildPaginatedUrl(string $baseUrl, int $page, DataSource $dataSource): string
    {
        if ($page === 1) {
            return $baseUrl;
        }

        // Use DataSource's pagination format if available
        if ($dataSource->has_pagination && $dataSource->pagination_format) {
            return $dataSource->buildPaginationUrl($page);
        }

        // Fallback to query parameter
        $separator = strpos($baseUrl, '?') !== false ? '&' : '?';
        return $baseUrl . $separator . 'page=' . $page;
    }

    /**
     * Extract companies from a page using configured selectors
     */
    protected function extractCompaniesFromPage(Crawler $crawler, DataSource $dataSource): array
    {
        $companies = [];
        $selectors = $dataSource->selectors;

        if (!$selectors || !isset($selectors['company_name'], $selectors['company_link'])) {
            return $companies;
        }

        try {
            $nameSelector = $selectors['company_name'];
            $linkSelector = $selectors['company_link'];

            // Find all companies on the page
            $crawler->filter($nameSelector)->each(function (Crawler $nameNode, $i) use (&$companies, $linkSelector, $crawler, $dataSource) {
                try {
                    $name = trim($nameNode->text());
                    
                    // Try to find corresponding link
                    $link = null;
                    
                    // First try to find link within the same parent element  
                    $parentNode = $nameNode->closest('div, li, tr, article') ?? $nameNode;
                    $linkNode = $parentNode->filter($linkSelector)->first();
                    
                    if ($linkNode->count() > 0) {
                        $link = $linkNode->attr('href');
                    } else {
                        // Fallback: find link by index
                        $allLinks = $crawler->filter($linkSelector);
                        if ($allLinks->count() > $i) {
                            $link = $allLinks->eq($i)->attr('href');
                        }
                    }

                    if ($name && $link) {
                        // Convert relative URLs to absolute
                        if (!filter_var($link, FILTER_VALIDATE_URL)) {
                            $link = $this->resolveUrl($dataSource->source_url, $link);
                        }
                        
                        $companies[] = [
                            'name' => $name,
                            'source_url' => $link,
                            'website' => $this->guessWebsiteFromLink($link, $name),
                        ];
                    }
                } catch (\Exception $e) {
                    Log::warning("Failed to extract company at index {$i}", ['error' => $e->getMessage()]);
                }
            });
        } catch (\Exception $e) {
            Log::error("Failed to extract companies from page", ['error' => $e->getMessage()]);
        }

        return $companies;
    }

    /**
     * Find company info page using keywords
     */
    protected function findCompanyInfoPage(Company $company): ?string
    {
        Log::info("🔍 Starting info page search for company: {$company->name}", [
            'company_id' => $company->id,
            'website' => $company->website,
            'source_url' => $company->source_url,
        ]);

        if (!$company->website) {
            Log::info("📝 No website found, using source URL as fallback", [
                'company' => $company->name,
                'fallback_url' => $company->source_url,
            ]);
            return $company->source_url;
        }

        $infoKeywords = Keyword::getCompanyInfoKeywords();
        Log::info("🎯 Retrieved keywords for info page search", [
            'company' => $company->name,
            'keywords_count' => count($infoKeywords),
            'keywords' => $infoKeywords,
        ]);
        
        // Try common info page patterns (exclude homepage and source URL)
        $patterns = [];
        foreach ($infoKeywords as $keyword) {
            $patterns[] = rtrim($company->website, '/') . '/' . $keyword;
        }

        Log::info("🌐 Generated candidate URLs for info page search", [
            'company' => $company->name,
            'total_candidates' => count($patterns),
            'candidates' => $patterns,
        ]);

        $successfulUrl = null;
        $attemptResults = [];

        foreach ($patterns as $index => $url) {
            $attemptStart = microtime(true);
            
            try {
                Log::debug("🔗 Testing candidate URL #{$index}", [
                    'company' => $company->name,
                    'url' => $url,
                    'attempt' => $index + 1,
                    'total' => count($patterns),
                ]);

                $response = $this->httpClient->head($url);
                $statusCode = $response->getStatusCode();
                $responseTime = round((microtime(true) - $attemptStart) * 1000, 2);
                
                $attemptResults[] = [
                    'url' => $url,
                    'status_code' => $statusCode,
                    'response_time_ms' => $responseTime,
                    'success' => $statusCode === 200,
                    'error' => null,
                ];

                if ($statusCode === 200) {
                    $successfulUrl = $url;
                    Log::info("✅ Found valid info page", [
                        'company' => $company->name,
                        'url' => $url,
                        'status_code' => $statusCode,
                        'response_time_ms' => $responseTime,
                        'attempt' => $index + 1,
                    ]);
                    break;
                } else {
                    Log::debug("❌ Invalid status code for candidate URL", [
                        'company' => $company->name,
                        'url' => $url,
                        'status_code' => $statusCode,
                        'response_time_ms' => $responseTime,
                    ]);
                }
            } catch (RequestException $e) {
                $responseTime = round((microtime(true) - $attemptStart) * 1000, 2);
                
                $attemptResults[] = [
                    'url' => $url,
                    'status_code' => null,
                    'response_time_ms' => $responseTime,
                    'success' => false,
                    'error' => $e->getMessage(),
                ];

                Log::debug("🚫 Request failed for candidate URL", [
                    'company' => $company->name,
                    'url' => $url,
                    'error' => $e->getMessage(),
                    'response_time_ms' => $responseTime,
                ]);
                
                // Continue to next URL
                continue;
            }
            
            // Rate limiting
            usleep(500000); // 0.5 second delay
        }

        // Log summary of all attempts
        $successCount = count(array_filter($attemptResults, fn($result) => $result['success']));
        $errorCount = count(array_filter($attemptResults, fn($result) => !is_null($result['error'])));
        $totalResponseTime = array_sum(array_column($attemptResults, 'response_time_ms'));

        Log::info("📊 Info page search completed", [
            'company' => $company->name,
            'total_candidates' => count($patterns),
            'successful_requests' => $successCount,
            'failed_requests' => count($attemptResults) - $successCount,
            'error_requests' => $errorCount,
            'total_response_time_ms' => round($totalResponseTime, 2),
            'average_response_time_ms' => count($attemptResults) > 0 ? round($totalResponseTime / count($attemptResults), 2) : 0,
            'found_url' => $successfulUrl,
            'all_attempts' => $attemptResults,
        ]);

        if ($successfulUrl) {
            return $successfulUrl;
        }

        // Fallback to website or source URL
        $fallbackUrl = $company->website ?: $company->source_url;
        Log::warning("⚠️ No valid info page found, using fallback", [
            'company' => $company->name,
            'fallback_url' => $fallbackUrl,
            'attempts_made' => count($attemptResults),
        ]);

        return $fallbackUrl;
    }

    /**
     * Extract company details from crawler
     */
    protected function extractCompanyDetails(Crawler $crawler, Company $company): array
    {
        $details = [];

        // Common selectors for company information
        $selectors = [
            'description' => ['meta[name="description"]', '.company-description', '.about-company', '#about', '.description'],
            'industry' => ['.industry', '.sector', '.business-type', '[data-industry]'],
            'address' => ['.address', '.location', '.company-address', 'address'],
            'phone' => ['[href^="tel:"]', '.phone', '.telephone', '.contact-phone'],
            'email' => ['[href^="mailto:"]', '.email', '.contact-email'],
            'employee_count' => ['.employees', '.staff-size', '.company-size', '[data-employees]'],
            'founded_year' => ['.founded', '.established', '.since', '[data-founded]'],
        ];

        foreach ($selectors as $field => $selectorList) {
            $value = $this->extractFieldValue($crawler, $selectorList);
            if ($value) {
                $details[$field] = $this->cleanText($value);
            }
        }

        // Process specific fields
        if (isset($details['phone'])) {
            $details['phone'] = $this->extractPhoneNumber($details['phone']);
        }
        
        if (isset($details['email'])) {
            $details['email'] = $this->extractEmail($details['email']);
        }

        if (isset($details['founded_year'])) {
            $details['founded_year'] = $this->extractYear($details['founded_year']);
        }

        return $details;
    }

    /**
     * Extract executives from crawler using HTML to text and regex approach
     */
    protected function extractExecutives(Crawler $crawler, Company $company): array
    {
        $executives = [];
        $executiveKeywords = Keyword::getExecutiveKeywords();

        Log::info("🔍 Starting executive extraction for company: {$company->name}", [
            'company_id' => $company->id,
            'executive_keywords_count' => count($executiveKeywords),
            'executive_keywords' => $executiveKeywords,
        ]);

        // Convert HTML to plain text while preserving line breaks
        $fullText = $this->convertHtmlToTextWithLineBreaks($crawler);
        Log::debug("📄 Converted HTML to text with preserved line breaks", [
            'company' => $company->name,
            'text_length' => strlen($fullText),
            'text_preview' => $fullText,
        ]);

        // Extract executives from full text using keywords and regex
        $textExecutives = $this->extractExecutivesFromFullText($fullText, $executiveKeywords, $company);
        $executives = array_merge($executives, $textExecutives);

        // Also try targeted sections as fallback
        $targetSections = $this->extractTargetedSections($crawler, $executiveKeywords, $company);
        $executives = array_merge($executives, $targetSections);

        // Remove duplicates based on name similarity
        $uniqueExecutives = $this->removeDuplicateExecutives($executives, $company);
        $finalExecutives = array_slice($uniqueExecutives, 0, 15); // Increase limit since we're more comprehensive
        
        Log::info("✅ Executive extraction completed", [
            'company' => $company->name,
            'total_found' => count($executives),
            'unique_found' => count($uniqueExecutives),
            'final_count' => count($finalExecutives),
            'final_executives' => array_map(fn($exec) => [
                'name' => $exec['name'],
                'position' => $exec['position'],
                'level' => $exec['level']
            ], $finalExecutives),
        ]);

        return $finalExecutives;
    }

    /**
     * Extract executives from full text using regex and keywords
     */
    private function extractExecutivesFromFullText(string $text, array $executiveKeywords, Company $company): array
    {
        $executives = [];
        
        // Use original text without aggressive normalization to preserve encoding
        $workingText = $text;
        
        Log::debug("🔍 Searching for executives in full text", [
            'company' => $company->name,
            'original_text_length' => strlen($text),
            'keywords_to_search' => count($executiveKeywords),
            'text_preview' => mb_substr($text, 0, 200) . '...',
        ]);

        // Split text into sentences and paragraphs for better context
        $sentences = $this->splitTextIntoSentences($workingText);
        
        foreach ($sentences as $index => $sentence) {
            if (strlen(trim($sentence)) < 10) continue;
            
            // Check if sentence contains any executive keyword
            $matchedKeywords = $this->findMatchingKeywords($sentence, $executiveKeywords);
            
            if (!empty($matchedKeywords)) {
                Log::debug("🎯 Found sentence with executive keywords", [
                    'company' => $company->name,
                    'sentence_index' => $index,
                    'matched_keywords' => $matchedKeywords,
                    'sentence' => $sentence,
                ]);
                
                // Extract executive info from this sentence and surrounding context
                $extractedExecs = $this->extractExecutivesFromSentence($sentence, $matchedKeywords, $sentences, $index, $company);
                
                Log::debug("📝 Extracted executives from sentence", [
                    'company' => $company->name,
                    'sentence_index' => $index,
                    'extracted_count' => count($extractedExecs),
                    'executives' => $extractedExecs,
                ]);
                
                $executives = array_merge($executives, $extractedExecs);
            }
        }
        
        return $executives;
    }

    /**
     * Extract executives from targeted sections as fallback
     */
    private function extractTargetedSections(Crawler $crawler, array $executiveKeywords, Company $company): array
    {
        $executives = [];
        
        // Try common section selectors as fallback
        $selectors = [
            '.management', '.executives', '.leadership', '.team', 
            '.board-members', '.staff', '.about-team', '.our-team',
            '#management', '#executives', '#leadership', '#team',
            '[class*="manage"]', '[class*="leader"]', '[class*="executive"]'
        ];

        foreach ($selectors as $selector) {
            try {
                $sections = $crawler->filter($selector);
                if ($sections->count() > 0) {
                    $sections->each(function (Crawler $section) use (&$executives, $executiveKeywords, $company, $selector) {
                        $sectionText = $section->text();
                        if (strlen(trim($sectionText)) > 20) {
                            Log::debug("📝 Extracting from targeted section", [
                                'company' => $company->name,
                                'selector' => $selector,
                                'text_length' => strlen($sectionText),
                            ]);
                            
                            $sectionExecs = $this->extractExecutivesFromFullText($sectionText, $executiveKeywords, $company);
                            $executives = array_merge($executives, $sectionExecs);
                        }
                    });
                }
            } catch (\Exception $e) {
                continue;
            }
        }
        
        return $executives;
    }

    /**
     * Convert HTML to text while preserving line breaks and paragraph structure
     */
    private function convertHtmlToTextWithLineBreaks(Crawler $crawler): string
    {
        // Get the raw HTML content
        $html = $crawler->html();
        
        // Replace common block-level elements with line breaks
        $html = preg_replace('/<\/(div|p|h[1-6]|li|tr|br)>/i', "\n", $html);
        $html = preg_replace('/<br\s*\/?>/i', "\n", $html);
        
        // Replace list items and table cells with line breaks
        $html = preg_replace('/<(li|td|th)[^>]*>/i', "\n", $html);
        
        // Add extra line breaks for major sections
        $html = preg_replace('/<\/(section|article|header|footer|main)>/i', "\n\n", $html);
        
        // Remove all other HTML tags
        $text = strip_tags($html);
        
        // Clean up the text while preserving meaningful line breaks
        $text = html_entity_decode($text, ENT_QUOTES | ENT_HTML5, 'UTF-8');
        
        // Normalize multiple line breaks but keep paragraph separation
        $text = preg_replace('/\n{3,}/', "\n\n", $text);
        $text = preg_replace('/[ \t]+/', ' ', $text); // Clean up spaces but keep line breaks
        
        // Clean up lines - remove empty lines with just spaces
        $lines = explode("\n", $text);
        $cleanLines = [];
        foreach ($lines as $line) {
            $trimmedLine = trim($line);
            if ($trimmedLine !== '') {
                $cleanLines[] = $trimmedLine;
            }
        }
        
        return implode("\n", $cleanLines);
    }

    /**
     * Normalize text for better processing
     */
    private function normalizeText(string $text): string
    {
        // Clean up common HTML artifacts
        $text = str_replace(['&nbsp;', '&amp;', '&lt;', '&gt;'], [' ', '&', '<', '>'], $text);
        
        // Remove excessive punctuation but preserve line breaks
        $text = preg_replace('/[^\p{L}\p{N}\s\-\.\,\(\)\/\:\n\r]/u', ' ', $text);
        
        // Clean up spaces within lines but preserve line breaks
        $text = preg_replace('/[ \t]+/u', ' ', $text);
        
        return trim($text);
    }

    /**
     * Remove duplicate executives based on name similarity
     */
    protected function removeDuplicateExecutives(array $executives, Company $company): array
    {
        $unique = [];
        $seenNames = [];
        
        foreach ($executives as $executive) {
            $name = $executive['name'] ?? '';
            $normalizedName = mb_strtolower(trim($name));
            
            // Skip if name is too short or already seen
            if (strlen($normalizedName) < 2) {
                continue;
            }
            
            $isDuplicate = false;
            foreach ($seenNames as $seenName) {
                // Check similarity (simple contains check for now)
                if (strpos($normalizedName, $seenName) !== false || strpos($seenName, $normalizedName) !== false) {
                    $isDuplicate = true;
                    break;
                }
            }
            
            if (!$isDuplicate) {
                $unique[] = $executive;
                $seenNames[] = $normalizedName;
            }
        }
        
        return $unique;
    }

    /**
     * Split text into meaningful sentences while preserving encoding
     */
    private function splitTextIntoSentences(string $text): array
    {
        // First split by line breaks (most important for structured data like executive lists)
        $lines = explode("\n", $text);
        
        $sentences = [];
        foreach ($lines as $line) {
            $line = trim($line);
            if (mb_strlen($line) > 5) {
                // For Japanese text, also split by common sentence delimiters
                // Use mb_* functions to handle multibyte characters properly
                $lineSentences = preg_split('/[。．！？\.\!\?]/u', $line, -1, PREG_SPLIT_NO_EMPTY);
                foreach ($lineSentences as $sentence) {
                    $sentence = trim($sentence);
                    if (mb_strlen($sentence) > 5) {
                        $sentences[] = $sentence;
                    }
                }
            }
        }
        
        return $sentences;
    }

    /**
     * Find matching keywords in sentence
     */
    private function findMatchingKeywords(string $sentence, array $keywords): array
    {
        $matched = [];
        $lowerSentence = mb_strtolower($sentence);
        
        foreach ($keywords as $keyword) {
            $lowerKeyword = mb_strtolower($keyword);
            if (strpos($lowerSentence, $lowerKeyword) !== false) {
                $matched[] = $keyword;
            }
        }
        
        return $matched;
    }

    /**
     * Extract executives from a single sentence
     */
    private function extractExecutivesFromSentence(string $sentence, array $matchedKeywords, array $allSentences, int $currentIndex, Company $company): array
    {
        $executives = [];
        
        Log::debug("🔍 Starting executive extraction from sentence", [
            'company' => $company->name,
            'sentence' => $sentence,
            'sentence_hex' => bin2hex($sentence),
            'sentence_length' => strlen($sentence),
            'matched_keywords' => $matchedKeywords,
        ]);
        
        // Use original sentence first, only apply encoding fix if needed
        $workingSentence = $sentence;
        
        // Process each keyword to extract executive names
        foreach ($matchedKeywords as $keyword) {
            Log::debug("🔍 Processing keyword", [
                'company' => $company->name,
                'keyword' => $keyword,
                'keyword_hex' => bin2hex($keyword),
                'sentence' => $workingSentence,
            ]);
            
            // Try extraction with original sentence first
            $extractedNames = $this->extractNamesFromSentenceWithKeyword($workingSentence, $keyword, $company);
            
            // If no names found and there are encoding issues, try with fixed encoding
            if (empty($extractedNames) && strpos($sentence, '�') !== false) {
                Log::debug("🔧 No names found, trying with encoding fix", [
                    'company' => $company->name,
                    'keyword' => $keyword,
                ]);
                
                $fixedSentence = $this->fixEncodingIssues($sentence);
                $extractedNames = $this->extractNamesFromSentenceWithKeyword($fixedSentence, $keyword, $company);
            }
            
            foreach ($extractedNames as $name) {
                if (!empty($name) && $this->looksLikeName($name) && strlen($name) >= 2) {
                    $executive = [
                        'name' => $name,
                        'position' => $keyword,
                        'level' => $this->determineExecutiveLevel($keyword),
                        'source_sentence' => $sentence,
                        'extraction_method' => 'keyword_based_extraction',
                    ];
                    
                    $executives[] = $executive;
                    
                    Log::debug("✅ Valid executive found and added", [
                        'company' => $company->name,
                        'executive' => $executive,
                    ]);
                } else {
                    Log::debug("❌ Executive rejected", [
                        'company' => $company->name,
                        'name' => $name,
                        'reason' => empty($name) ? 'empty_name' : (!$this->looksLikeName($name) ? 'name_invalid' : 'name_too_short'),
                    ]);
                }
            }
        }
        
        Log::debug("🏁 Sentence extraction completed", [
            'company' => $company->name,
            'sentence' => $sentence,
            'total_executives_found' => count($executives),
        ]);
        
        return $executives;
    }

    /**
     * Fix encoding issues in text
     */
    private function fixEncodingIssues(string $text): string
    {
        // Remove replacement characters (encoding errors)
        $text = str_replace('�', '', $text);
        
        // Try to detect and fix encoding
        $encoding = mb_detect_encoding($text, ['UTF-8', 'ISO-8859-1', 'Windows-1252'], true);
        if ($encoding && $encoding !== 'UTF-8') {
            $text = mb_convert_encoding($text, 'UTF-8', $encoding);
        }
        
        // Normalize unicode
        if (class_exists('Normalizer')) {
            $text = \Normalizer::normalize($text, \Normalizer::FORM_C);
        }
        
        return $text;
    }

    /**
     * Extract names from sentence with specific keyword
     */
    private function extractNamesFromSentenceWithKeyword(string $sentence, string $keyword, Company $company): array
    {
        $names = [];
        
        Log::debug("🔧 Extracting names from sentence with keyword", [
            'company' => $company->name,
            'sentence' => $sentence,
            'sentence_hex' => bin2hex($sentence),
            'keyword' => $keyword,
            'keyword_hex' => bin2hex($keyword),
        ]);
        
        // Strategy 1: Direct pattern matching
        $patterns = [
            // Pattern: Keyword followed by name (代表取締役 田中太郎)
            '/(?:' . preg_quote($keyword, '/') . ')[\s　]+([^\s　\n\r]{2,}(?:[\s　][^\s　\n\r]{1,})?)/u',
            
            // Pattern: Name followed by keyword (田中太郎 代表取締役)
            '/([^\s　\n\r]{2,}(?:[\s　][^\s　\n\r]{1,}))[\s　]+(?:' . preg_quote($keyword, '/') . ')/u',
            
            // Pattern: Keyword with separators followed by name
            '/(?:' . preg_quote($keyword, '/') . ')[\s　：:・]+([^\s　\n\r]{2,}(?:[\s　][^\s　\n\r]{1,})?)/u',
        ];
        
        foreach ($patterns as $patternIndex => $pattern) {
            Log::debug("🧩 Testing pattern #{$patternIndex}", [
                'company' => $company->name,
                'pattern' => $pattern,
                'sentence' => $sentence,
            ]);
            
            if (preg_match_all($pattern, $sentence, $matches, PREG_SET_ORDER)) {
                Log::debug("✅ Pattern {$patternIndex} matched", [
                    'company' => $company->name,
                    'pattern' => $pattern,
                    'matches_count' => count($matches),
                    'matches' => $matches,
                ]);
                
                foreach ($matches as $match) {
                    $name = trim($match[1]);
                    $cleanName = $this->cleanExecutiveName($name);
                    
                    Log::debug("🔧 Processing pattern match", [
                        'company' => $company->name,
                        'pattern_index' => $patternIndex,
                        'raw_name' => $name,
                        'raw_name_hex' => bin2hex($name),
                        'clean_name' => $cleanName,
                        'clean_name_hex' => bin2hex($cleanName),
                    ]);
                    
                    if (!empty($cleanName)) {
                        $names[] = $cleanName;
                        Log::debug("📋 Extracted name from pattern {$patternIndex}", [
                            'company' => $company->name,
                            'raw_name' => $name,
                            'clean_name' => $cleanName,
                        ]);
                    }
                }
            } else {
                Log::debug("❌ Pattern {$patternIndex} no match", [
                    'company' => $company->name,
                ]);
            }
        }
        
        // Strategy 2: Simple keyword removal if patterns failed
        if (empty($names)) {
            Log::debug("🔄 Patterns failed, trying keyword removal", [
                'company' => $company->name,
            ]);
            
            $remainingText = str_replace($keyword, '', $sentence);
            $remainingText = preg_replace('/[\s　]+/u', ' ', $remainingText);
            $remainingText = trim($remainingText);
            
            // Remove common punctuation
            $remainingText = preg_replace('/^[\-\s　:：・]+|[\-\s　:：・]+$/u', '', $remainingText);
            
            Log::debug("🔧 Keyword removal result", [
                'company' => $company->name,
                'keyword' => $keyword,
                'original_sentence' => $sentence,
                'original_sentence_hex' => bin2hex($sentence),
                'remaining_text' => $remainingText,
                'remaining_text_hex' => bin2hex($remainingText),
                'remaining_text_length' => mb_strlen($remainingText),
            ]);
            
            if (!empty($remainingText)) {
                // Split by spaces to get individual names
                $possibleNames = preg_split('/[\s　]+/u', $remainingText);
                
                Log::debug("🔧 Split remaining text into possible names", [
                    'company' => $company->name,
                    'possible_names' => $possibleNames,
                    'count' => count($possibleNames),
                ]);
                
                foreach ($possibleNames as $possibleName) {
                    $possibleName = trim($possibleName);
                    
                    if (!empty($possibleName)) {
                        $cleanName = $this->cleanExecutiveName($possibleName);
                        
                        Log::debug("🔧 Processing possible name from keyword removal", [
                            'company' => $company->name,
                            'possible_name' => $possibleName,
                            'possible_name_hex' => bin2hex($possibleName),
                            'clean_name' => $cleanName,
                            'clean_name_hex' => bin2hex($cleanName),
                        ]);
                        
                        if (!empty($cleanName)) {
                            $names[] = $cleanName;
                            Log::debug("📋 Extracted name from keyword removal", [
                                'company' => $company->name,
                                'raw_name' => $possibleName,
                                'clean_name' => $cleanName,
                            ]);
                        }
                    }
                }
            } else {
                Log::warning("⚠️ No remaining text after keyword removal", [
                    'company' => $company->name,
                    'keyword' => $keyword,
                    'original_sentence' => $sentence,
                    'sentence_encoding_issue' => strpos($sentence, '�') !== false,
                    'possible_issue' => 'Text encoding problem or keyword matches entire sentence',
                ]);
            }
        }
        
        Log::debug("🏁 Name extraction completed", [
            'company' => $company->name,
            'sentence' => $sentence,
            'keyword' => $keyword,
            'extracted_names' => $names,
            'total_names' => count($names),
        ]);
        
        return $names;
    }

    /**
     * Clean executive name or position
     */
    private function cleanExecutiveName(string $text): string
    {
        // Remove common prefixes and suffixes
        $text = preg_replace('/^[:\-\s　]+|[:\-\s　]+$/u', '', $text);
        
        // Remove parentheses and their contents
        $text = preg_replace('/\([^)]*\)/u', '', $text);
        
        // Normalize spaces (including full-width spaces)
        $text = preg_replace('/[\s　]+/u', ' ', $text);
        
        return trim($text);
    }

    /**
     * Check if text looks like a person's name
     */
    private function looksLikeName(string $text): bool
    {
        $reasons = [];
        
        // Too short or too long
        if (strlen($text) < 2) {
            $reasons[] = 'too_short';
        }
        if (strlen($text) > 50) {
            $reasons[] = 'too_long';
        }
        
        // Contains mainly letters and spaces (including Japanese characters)
        if (!preg_match('/^[\p{L}\s　\-\.]+$/u', $text)) {
            $reasons[] = 'invalid_characters';
        }
        
        // Skip if contains common non-name words
        $nonNameWords = ['株式会社', '会社', '部門', '課', '係', 'Ltd', 'Inc', 'Corp', 'Co'];
        foreach ($nonNameWords as $word) {
            if (stripos($text, $word) !== false) {
                $reasons[] = "contains_company_word_{$word}";
            }
        }
        
        $isValid = empty($reasons);
        
        if (!$isValid) {
            Log::debug("🚫 Name validation failed", [
                'text' => $text,
                'reasons' => $reasons,
            ]);
        }
        
        return $isValid;
    }

    /**
     * Determine executive level from position
     */
    private function determineExecutiveLevel(string $position): string
    {
        $position = mb_strtolower($position);
        
        // C-level executives
        $cLevelKeywords = ['ceo', '社長', '代表', '最高', '会長', 'president', 'chairman'];
        foreach ($cLevelKeywords as $keyword) {
            if (strpos($position, $keyword) !== false) {
                return 'C-Level';
            }
        }
        
        // Senior executives
        $seniorKeywords = ['副社長', '専務', '常務', '取締役', 'director', 'vice', '部長', 'manager'];
        foreach ($seniorKeywords as $keyword) {
            if (strpos($position, $keyword) !== false) {
                return 'Senior';
            }
        }
        
        return 'Executive';
    }

    /**
     * Extract field value using multiple selectors
     */
    protected function extractFieldValue(Crawler $crawler, array $selectors): ?string
    {
        foreach ($selectors as $selector) {
            try {
                $nodes = $crawler->filter($selector);
                if ($nodes->count() > 0) {
                    $value = $nodes->first()->text();
                    if (!empty(trim($value))) {
                        return $value;
                    }
                }
            } catch (\Exception $e) {
                // Continue to next selector
                continue;
            }
        }
        
        return null;
    }

    /**
     * Extract phone number from text
     */
    protected function extractPhoneNumber(string $text): ?string
    {
        // Remove tel: prefix if present
        $text = preg_replace('/^tel:/', '', $text);
        
        // Extract phone number pattern
        if (preg_match('/[\d\-\(\)\s\+]+/', $text, $matches)) {
            return trim($matches[0]);
        }
        
        return null;
    }

    /**
     * Extract email from text
     */
    protected function extractEmail(string $text): ?string
    {
        // Remove mailto: prefix if present
        $text = preg_replace('/^mailto:/', '', $text);
        
        // Extract email pattern
        if (preg_match('/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/', $text, $matches)) {
            return trim($matches[0]);
        }
        
        return null;
    }

    /**
     * Extract year from text
     */
    protected function extractYear(string $text): ?int
    {
        // Extract 4-digit year
        if (preg_match('/\b(19|20)\d{2}\b/', $text, $matches)) {
            return (int) $matches[0];
        }
        
        return null;
    }

    /**
     * Resolve relative URL to absolute URL
     */
    protected function resolveUrl(string $baseUrl, string $relativeUrl): string
    {
        // If already absolute, return as is
        if (filter_var($relativeUrl, FILTER_VALIDATE_URL)) {
            return $relativeUrl;
        }
        
        $parsedBase = parse_url($baseUrl);
        $scheme = $parsedBase['scheme'] ?? 'https';
        $host = $parsedBase['host'] ?? '';
        
        // Handle protocol-relative URLs
        if (str_starts_with($relativeUrl, '//')) {
            return $scheme . ':' . $relativeUrl;
        }
        
        // Handle absolute paths
        if (str_starts_with($relativeUrl, '/')) {
            return $scheme . '://' . $host . $relativeUrl;
        }
        
        // Handle relative paths
        $basePath = dirname($parsedBase['path'] ?? '/');
        if ($basePath === '.') {
            $basePath = '/';
        }
        
        return $scheme . '://' . $host . rtrim($basePath, '/') . '/' . ltrim($relativeUrl, '/');
    }

    /**
     * Guess website URL from company link
     */
    protected function guessWebsiteFromLink(string $link, string $companyName): ?string
    {
        $parsedUrl = parse_url($link);
        
        if (isset($parsedUrl['scheme']) && isset($parsedUrl['host'])) {
            return $parsedUrl['scheme'] . '://' . $parsedUrl['host'];
        }
        
        return null;
    }

    /**
     * Cleanup Panther client
     */
    private function cleanupPantherClient(): void
    {
        if ($this->pantherClient) {
            try {
                $this->pantherClient->quit();
            } catch (\Exception $e) {
                // Ignore cleanup errors
                Log::debug("Error during Panther cleanup: " . $e->getMessage());
            } finally {
                $this->pantherClient = null;
            }
        }
    }

    /**
     * Destructor to ensure cleanup
     */
    public function __destruct()
    {
        $this->cleanupPantherClient();
    }

    /**
     * Test method for debugging WebDriver integration
     */
    public function testWebDriverIntegration(string $url = 'https://httpbin.org/html'): array
    {
        $results = [];
        
        // Test WebDriver method
        try {
            $webDriverContent = $this->getPageContentWithWebDriver($url);
            $results['webdriver'] = [
                'success' => !is_null($webDriverContent),
                'content_length' => $webDriverContent ? strlen($webDriverContent) : 0,
                'error' => null
            ];
        } catch (\Exception $e) {
            $results['webdriver'] = [
                'success' => false,
                'content_length' => 0,
                'error' => $e->getMessage()
            ];
        }
        
        // Test HTTP client fallback
        try {
            $response = $this->httpClient->get($url);
            $httpContent = $response->getBody()->getContents();
            $results['http'] = [
                'success' => true,
                'content_length' => strlen($httpContent),
                'error' => null
            ];
        } catch (\Exception $e) {
            $results['http'] = [
                'success' => false,
                'content_length' => 0,
                'error' => $e->getMessage()
            ];
        }
        
        // Test combined method
        try {
            $combinedContent = $this->getPageContent($url);
            $results['combined'] = [
                'success' => !is_null($combinedContent),
                'content_length' => $combinedContent ? strlen($combinedContent) : 0,
                'error' => null
            ];
        } catch (\Exception $e) {
            $results['combined'] = [
                'success' => false,
                'content_length' => 0,
                'error' => $e->getMessage()
            ];
        }
        
        return $results;
    }

    /**
     * Clean text by removing extra whitespace and HTML artifacts
     */
    protected function cleanText(string $text): string
    {
        // Remove HTML entities
        $text = html_entity_decode($text, ENT_QUOTES | ENT_HTML5, 'UTF-8');
        
        // Remove extra whitespace
        $text = preg_replace('/\s+/', ' ', $text);
        
        // Trim and return
        return trim($text);
    }

    /**
     * Wait for JavaScript content to fully load
     */
    private function waitForJavaScriptContent(string $url): void
    {
        $maxWaitTime = 15; // Maximum wait time in seconds
        $pollInterval = 0.5; // Check every 500ms
        $startTime = time();
        
        Log::debug("🕐 Waiting for JavaScript content to load", [
            'url' => $url,
            'max_wait_time' => $maxWaitTime,
        ]);
        
        $initialContentLength = 0;
        $stableCount = 0;
        $requiredStableChecks = 3; // Content must be stable for 3 checks
        
        while ((time() - $startTime) < $maxWaitTime) {
            try {
                // Check multiple indicators of JavaScript loading
                $contentLoaded = $this->checkJavaScriptLoadingComplete();
                
                if ($contentLoaded) {
                    Log::debug("✅ JavaScript loading indicators suggest content is ready");
                    break;
                }
                
                // Also check content stability
                $currentContent = $this->pantherClient->getPageSource();
                $currentLength = strlen($currentContent);
                
                if ($currentLength > $initialContentLength) {
                    $initialContentLength = $currentLength;
                    $stableCount = 0; // Reset stability counter
                } else {
                    $stableCount++;
                }
                
                // If content is stable for required checks, consider it loaded
                if ($stableCount >= $requiredStableChecks && $currentLength > 1000) {
                    Log::debug("✅ Content appears stable, assuming JavaScript loading complete", [
                        'content_length' => $currentLength,
                        'stable_checks' => $stableCount,
                    ]);
                    break;
                }
                
                usleep($pollInterval * 1000000); // Convert to microseconds
                
            } catch (\Exception $e) {
                Log::warning("Error while waiting for JavaScript content: " . $e->getMessage());
                break;
            }
        }
        
        $totalWaitTime = time() - $startTime;
        Log::debug("🏁 JavaScript content wait completed", [
            'url' => $url,
            'total_wait_time' => $totalWaitTime,
            'final_content_length' => strlen($this->pantherClient->getPageSource()),
        ]);
    }

    /**
     * Check various indicators that JavaScript loading is complete
     */
    private function checkJavaScriptLoadingComplete(): bool
    {
        try {
            // Check if document.readyState is complete
            $readyState = $this->pantherClient->executeScript('return document.readyState;');
            if ($readyState !== 'complete') {
                return false;
            }
            
            // Check if jQuery is present and finished loading
            $jQueryReady = $this->pantherClient->executeScript('
                if (typeof jQuery !== "undefined") {
                    return jQuery.active === 0;
                }
                return true;
            ');
            if (!$jQueryReady) {
                return false;
            }
            
            // Check for common loading indicators
            $loadingElements = $this->pantherClient->executeScript('
                var loadingSelectors = [
                    ".loading", ".spinner", ".loader", 
                    "[class*=\"loading\"]", "[class*=\"spinner\"]", 
                    "[style*=\"display: none\"]"
                ];
                
                var hasVisibleLoading = false;
                loadingSelectors.forEach(function(selector) {
                    var elements = document.querySelectorAll(selector);
                    elements.forEach(function(el) {
                        var style = window.getComputedStyle(el);
                        if (style.display !== "none" && style.visibility !== "hidden") {
                            hasVisibleLoading = true;
                        }
                    });
                });
                
                return !hasVisibleLoading;
            ');
            
            if (!$loadingElements) {
                return false;
            }
            
            // Check for network activity
            $networkIdle = $this->pantherClient->executeScript('
                return performance.getEntriesByType("resource").filter(function(entry) {
                    return entry.responseEnd === 0;
                }).length === 0;
            ');
            
            return $networkIdle;
            
        } catch (\Exception $e) {
            // If we can't execute JavaScript, assume loading is complete
            Log::debug("Could not check JavaScript loading status: " . $e->getMessage());
            return true;
        }
    }

    /**
     * Enhanced JavaScript detection for specific URLs
     */
    private function needsJavaScriptRendering(string $url): bool
    {
        // URLs that commonly require JavaScript
        $jsRequiredPatterns = [
            '/react/', '/angular/', '/vue/', '/spa/',
            '/ajax/', '/api/', '/dynamic/',
            'deha.co.jp', // Known to use JavaScript
        ];
        
        foreach ($jsRequiredPatterns as $pattern) {
            if (strpos($url, $pattern) !== false) {
                Log::debug("🎯 URL matches JavaScript requirement pattern", [
                    'url' => $url,
                    'pattern' => $pattern,
                ]);
                return true;
            }
        }
        
        // Quick check: fetch page statically first
        try {
            $response = $this->httpClient->get($url, ['timeout' => 5]);
            $staticContent = $response->getBody()->getContents();
            
            // Look for JavaScript frameworks
            $jsIndicators = [
                'react', 'angular', 'vue.js', 'backbone',
                'ember', 'knockout', 'jquery',
                'document.ready', 'window.onload',
                'ajax', 'fetch(', 'XMLHttpRequest',
                'spa-', 'single-page'
            ];
            
            $jsCount = 0;
            foreach ($jsIndicators as $indicator) {
                if (stripos($staticContent, $indicator) !== false) {
                    $jsCount++;
                }
            }
            
            // If we find multiple JS indicators, likely needs rendering
            if ($jsCount >= 3) {
                Log::debug("🎯 Multiple JavaScript indicators found, enabling JS rendering", [
                    'url' => $url,
                    'js_indicators_count' => $jsCount,
                ]);
                return true;
            }
            
            // Check if content is very minimal (likely SPA)
            $textContent = strip_tags($staticContent);
            if (strlen(trim($textContent)) < 500) {
                Log::debug("🎯 Minimal static content detected, likely SPA", [
                    'url' => $url,
                    'text_length' => strlen(trim($textContent)),
                ]);
                return true;
            }
            
        } catch (\Exception $e) {
            // If we can't fetch statically, assume JS is needed
            Log::debug("🎯 Static fetch failed, assuming JavaScript needed", [
                'url' => $url,
                'error' => $e->getMessage(),
            ]);
            return true;
        }
        
        return false;
    }

    /**
     * Get page content with smart JavaScript detection
     */
    private function getPageContentSmart(string $url): ?string
    {
        // Smart detection of JavaScript requirement
        if ($this->needsJavaScriptRendering($url)) {
            Log::info("🎯 JavaScript rendering detected as needed for URL", ['url' => $url]);
            return $this->getPageContentWithWebDriver($url);
        } else {
            Log::info("📄 Static content should be sufficient for URL", ['url' => $url]);
            
            // Try static first, fallback to JS if needed
            try {
                $response = $this->httpClient->get($url);
                $content = $response->getBody()->getContents();
                
                // Quick quality check - if content seems too minimal, try JS
                $textContent = strip_tags($content);
                if (strlen(trim($textContent)) < 300) {
                    Log::info("⚠️ Static content seems minimal, falling back to JavaScript rendering");
                    return $this->getPageContentWithWebDriver($url);
                }
                
                Log::info("✅ Static content appears sufficient", [
                    'content_length' => strlen($content),
                    'text_length' => strlen(trim($textContent)),
                ]);
                
                return $content;
                
            } catch (\Exception $e) {
                Log::warning("⚠️ Static fetch failed, falling back to JavaScript rendering", [
                    'error' => $e->getMessage(),
                ]);
                return $this->getPageContentWithWebDriver($url);
            }
        }
    }
}
