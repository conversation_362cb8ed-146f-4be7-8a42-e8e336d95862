<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;

use Illuminate\Support\Str;

class ExecutiveEmailService
{
    /**
     * Mapping bảng chữ cái <PERSON> sang Latin
     */
    private array $hiraganaToRomaji = [
        // Hiragana vowels
        'あ' => 'a',
        'い' => 'i',
        'う' => 'u',
        'え' => 'e',
        'お' => 'o',

        // K sounds
        'か' => 'ka',
        'き' => 'ki',
        'く' => 'ku',
        'け' => 'ke',
        'こ' => 'ko',
        'が' => 'ga',
        'ぎ' => 'gi',
        'ぐ' => 'gu',
        'げ' => 'ge',
        'ご' => 'go',

        // S sounds
        'さ' => 'sa',
        'し' => 'shi',
        'す' => 'su',
        'せ' => 'se',
        'そ' => 'so',
        'ざ' => 'za',
        'じ' => 'ji',
        'ず' => 'zu',
        'ぜ' => 'ze',
        'ぞ' => 'zo',

        // T sounds
        'た' => 'ta',
        'ち' => 'chi',
        'つ' => 'tsu',
        'て' => 'te',
        'と' => 'to',
        'だ' => 'da',
        'ぢ' => 'ji',
        'づ' => 'zu',
        'で' => 'de',
        'ど' => 'do',

        // N sounds
        'な' => 'na',
        'に' => 'ni',
        'ぬ' => 'nu',
        'ね' => 'ne',
        'の' => 'no',

        // H sounds
        'は' => 'ha',
        'ひ' => 'hi',
        'ふ' => 'fu',
        'へ' => 'he',
        'ほ' => 'ho',
        'ば' => 'ba',
        'び' => 'bi',
        'ぶ' => 'bu',
        'べ' => 'be',
        'ぼ' => 'bo',
        'ぱ' => 'pa',
        'ぴ' => 'pi',
        'ぷ' => 'pu',
        'ぺ' => 'pe',
        'ぽ' => 'po',

        // M sounds
        'ま' => 'ma',
        'み' => 'mi',
        'む' => 'mu',
        'め' => 'me',
        'も' => 'mo',

        // Y sounds
        'や' => 'ya',
        'ゆ' => 'yu',
        'よ' => 'yo',

        // R sounds
        'ら' => 'ra',
        'り' => 'ri',
        'る' => 'ru',
        'れ' => 're',
        'ろ' => 'ro',

        // W sounds
        'わ' => 'wa',
        'ゐ' => 'wi',
        'ゑ' => 'we',
        'を' => 'wo',

        // N
        'ん' => 'n',

        // Special characters
        'ー' => '', // Long vowel mark
        '・' => '', // Middle dot
        '々' => '', // Repetition mark
    ];

    private array $katakanaToRomaji = [
        // Katakana vowels
        'ア' => 'a',
        'イ' => 'i',
        'ウ' => 'u',
        'エ' => 'e',
        'オ' => 'o',

        // K sounds
        'カ' => 'ka',
        'キ' => 'ki',
        'ク' => 'ku',
        'ケ' => 'ke',
        'コ' => 'ko',
        'ガ' => 'ga',
        'ギ' => 'gi',
        'グ' => 'gu',
        'ゲ' => 'ge',
        'ゴ' => 'go',

        // S sounds
        'サ' => 'sa',
        'シ' => 'shi',
        'ス' => 'su',
        'セ' => 'se',
        'ソ' => 'so',
        'ザ' => 'za',
        'ジ' => 'ji',
        'ズ' => 'zu',
        'ゼ' => 'ze',
        'ゾ' => 'zo',

        // T sounds
        'タ' => 'ta',
        'チ' => 'chi',
        'ツ' => 'tsu',
        'テ' => 'te',
        'ト' => 'to',
        'ダ' => 'da',
        'ヂ' => 'ji',
        'ヅ' => 'zu',
        'デ' => 'de',
        'ド' => 'do',

        // N sounds
        'ナ' => 'na',
        'ニ' => 'ni',
        'ヌ' => 'nu',
        'ネ' => 'ne',
        'ノ' => 'no',

        // H sounds
        'ハ' => 'ha',
        'ヒ' => 'hi',
        'フ' => 'fu',
        'ヘ' => 'he',
        'ホ' => 'ho',
        'バ' => 'ba',
        'ビ' => 'bi',
        'ブ' => 'bu',
        'ベ' => 'be',
        'ボ' => 'bo',
        'パ' => 'pa',
        'ピ' => 'pi',
        'プ' => 'pu',
        'ペ' => 'pe',
        'ポ' => 'po',

        // M sounds
        'マ' => 'ma',
        'ミ' => 'mi',
        'ム' => 'mu',
        'メ' => 'me',
        'モ' => 'mo',

        // Y sounds
        'ヤ' => 'ya',
        'ユ' => 'yu',
        'ヨ' => 'yo',

        // R sounds
        'ラ' => 'ra',
        'リ' => 'ri',
        'ル' => 'ru',
        'レ' => 're',
        'ロ' => 'ro',

        // W sounds
        'ワ' => 'wa',
        'ヰ' => 'wi',
        'ヱ' => 'we',
        'ヲ' => 'wo',

        // N
        'ン' => 'n',

        // Special characters
        'ー' => '', // Long vowel mark
        '・' => '', // Middle dot
        '々' => '', // Repetition mark
    ];

    /**
     * Chuyển đổi tên Nhật Bản sang Romaji (Latin) bằng Python script
     */
    public function convertToRomaji(string $name): string
    {
        // Ensure input is valid UTF-8
        if (!mb_check_encoding($name, 'UTF-8')) {
            $name = mb_convert_encoding($name, 'UTF-8', 'auto');
        }

        // Remove common prefixes/suffixes first
        $cleaned = $this->removeCommonTitles($name);

        if (empty($cleaned)) {
            return '';
        }

        try {
            // Use Python script for accurate conversion
            $romaji = $this->convertUsingPythonScript($cleaned);

            if (!empty($romaji)) {
                // Clean up the result
                $romaji = preg_replace('/\s+/', ' ', $romaji); // Multiple spaces to single space
                $romaji = trim($romaji);

                return $romaji;
            }
        } catch (\Exception $e) {
            \Log::warning("Python script conversion failed, using fallback", [
                'name' => $cleaned,
                'error' => $e->getMessage()
            ]);
        }

        // Fallback to old method if Python script fails
        return $this->convertToRomajiFallback($cleaned);
    }

    /**
     * Convert Japanese text to Romaji using Python script
     */
    private function convertUsingPythonScript(string $name): string
    {
        // Get the absolute path to the Python script
        $scriptPath = base_path('crawl-executives-scripts/kanji_to_romaji.py');

        if (!file_exists($scriptPath)) {
            throw new \Exception("Python script not found at: {$scriptPath}");
        }

        // Escape the name for shell execution
        $escapedName = escapeshellarg($name);

        // Execute Python script with capitalization option
        $command = "cd " . escapeshellarg(base_path('crawl-executives-scripts')) . " && python kanji_to_romaji.py -c {$escapedName} 2>&1";

        $output = shell_exec($command);

        if ($output === null) {
            throw new \Exception("Failed to execute Python script");
        }

        $romaji = trim($output);

        // Check if output contains error messages
        if (stripos($romaji, 'error') !== false || stripos($romaji, 'traceback') !== false) {
            throw new \Exception("Python script error: {$romaji}");
        }

        // Format Japanese names properly (add space between surname and given name)
        $romaji = $this->formatJapaneseName($romaji);

        return $romaji;
    }

    /**
     * Format Japanese names to add proper spacing
     * Japanese names typically have surname first, then given name
     */
    private function formatJapaneseName(string $romaji): string
    {
        // Common Japanese surname patterns (first part of name)
        $japaneseSurnames = [
            'tanaka',
            'yamada',
            'sato',
            'suzuki',
            'takahashi',
            'watanabe',
            'ito',
            'nakamura',
            'kobayashi',
            'saito',
            'kato',
            'yoshida',
            'yamamoto',
            'sasaki',
            'matsumoto',
            'inoue',
            'kimura',
            'hayashi',
            'shimizu',
            'yamazaki',
            'mori',
            'abe',
            'ikeda',
            'hashimoto',
            'yamashita',
            'ishikawa',
            'nakajima',
            'maeda',
            'fujita',
            'ogawa',
            'goto',
            'okada',
            'hasegawa',
            'murai',
            'fukuda',
            'ota',
            'miura',
            'fujiwara',
            'okamoto',
            'matsuda',
            'nakagawa',
            'nakano',
            'harada',
            'ono',
            'tamura',
            'takeuchi',
            'kaneko',
            'wada'
        ];

        $lowerRomaji = strtolower($romaji);

        // Try to match common surname patterns and add space
        foreach ($japaneseSurnames as $surname) {
            if (strpos($lowerRomaji, $surname) === 0) {
                $surnameLength = strlen($surname);
                if (strlen($lowerRomaji) > $surnameLength) {
                    $givenName = substr($romaji, $surnameLength);
                    $formattedSurname = ucfirst($surname);
                    $formattedGivenName = ucfirst(strtolower($givenName));
                    return $formattedSurname . ' ' . $formattedGivenName;
                }
            }
        }

        // If no pattern match, try to split by common given name endings
        $commonGivenNameEndings = ['rou', 'ko', 'mi', 'ka', 'na', 'ta', 'ya', 'ki', 'shi', 'chi'];

        foreach ($commonGivenNameEndings as $ending) {
            $pos = strrpos($lowerRomaji, $ending);
            if ($pos !== false && $pos + strlen($ending) === strlen($lowerRomaji) && $pos > 3) {
                $surname = substr($romaji, 0, $pos);
                $givenName = substr($romaji, $pos);
                return ucfirst(strtolower($surname)) . ' ' . ucfirst(strtolower($givenName));
            }
        }

        // If no pattern found, try to split at reasonable points (around middle)
        $length = strlen($romaji);
        if ($length > 6) {
            $midPoint = intval($length / 2);
            // Try to find a good split point around the middle
            for ($i = $midPoint - 1; $i <= $midPoint + 1; $i++) {
                if ($i > 2 && $i < $length - 2) {
                    $surname = substr($romaji, 0, $i);
                    $givenName = substr($romaji, $i);
                    return ucfirst(strtolower($surname)) . ' ' . ucfirst(strtolower($givenName));
                }
            }
        }

        // Default: just return capitalized version
        return ucfirst(strtolower($romaji));
    }

    /**
     * Fallback conversion method (original implementation)
     */
    private function convertToRomajiFallback(string $name): string
    {
        // Try Kanji transliteration first
        $transliterated = $this->transliterateKanji($name);

        $romaji = $transliterated;

        // Convert Hiragana to Romaji
        foreach ($this->hiraganaToRomaji as $hiragana => $latin) {
            $romaji = str_replace($hiragana, $latin, $romaji);
        }

        // Convert Katakana to Romaji
        foreach ($this->katakanaToRomaji as $katakana => $latin) {
            $romaji = str_replace($katakana, $latin, $romaji);
        }

        // Clean up the result but preserve useful characters
        $romaji = preg_replace('/[^\w\s\-]/u', '', $romaji); // Remove special chars
        $romaji = preg_replace('/\s+/', ' ', $romaji); // Multiple spaces to single space
        $romaji = trim($romaji);

        // Convert to lowercase if it contains Latin characters
        if (preg_match('/[a-zA-Z]/', $romaji)) {
            $romaji = strtolower($romaji);
        }

        // Ensure output is clean UTF-8
        $romaji = mb_convert_encoding($romaji, 'UTF-8', 'UTF-8');

        // Remove any null bytes or control characters
        $romaji = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $romaji);

        return $romaji;
    }

    /**
     * Loại bỏ các title/honorific phổ biến
     */
    private function removeCommonTitles(string $name): string
    {
        $titles = [
            '代表取締役',
            '取締役',
            '社長',
            '会長',
            '部長',
            '課長',
            '係長',
            '主任',
            '専務',
            '常務',
            '副社長',
            '執行役員',
            '顧問',
            '相談役',
            '監査役',
            'CEO',
            'CTO',
            'CFO',
            'COO',
            'CIO',
            'CMO',
            'President',
            'Director',
            'Manager',
            'Executive',
            'Officer',
            '株式会社',
            '有限会社',
            '合同会社',
            '合資会社',
            '合名会社',
            '（株）',
            '(株)',
            '（有）',
            '(有)',
            '㈱',
            '㈲',
            'Mr.',
            'Ms.',
            'Dr.',
            'Prof.',
            '先生',
            '様'
        ];

        foreach ($titles as $title) {
            $name = str_replace($title, '', $name);
        }

        return trim($name);
    }

    /**
     * Tạo dự đoán email từ tên và domain công ty
     */
    public function generateEmailPredictions(string $executiveName, string $companyDomain): array
    {
        try {
            if (empty($executiveName) || empty($companyDomain)) {
                return [];
            }

            // Ensure input is valid UTF-8 and clean encoding
            $executiveName = $this->sanitizeUTF8($executiveName);
            if (empty($executiveName)) {
                return [];
            }

            // Chuyển đổi tên sang Romaji
            $romajiName = $this->convertToRomaji($executiveName);

            // If romaji contains non-latin characters (like Kanji), try to create fallback
            $latinName = $romajiName;
            if (preg_match('/[\p{Han}]/u', $romajiName)) {
                // Contains Kanji - try to extract any Latin parts or create generic email
                $latinParts = preg_split('/[\p{Han}\s]+/u', $romajiName, -1, PREG_SPLIT_NO_EMPTY);
                if (!empty($latinParts)) {
                    $latinName = implode(' ', $latinParts);
                } else {
                    // If no Latin parts, use transliterated version or generic
                    $latinName = $this->transliterateKanji($executiveName);
                }
            }

            // Tách tên thành các phần
            $nameParts = explode(' ', $latinName);
            $nameParts = array_filter($nameParts); // Remove empty parts

            if (empty($nameParts)) {
                return [];
            }

            // Clean domain (remove protocol, www, etc.)
            $domain = $this->cleanDomain($companyDomain);

            if (empty($domain)) {
                return [];
            }

            $emailPredictions = [];

            // Ensure all name parts are clean ASCII for email
            $cleanNameParts = array_map(function ($part) {
                return $this->sanitizeForEmail($part);
            }, $nameParts);
        } catch (\Exception $e) {
            \Log::warning("Error in generateEmailPredictions", [
                'name' => $executiveName,
                'domain' => $companyDomain,
                'error' => $e->getMessage()
            ]);
            return [];
        }

        // Filter out empty parts
        $cleanNameParts = array_filter($cleanNameParts);

        if (empty($cleanNameParts)) {
            return [];
        }

        try {
            // Format 1: <EMAIL>
            if (count($cleanNameParts) >= 2) {
                $firstName = $cleanNameParts[0];
                $lastName = end($cleanNameParts);
                if (!empty($firstName) && !empty($lastName)) {
                    $emailPredictions[] = $firstName . '.' . $lastName . '@' . $domain;
                    $emailPredictions[] = $lastName . '.' . $firstName . '@' . $domain;
                }
            }

            // Format 2: <EMAIL>
            if (count($cleanNameParts) >= 2) {
                $fullName = implode('', $cleanNameParts);
                if (!empty($fullName)) {
                    $emailPredictions[] = $fullName . '@' . $domain;
                }
            }

            // Format 3: <EMAIL>
            if (!empty($cleanNameParts[0])) {
                $emailPredictions[] = $cleanNameParts[0] . '@' . $domain;
            }

            // Format 4: <EMAIL> (first initial + last name)
            if (count($cleanNameParts) >= 2) {
                $firstInitial = substr($cleanNameParts[0], 0, 1);
                $lastName = end($cleanNameParts);
                if (!empty($firstInitial) && !empty($lastName)) {
                    $emailPredictions[] = $firstInitial . '.' . $lastName . '@' . $domain;
                    $emailPredictions[] = $firstInitial . $lastName . '@' . $domain;
                }
            }

            // Format 5: <EMAIL> (first name + last initial)
            if (count($cleanNameParts) >= 2) {
                $firstName = $cleanNameParts[0];
                $lastInitial = substr(end($cleanNameParts), 0, 1);
                if (!empty($firstName) && !empty($lastInitial)) {
                    $emailPredictions[] = $firstName . '.' . $lastInitial . '@' . $domain;
                    $emailPredictions[] = $firstName . $lastInitial . '@' . $domain;
                }
            }

            // Format 6: <EMAIL>
            if (count($cleanNameParts) >= 2) {
                $firstName = $cleanNameParts[0];
                $lastName = end($cleanNameParts);
                if (!empty($firstName) && !empty($lastName)) {
                    $emailPredictions[] = $firstName . '_' . $lastName . '@' . $domain;
                }
            }

            // Format 7: <EMAIL>
            if (count($cleanNameParts) >= 2) {
                $firstName = $cleanNameParts[0];
                $lastName = end($cleanNameParts);
                if (!empty($firstName) && !empty($lastName)) {
                    $emailPredictions[] = $firstName . '-' . $lastName . '@' . $domain;
                }
            }

            // Remove duplicates and empty emails
            $emailPredictions = array_filter(array_unique($emailPredictions));

            // Validate each email format
            $validEmails = [];
            foreach ($emailPredictions as $email) {
                if ($this->isValidEmail($email)) {
                    $validEmails[] = $email;
                }
            }

            return $validEmails;
        } catch (\Exception $e) {
            \Log::warning("Error generating email formats", [
                'cleanNameParts' => $cleanNameParts,
                'domain' => $domain,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Simple transliteration for Kanji (fallback)
     */
    private function transliterateKanji(string $name): string
    {
        // Common Kanji to Romaji mappings for names
        $kanjiMap = [
            '田中' => 'tanaka',
            '山田' => 'yamada',
            '佐藤' => 'sato',
            '鈴木' => 'suzuki',
            '高橋' => 'takahashi',
            '渡辺' => 'watanabe',
            '太郎' => 'taro',
            '花子' => 'hanako',
            '次郎' => 'jiro',
            '三郎' => 'saburo',
            // Add more common name mappings as needed
        ];

        $result = $name;
        foreach ($kanjiMap as $kanji => $romaji) {
            $result = str_replace($kanji, $romaji, $result);
        }

        // If still contains Kanji, create a generic name
        if (preg_match('/[\p{Han}]/u', $result)) {
            // Extract any remaining Latin characters
            $latin = preg_replace('/[^\w\s]/u', '', $result);
            $latin = preg_replace('/\s+/', ' ', trim($latin));

            if (empty($latin)) {
                // If no Latin at all, use a generic format
                return 'user' . substr(md5($name), 0, 4);
            }

            return $latin;
        }

        return trim($result);
    }

    /**
     * Làm sạch domain name
     */
    private function cleanDomain(string $domain): string
    {
        // Remove protocol
        $domain = preg_replace('/^https?:\/\//', '', $domain);

        // Remove www
        $domain = preg_replace('/^www\./', '', $domain);

        // Remove trailing slash and path
        $domain = strtok($domain, '/');

        // Remove port if exists
        $domain = strtok($domain, ':');

        return strtolower(trim($domain));
    }

    /**
     * Xử lý một executive và tạo email predictions
     */
    public function processExecutive(array $executive, string $companyDomain): array
    {
        $name = $executive['name'] ?? '';

        // Always add the new fields, even if name is empty
        $executive['name_romaji'] = '';
        $executive['email_predictions'] = [];

        if (!empty($name)) {
            try {
                // Sanitize input name
                $name = $this->sanitizeUTF8($name);

                if (!empty($name)) {
                    // Thêm tên Latin và email predictions
                    $romaji = $this->convertToRomaji($name);
                    $predictions = $this->generateEmailPredictions($name, $companyDomain);

                    // Ensure outputs are clean
                    $executive['name_romaji'] = $this->sanitizeUTF8($romaji);
                    $executive['email_predictions'] = array_filter($predictions, function ($email) {
                        return $this->isValidEmail($email);
                    });
                }
            } catch (\Exception $e) {
                // Log error but don't fail the entire process
                Log::warning("Error processing executive email predictions", [
                    'name' => $name,
                    'domain' => $companyDomain,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $executive;
    }

    /**
     * Sanitize UTF-8 string safely
     */
    private function sanitizeUTF8(string $input): string
    {
        if (empty($input)) {
            return '';
        }

        try {
            // First try to detect and fix encoding
            if (!mb_check_encoding($input, 'UTF-8')) {
                $input = mb_convert_encoding($input, 'UTF-8', 'auto');
            }

            // Remove malformed UTF-8 sequences
            $input = mb_convert_encoding($input, 'UTF-8', 'UTF-8');

            // Trim whitespace
            $input = trim($input);

            return $input;
        } catch (\Exception $e) {
            Log::warning("Error sanitizing UTF-8 string", [
                'input' => substr($input, 0, 100),
                'error' => $e->getMessage()
            ]);
            return '';
        }
    }

    /**
     * Sanitize text for email format (ASCII only)
     */
    private function sanitizeForEmail(string $input): string
    {
        try {
            // Remove any non-ASCII characters and ensure clean UTF-8
            $clean = preg_replace('/[^\x20-\x7E]/', '', $input);
            $clean = preg_replace('/[^a-zA-Z0-9]/', '', $clean);
            $clean = strtolower(trim($clean));

            return $clean;
        } catch (\Exception $e) {
            \Log::warning("Error sanitizing for email", [
                'input' => $input,
                'error' => $e->getMessage()
            ]);
            return '';
        }
    }

    /**
     * Validate email format safely with MX check
     */
    private function isValidEmail(string $email): bool
    {
        try {
            // Check encoding first
            if (!mb_check_encoding($email, 'UTF-8')) {
                return false;
            }

            // Check basic format
            if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
                return false;
            }

            // Check if email has at least 3 characters before @ symbol
            $atPosition = strpos($email, '@');
            if ($atPosition === false || $atPosition < 3) {
                return false;
            }

            // Additional checks for malformed emails
            if (strpos($email, '..') !== false || strpos($email, '@@') !== false) {
                return false;
            }

            // Extract domain and check MX record
            $domain = substr($email, $atPosition + 1);
            if (!$this->hasMxRecord($domain)) {
                return false;
            }

            return true;
        } catch (\Exception $e) {
            \Log::warning("Error validating email", [
                'email' => $email,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Check if domain has MX record
     */
    private function hasMxRecord(string $domain): bool
    {
        try {
            // Cache MX check results to avoid repeated DNS lookups
            $cacheKey = 'mx_check_' . md5($domain);

            if (cache()->has($cacheKey)) {
                return cache()->get($cacheKey);
            }

            // Check MX record
            $mxRecords = dns_get_record($domain, DNS_MX);
            $hasMx = !empty($mxRecords);

            // Cache result for 1 hour
            cache()->put($cacheKey, $hasMx, 3600);

            return $hasMx;
        } catch (\Exception $e) {
            \Log::warning("Error checking MX record", [
                'domain' => $domain,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Xử lý tất cả executives của một công ty
     */
    public function processCompanyExecutives(array $executives, string $companyDomain): array
    {
        return array_map(function ($executive) use ($companyDomain) {
            return $this->processExecutive($executive, $companyDomain);
        }, $executives);
    }
}
