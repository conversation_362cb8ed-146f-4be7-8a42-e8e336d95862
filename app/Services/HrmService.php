<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;

class HrmService
{
    /**
     * Call the HRM API with the given parameters
     *
     * @param string $route The API route
     * @param string $method The HTTP method (get, post, put, delete)
     * @param string $param Additional query parameters
     * @return \Illuminate\Http\Client\Response
     */
    public function callApiHrm($route, $method, $param = '')
    {
        return Http::$method(config("hrm.host") . $route . "?api_token=" . config("hrm.api_token") . '&' . $param);
    }

    /**
     * Get departments list from HRM system
     *
     * @return array
     */
    public function getDepartments()
    {
        $response = $this->callApiHrm('/api/getPartsByListNameId', 'get');
        
        if ($response->successful()) {
            return $response->json('data') ?? [];
        }
        
        return [];
    }

    /**
     * Sync departments from HRM system to local database
     *
     * @return array Summary of sync operation
     */
    public function syncDepartments()
    {
        $departments = $this->getDepartments();
        
        $stats = [
            'created' => 0,
            'updated' => 0,
            'deactivated' => 0,
            'skipped' => 0,
            'total' => count($departments),
        ];
        
        // Collect all the external_ids from the API response to track which departments exist in the HRM system
        $existingExternalIds = [];
        
        if (empty($departments)) {
            // If API returns no departments, deactivate all departments in our DB (optional, depends on requirements)
            $deactivatedCount = \App\Models\Department::where('is_active', true)->update(['is_active' => false]);
            $stats['deactivated'] = $deactivatedCount;
            return $stats;
        }
        
        foreach ($departments as $deptData) {
            // If there's no external ID or name, skip this record
            if (empty($deptData['id']) || empty($deptData['name'])) {
                $stats['skipped']++;
                continue;
            }
            
            // Add this external_id to our tracking array
            $existingExternalIds[] = $deptData['id'];
            
            // Try to find the department by external_id first
            $department = \App\Models\Department::firstWhere('external_id', $deptData['id']);
            
            // Use the department name directly as the code
            $departmentData = [
                'name' => $deptData['name'],
                'code' => $deptData['name'],
                'external_id' => $deptData['id'],
                'description' => null,
                'is_active' => true,
            ];
            
            // Manager fields are set to null as they're not provided by the HRM system
            $departmentData['manager_id'] = null;
            $departmentData['manager_name'] = null;
            
            if ($department) {
                $department->update($departmentData);
                $stats['updated']++;
            } else {
                \App\Models\Department::create($departmentData);
                $stats['created']++;
            }
        }
        
        // Deactivate departments that weren't in the API response (they don't exist in HRM anymore)
        if (!empty($existingExternalIds)) {
            $deactivatedCount = \App\Models\Department::whereNotIn('external_id', $existingExternalIds)
                ->where('is_active', true)
                ->update(['is_active' => false]);
            
            $stats['deactivated'] = $deactivatedCount;
        }
        
        return $stats;
    }
    
    /**
     * Lấy thông tin chi tiết người dùng từ HRM thông qua API getInfoByEmail
     *
     * @param string $email Email của người dùng cần lấy thông tin
     * @return array|null Thông tin chi tiết người dùng hoặc null nếu không tìm thấy
     */
    public function getUserInfoByEmail($email)
    {
        if (empty($email)) {
            return null;
        }
        
        $response = $this->callApiHrm('/api/getInfoByEmail', 'get', 'email=' . urlencode($email));
        
        if ($response->successful()) {
            $userData = $response->json('data');
            
            if (!empty($userData)) {
                return $userData;
            }
        }
        
        return null;
    }
}
