<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class FastEmailValidationService
{
    /**
     * Validate email format and MX record (fast validation)
     */
    public function validateEmail(string $email): bool
    {
        try {
            // Basic format check
            if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
                return false;
            }

            // Check minimum length before @
            $atPosition = strpos($email, '@');
            if ($atPosition === false || $atPosition < 3) {
                return false;
            }

            // Check for malformed patterns
            if (strpos($email, '..') !== false || strpos($email, '@@') !== false) {
                return false;
            }

            // Extract domain and check MX record
            $domain = substr($email, $atPosition + 1);
            if (!$this->hasMxRecord($domain)) {
                return false;
            }

            return true;
        } catch (\Exception $e) {
            Log::warning("Error validating email", [
                'email' => $email,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Check if domain has MX record (with caching)
     */
    private function hasMxRecord(string $domain): bool
    {
        try {
            // Cache MX check results to avoid repeated DNS lookups
            $cacheKey = 'mx_check_' . md5($domain);

            if (Cache::has($cacheKey)) {
                return Cache::get($cacheKey);
            }

            // Check MX record
            $mxRecords = dns_get_record($domain, DNS_MX);
            $hasMx = !empty($mxRecords);

            // Cache result for 1 hour
            Cache::put($cacheKey, $hasMx, 3600);

            return $hasMx;
        } catch (\Exception $e) {
            Log::warning("Error checking MX record", [
                'domain' => $domain,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Validate multiple emails (batch processing)
     */
    public function validateEmails(array $emails): array
    {
        $validEmails = [];
        $invalidEmails = [];

        foreach ($emails as $email) {
            if ($this->validateEmail($email)) {
                $validEmails[] = $email;
            } else {
                $invalidEmails[] = $email;
            }
        }

        return [
            'valid' => $validEmails,
            'invalid' => $invalidEmails,
            'total' => count($emails),
            'valid_count' => count($validEmails),
            'invalid_count' => count($invalidEmails)
        ];
    }

    /**
     * Get validation statistics
     */
    public function getValidationStats(array $emails): array
    {
        $result = $this->validateEmails($emails);

        return [
            'total' => $result['total'],
            'valid' => $result['valid_count'],
            'invalid' => $result['invalid_count'],
            'valid_percentage' => $result['total'] > 0 ? round(($result['valid_count'] / $result['total']) * 100, 2) : 0
        ];
    }
}
