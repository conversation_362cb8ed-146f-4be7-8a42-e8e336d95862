<?php

namespace App\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Symfony\Component\DomCrawler\Crawler;
use Symfony\Component\Panther\PantherTestCase;
use Symfony\Component\Panther\Client as PantherClient;
use Facebook\WebDriver\Remote\RemoteWebDriver;
use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverWait;
use Facebook\WebDriver\WebDriverExpectedCondition;
use Facebook\WebDriver\Exception\TimeoutException;
use Facebook\WebDriver\Exception\WebDriverException;
use App\Models\DataSource;
use App\Models\Company;
use App\Models\Keyword;
use Illuminate\Support\Facades\Log;

class EnhancedWebCrawlerService extends WebCrawlerService
{
    private static ?PantherClient $sharedPantherClient = null;
    private static int $tabCounter = 0;
    private ?PantherClient $pantherClient = null;
    private bool $useJavaScript = false;
    private int $jsWaitTime = 3; // seconds to wait for J<PERSON> to render
    
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Get protected property from parent class
     */
    private function getHttpClient(): Client
    {
        return $this->httpClient ?? new Client([
            'timeout' => 30,
            'headers' => [
                'User-Agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language' => 'vi-VN,vi;q=0.9,en;q=0.8',
                'Accept-Encoding' => 'gzip, deflate, br',
                'Connection' => 'keep-alive',
                'Upgrade-Insecure-Requests' => '1',
            ],
        ]);
    }

    /**
     * Set whether to use JavaScript rendering
     */
    public function setJavaScriptEnabled(bool $enabled): self
    {
        $this->useJavaScript = $enabled;
        return $this;
    }

    /**
     * Set wait time for JavaScript rendering
     */
    public function setJavaScriptWaitTime(int $seconds): self
    {
        $this->jsWaitTime = $seconds;
        return $this;
    }

    /**
     * Get or create shared Panther client for browser automation
     */
    private function getSharedPantherClient(): PantherClient
    {
        if (self::$sharedPantherClient === null) {
            Log::info("Initializing shared Chrome browser for JavaScript rendering");
            
            try {
                // Set Chrome binary path for macOS
                $chromeBinary = '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome';
                $chromeDriverPath = base_path('chromedriver');
                
                if (!file_exists($chromeBinary)) {
                    throw new \Exception("Chrome browser not found at: {$chromeBinary}");
                }
                
                if (!file_exists($chromeDriverPath)) {
                    throw new \Exception("ChromeDriver not found at: {$chromeDriverPath}");
                }
                
                // Make sure ChromeDriver is executable
                if (!is_executable($chromeDriverPath)) {
                    chmod($chromeDriverPath, 0755);
                }
                
                // Set environment variables for Panther
                putenv("PANTHER_CHROME_BINARY={$chromeBinary}");
                putenv("PANTHER_CHROME_DRIVER_BINARY={$chromeDriverPath}");
                putenv("PANTHER_NO_HEADLESS=0"); // Force headless mode
                
                // Enhanced Chrome options for stability and multi-tab support
                $chromeOptions = [
                    '--headless=new',
                    '--no-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-gpu',
                    '--disable-extensions',
                    '--disable-images',
                    '--disable-plugins',
                    '--disable-java',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--disable-background-timer-throttling',
                    '--disable-renderer-backgrounding',
                    '--disable-backgrounding-occluded-windows',
                    '--memory-pressure-off',
                    '--max_old_space_size=4096',
                    '--window-size=1920,1080',
                    '--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    // Multi-tab options
                    '--enable-automation',
                    '--disable-popup-blocking',
                    '--disable-default-apps',
                    '--no-first-run',
                    '--disable-sync',
                ];
                
                // Create shared Panther client
                self::$sharedPantherClient = PantherClient::createChromeClient($chromeDriverPath, $chromeOptions);
                
                Log::info("Shared Chrome browser initialized successfully");
                
            } catch (\Exception $e) {
                Log::error("Failed to initialize shared Chrome browser: " . $e->getMessage());
                self::$sharedPantherClient = null;
                throw $e;
            }
        }
        
        return self::$sharedPantherClient;
    }
    
    /**
     * Create new tab in shared browser
     */
    private function createNewTab(): PantherClient
    {
        try {
            $sharedClient = $this->getSharedPantherClient();
            
            // Increment tab counter for tracking
            self::$tabCounter++;
            
            Log::debug("Creating new tab #{" . self::$tabCounter . "} in shared browser");
            
            // Get WebDriver instance and create new window/tab
            $webDriver = $sharedClient->getWebDriver();
            $webDriver->switchTo()->newWindow(\Facebook\WebDriver\WebDriverTargetLocator::WINDOW_TYPE_TAB);
            
            // Return the shared client (now switched to new tab)
            return $sharedClient;
            
        } catch (\Exception $e) {
            Log::error("Failed to create new tab: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Close current tab (but keep browser alive)
     */
    private function closeCurrentTab(): void
    {
        try {
            if (self::$sharedPantherClient) {
                $webDriver = self::$sharedPantherClient->getWebDriver();
                
                // Check if session is still valid
                if (!$this->isWebDriverSessionValid($webDriver)) {
                    Log::debug("WebDriver session is invalid, skipping tab cleanup");
                    $this->resetSharedPantherClient();
                    return;
                }
                
                $allHandles = $webDriver->getWindowHandles();
                
                // Only close if we have multiple tabs
                if (count($allHandles) > 1) {
                    Log::debug("Closing current tab, " . (count($allHandles) - 1) . " tabs remaining");
                    $webDriver->close();
                    
                    // Switch to first available tab
                    $webDriver->switchTo()->window($allHandles[0]);
                } else {
                    Log::debug("Only one tab remaining, navigating to blank page instead of closing");
                    // Instead of closing, just navigate to blank page to reset state
                    $webDriver->get('about:blank');
                    
                    // Clear any cookies/local storage
                    try {
                        $webDriver->manage()->deleteAllCookies();
                        $webDriver->executeScript('localStorage.clear(); sessionStorage.clear();');
                    } catch (\Exception $e) {
                        // Ignore cleanup errors
                    }
                }
            }
        } catch (\Exception $e) {
            Log::debug("Error managing tab: " . $e->getMessage());
            
            // If session is invalid, reset shared client
            if (strpos($e->getMessage(), 'invalid session id') !== false) {
                Log::info("Resetting shared Chrome client due to invalid session in closeCurrentTab");
                $this->resetSharedPantherClient();
            }
        }
    }

    /**
     * Cleanup tab (close current tab but keep browser alive)
     */
    private function cleanupPantherClient(): void
    {
        try {
            // Just close the current tab instead of killing entire browser
            $this->closeCurrentTab();
            
            // Set pantherClient to null so destructor won't try to close it again
            $this->pantherClient = null;
            
        } catch (\Exception $e) {
            Log::debug("Error during tab cleanup: " . $e->getMessage());
            // Set to null anyway to prevent destructor issues
            $this->pantherClient = null;
        }
    }
    
    /**
     * Force cleanup all Chrome processes (only use when necessary)
     */
    public static function forceCleanupAllChrome(): void
    {
        try {
            Log::info("Force cleaning up all Chrome processes...");
            
            // Close shared client first
            if (self::$sharedPantherClient) {
                try {
                    self::$sharedPantherClient->quit();
                } catch (\Exception $e) {
                    Log::debug("Error during graceful quit: " . $e->getMessage());
                }
                self::$sharedPantherClient = null;
                self::$tabCounter = 0;
            }
            
            // Kill Chrome and ChromeDriver processes
            exec('pkill -f "chrome\|chromium" 2>/dev/null || true');
            exec('pkill -f "chromedriver" 2>/dev/null || true');
            exec('pkill -f "Google Chrome" 2>/dev/null || true');
            
            // Clear Chrome temp directories
            exec('rm -rf /tmp/.com.google.Chrome.* 2>/dev/null || true');
            exec('rm -rf /tmp/chrome_* 2>/dev/null || true');
            exec('rm -rf /tmp/puppeteer_* 2>/dev/null || true');
            exec('rm -rf /tmp/browsershot_* 2>/dev/null || true');
            
            // Clear memory
            if (function_exists('gc_collect_cycles')) {
                gc_collect_cycles();
            }
            
            sleep(2);
            Log::info("Chrome cleanup completed");
            
        } catch (\Exception $e) {
            Log::warning("Chrome cleanup failed: " . $e->getMessage());
        }
    }

    /**
     * Get Panther client for browser automation
     */
    private function getPantherClient(): PantherClient
    {
        if ($this->pantherClient === null) {
            // Create Panther client with Chrome options using static method
            $this->pantherClient = \Symfony\Component\Panther\Client::createChromeClient(null, [
                '--headless',
                '--no-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--window-size=1920,1080',
                '--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            ]);
        }
        
        return $this->pantherClient;
    }

    /**
     * Enhanced method to fetch content with optional JavaScript rendering
     */
    public function fetchContent(string $url, bool $forceJavaScript = false): array
    {
        $useJS = $this->useJavaScript || $forceJavaScript;
        
        if ($useJS) {
            return $this->fetchContentWithJavaScript($url);
        } else {
            return $this->fetchContentWithHttp($url);
        }
    }

    /**
     * Fetch content using traditional HTTP client (static HTML)
     */
    private function fetchContentWithHttp(string $url): array
    {
        try {
            $response = $this->getHttpClient()->get($url);
            $html = $response->getBody()->getContents();
            
            return [
                'success' => true,
                'html' => $html,
                'method' => 'http',
                'status_code' => $response->getStatusCode(),
                'error' => null,
            ];
        } catch (RequestException $e) {
            return [
                'success' => false,
                'html' => '',
                'method' => 'http',
                'status_code' => $e->getCode(),
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Fetch content using browser automation (JavaScript rendering) with tab management
     */
    private function fetchContentWithJavaScript(string $url): array
    {
        $webDriver = null;
        
        try {
            Log::info("Attempting JavaScript rendering with Chrome tab for URL: {$url}");
            
            // Get shared browser instance
            $sharedClient = $this->getSharedPantherClient();
            
            // Create new tab in shared browser
            $webDriver = $sharedClient->getWebDriver();
            
            // Check if session is valid before proceeding
            if (!$this->isWebDriverSessionValid($webDriver)) {
                Log::warning("WebDriver session invalid, creating new browser instance");
                $this->resetSharedPantherClient();
                $sharedClient = $this->getSharedPantherClient();
                $webDriver = $sharedClient->getWebDriver();
            }
            
            $webDriver->switchTo()->newWindow(\Facebook\WebDriver\WebDriverTargetLocator::WINDOW_TYPE_TAB);
            
            // Navigate to the URL in the new tab
            $webDriver->get($url);
            
            // Enhanced waiting strategy for complex SPAs
            $this->waitForCompletePageLoad($url, $webDriver);
            
            // Get the rendered HTML
            $html = $webDriver->getPageSource();
            
            if (empty($html) || strlen(trim($html)) < 100) {
                throw new \Exception("JavaScript rendering returned insufficient content");
            }
            
            Log::info("JavaScript rendering successful, content length: " . strlen($html));
            
            // Close this tab after getting content
            $this->closeSpecificTab($webDriver);
            
            return [
                'success' => true,
                'html' => $html,
                'method' => 'javascript',
                'error' => null
            ];
            
        } catch (\Exception $e) {
            Log::error("JavaScript crawling failed for URL: {$url}", ['error' => $e->getMessage()]);
            
            // Try to cleanup current tab if it exists
            if ($webDriver) {
                try {
                    $this->closeSpecificTab($webDriver);
                } catch (\Exception $cleanupError) {
                    Log::debug("Error closing specific tab: " . $cleanupError->getMessage());
                }
            }
            
            // If session error, reset client for next attempt
            if (strpos($e->getMessage(), 'invalid session id') !== false) {
                Log::info("Resetting shared Chrome client due to session error");
                $this->resetSharedPantherClient();
            }
            
            // Fallback to HTTP method
            Log::info("Falling back to HTTP method for URL: {$url}");
            $fallbackResult = $this->fetchContentWithHttp($url);
            if ($fallbackResult['success']) {
                $fallbackResult['method'] = 'http_fallback';
            }
            return $fallbackResult;
        }
    }
    
    /**
     * Close specific tab and switch to first available tab
     */
    private function closeSpecificTab($webDriver): void
    {
        try {
            // Check if session is still valid
            if (!$this->isWebDriverSessionValid($webDriver)) {
                Log::debug("WebDriver session is invalid, skipping tab cleanup");
                return;
            }
            
            $allHandles = $webDriver->getWindowHandles();
            
            if (count($allHandles) > 1) {
                // Close current tab
                $webDriver->close();
                
                // Switch to first available tab
                $webDriver->switchTo()->window($allHandles[0]);
                Log::debug("Closed tab, " . (count($allHandles) - 1) . " tabs remaining");
            } else {
                // Navigate to blank page instead of closing last tab
                $webDriver->get('about:blank');
                
                // Clear browser state
                try {
                    $webDriver->manage()->deleteAllCookies();
                    $webDriver->executeScript('localStorage.clear(); sessionStorage.clear();');
                } catch (\Exception $e) {
                    // Ignore cleanup errors
                }
                Log::debug("Reset last tab to blank page");
            }
        } catch (\Exception $e) {
            Log::debug("Error closing specific tab: " . $e->getMessage());
            
            // If session is invalid, reset shared client
            if (strpos($e->getMessage(), 'invalid session id') !== false) {
                Log::info("Resetting shared Chrome client due to invalid session");
                $this->resetSharedPantherClient();
            }
        }
    }
    
    /**
     * Check if WebDriver session is still valid
     */
    private function isWebDriverSessionValid($webDriver): bool
    {
        try {
            $webDriver->getCurrentURL();
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Reset shared Panther client
     */
    private function resetSharedPantherClient(): void
    {
        try {
            if (self::$sharedPantherClient) {
                self::$sharedPantherClient->quit();
            }
        } catch (\Exception $e) {
            // Ignore errors when quitting
        } finally {
            self::$sharedPantherClient = null;
            self::$tabCounter = 0;
        }
    }

    /**
     * Enhanced test connection with JavaScript detection
     */
    public function testConnection(DataSource $dataSource): array
    {
        $result = parent::testConnection($dataSource);
        
        if ($result['success']) {
            // Test if JavaScript rendering might be needed
            $jsDetection = $this->detectJavaScriptRequirement($dataSource->source_url);
            $result['javascript_detected'] = $jsDetection['detected'];
            $result['javascript_indicators'] = $jsDetection['indicators'];
            $result['recommended_method'] = $jsDetection['detected'] ? 'javascript' : 'http';
        }
        
        return $result;
    }

    /**
     * Detect if a website requires JavaScript rendering
     */
    public function detectJavaScriptRequirement(string $url): array
    {
        try {
            $response = $this->getHttpClient()->get($url);
            $html = $response->getBody()->getContents();
            
            $indicators = [];
            $jsDetected = false;
            
            // Check for common JavaScript frameworks and patterns
            $jsPatterns = [
                'react' => ['React\.', 'react-dom', 'ReactDOM', '__REACT_DEVTOOLS__', 'data-reactroot'],
                'vue' => ['Vue\.', 'vue\.js', 'v-if', 'v-for', '{{.*}}', 'data-v-'],
                'angular' => ['angular', 'ng-app', 'ng-controller', 'data-ng-', '\[ng-'],
                'spa_indicators' => ['single.*page.*application', 'SPA', 'app\.js', 'bundle\.js'],
                'ajax_patterns' => ['XMLHttpRequest', 'fetch\(', '\.ajax\(', 'axios'],
                'dynamic_loading' => ['window\.onload', 'DOMContentLoaded', 'document\.ready'],
                'empty_content' => []
            ];
            
            foreach ($jsPatterns as $category => $patterns) {
                foreach ($patterns as $pattern) {
                    if (preg_match("/{$pattern}/i", $html)) {
                        $indicators[] = $category . ': ' . $pattern;
                        $jsDetected = true;
                    }
                }
            }
            
            // Check if the initial HTML has very little content (common in SPAs)
            $crawler = new Crawler($html);
            $bodyText = trim($crawler->filter('body')->text());
            $contentLength = strlen($bodyText);
            
            if ($contentLength < 500) {
                $indicators[] = "minimal_content: Body text only {$contentLength} characters";
                $jsDetected = true;
            }
            
            // Check for loading placeholders or spinners
            $loadingPatterns = ['loading', 'spinner', 'skeleton', 'placeholder'];
            foreach ($loadingPatterns as $pattern) {
                if (stripos($html, $pattern) !== false) {
                    $indicators[] = "loading_indicator: {$pattern}";
                    $jsDetected = true;
                }
            }
            
            return [
                'detected' => $jsDetected,
                'indicators' => $indicators,
                'content_length' => $contentLength,
                'recommendation' => $jsDetected ? 'Use JavaScript rendering for better results' : 'Static HTML parsing should be sufficient'
            ];
            
        } catch (\Exception $e) {
            return [
                'detected' => false,
                'indicators' => ['error: ' . $e->getMessage()],
                'content_length' => 0,
                'recommendation' => 'Unable to analyze - try JavaScript rendering if static fails'
            ];
        }
    }

    /**
     * Enhanced crawl company list with intelligent method selection
     */
    public function crawlCompanyList(DataSource $dataSource, int $maxPages = 5): array
    {
        // Always enable JavaScript for reliable content extraction
        if (!$this->useJavaScript) {
            Log::info("Force enabling JavaScript for reliable company list crawling: {$dataSource->name}");
            $this->setJavaScriptEnabled(true);
        }
        
        $companies = [];
        $errors = [];
        $method = 'javascript'; // Always use JavaScript
        $page = 1; // Initialize page counter outside try block
        
        Log::info("Starting enhanced crawl for {$dataSource->name} using {$method} method");

        try {
            for ($page = 1; $page <= $maxPages; $page++) {
                $url = $this->buildPaginatedUrl($dataSource->source_url, $page, $dataSource);
                
                Log::info("Crawling company list page {$page}: {$url}");
                
                // Fetch content using JavaScript method
                $content = $this->fetchContent($url);
                
                if (!$content['success']) {
                    $errors[] = "Failed to fetch page {$page}: " . $content['error'];
                    continue;
                }
                
                $crawler = new Crawler($content['html']);
                
                // Extract companies using configured selectors
                $pageCompanies = $this->extractCompaniesFromPage($crawler, $dataSource);
                
                if (empty($pageCompanies)) {
                    Log::info("No companies found on page {$page}, continuing pagination to respect max_pages setting");
                } else {
                    $companies = array_merge($companies, $pageCompanies);
                    Log::info("Found " . count($pageCompanies) . " companies on page {$page} (method: {$content['method']})");
                }
                
                // Respect rate limiting
                if ($page < $maxPages) {
                    sleep($this->requestDelay);
                }
            }
        } catch (\Exception $e) {
            $errors[] = "Crawling failed: " . $e->getMessage();
            Log::error("Enhanced crawl failed", [
                'error' => $e->getMessage(), 
                'url' => $url ?? $dataSource->source_url,
                'method' => $method
            ]);
        }

        return [
            'companies' => $companies,
            'errors' => $errors,
            'method_used' => $method,
            'pages_crawled' => min($page - 1, $maxPages),
            'total_found' => count($companies)
        ];
    }

    /**
     * Enhanced crawl company details with JavaScript support
     */
    public function crawlCompanyDetails(Company $company): array
    {
        $details = [];
        $errors = [];

        try {
            // First, try to find the company info page
            $infoPageUrl = $this->findCompanyInfoPage($company);
            
            if (!$infoPageUrl) {
                throw new \Exception("Could not find company info page");
            }

            Log::info("Enhanced crawling company details: {$infoPageUrl}");

            // Always enable JavaScript rendering for reliable content extraction
            if (!$this->useJavaScript) {
                Log::info("Force enabling JavaScript rendering for reliable content extraction");
                $this->setJavaScriptEnabled(true);
            }
            
            $content = $this->fetchContent($infoPageUrl);
            
            if (!$content['success']) {
                throw new \Exception("Failed to fetch content: " . $content['error']);
            }

            $crawler = new Crawler($content['html']);
            
            // Log HTML content to file for debugging
            $this->logHtmlToFile($content['html'], $infoPageUrl, $content['method'] ?? 'unknown');
            
            // Extract company details
            $details = $this->extractCompanyDetails($crawler, $company);
            $details['info_page_url'] = $infoPageUrl;
            
            // Extract executives
            $executives = $this->extractExecutives($crawler, $company);
            $details['executives'] = $executives;

            Log::info("Enhanced crawl completed for company {$company->name} using {$content['method']} method");

        } catch (\Exception $e) {
            $errors[] = "Enhanced crawl failed: " . $e->getMessage();
            Log::error("Failed enhanced company details crawl", [
                'company' => $company->name,
                'error' => $e->getMessage()
            ]);
        }

        return [
            'details' => $details,
            'errors' => $errors,
            'success' => empty($errors),
            'method_used' => isset($content) ? $content['method'] : 'unknown'
        ];
    }

    /**
     * Compare results between static and JavaScript methods
     */
    public function compareStaticVsJavaScript(string $url): array
    {
        Log::info("Comparing static vs JavaScript methods for: {$url}");
        
        // Fetch with static method
        $staticResult = $this->fetchContentWithHttp($url);
        $staticCrawler = $staticResult['success'] ? new Crawler($staticResult['html']) : null;
        
        // Fetch with JavaScript method
        $jsResult = $this->fetchContentWithJavaScript($url);
        $jsCrawler = $jsResult['success'] ? new Crawler($jsResult['html']) : null;
        
        $comparison = [
            'url' => $url,
            'static' => [
                'success' => $staticResult['success'],
                'content_length' => strlen($staticResult['html']),
                'body_text_length' => $staticCrawler ? strlen(trim($staticCrawler->filter('body')->text())) : 0,
                'link_count' => $staticCrawler ? $staticCrawler->filter('a')->count() : 0,
                'form_count' => $staticCrawler ? $staticCrawler->filter('form')->count() : 0,
            ],
            'javascript' => [
                'success' => $jsResult['success'],
                'content_length' => strlen($jsResult['html']),
                'body_text_length' => $jsCrawler ? strlen(trim($jsCrawler->filter('body')->text())) : 0,
                'link_count' => $jsCrawler ? $jsCrawler->filter('a')->count() : 0,
                'form_count' => $jsCrawler ? $jsCrawler->filter('form')->count() : 0,
            ]
        ];
        
        // Calculate improvement metrics
        if ($comparison['static']['success'] && $comparison['javascript']['success']) {
            $comparison['improvement'] = [
                'content_length_ratio' => $comparison['javascript']['content_length'] / max($comparison['static']['content_length'], 1),
                'body_text_ratio' => $comparison['javascript']['body_text_length'] / max($comparison['static']['body_text_length'], 1),
                'link_ratio' => $comparison['javascript']['link_count'] / max($comparison['static']['link_count'], 1),
                'recommended_method' => $comparison['javascript']['body_text_length'] > $comparison['static']['body_text_length'] * 1.5 ? 'javascript' : 'static'
            ];
        }
        
        return $comparison;
    }

    /**
     * Extract companies from a page using individual selector columns
     * Override parent method to support individual selector columns instead of JSON selectors
     */
    protected function extractCompaniesFromPage(Crawler $crawler, DataSource $dataSource): array
    {
        $companies = [];
        
        // Use individual selector columns instead of JSON selectors
        $nameSelector = $dataSource->company_name_selector;
        $linkSelector = $dataSource->company_link_selector;

        if (!$nameSelector) {
            Log::warning("No company name selector configured for data source: {$dataSource->name}");
            return $companies;
        }

        try {
            // For dt-based structure, use enhanced Japanese structure parsing
            if (strpos($nameSelector, 'dt') !== false) {
                // Use specialized method for Japanese company directories
                $companies = $this->extractJapaneseCompanyStructure($crawler, $dataSource);
                
                // If the specialized method didn't find anything, fall back to original method
                if (empty($companies)) {
                    Log::info("Japanese structure extraction found no companies, trying fallback method");
                    $companies = $this->extractDtStructureFallback($crawler, $nameSelector, $linkSelector, $dataSource);
                }
            } else {
                // Handle general structure
                $crawler->filter($nameSelector)->each(function (Crawler $nameNode, $i) use (&$companies, $linkSelector, $crawler, $dataSource) {
                    try {
                        $name = trim($nameNode->text());
                        
                        // Try to find corresponding link
                        $link = null;
                        
                        if ($linkSelector) {
                            // First try to find link within the same parent element  
                            $parentNode = $nameNode->closest('div, li, tr, article, dt') ?? $nameNode;
                            $linkNode = $parentNode->filter($linkSelector)->first();
                            
                            if ($linkNode->count() > 0) {
                                $link = $linkNode->attr('href');
                            } else {
                                // Fallback: find link by index
                                $allLinks = $crawler->filter($linkSelector);
                                if ($allLinks->count() > $i) {
                                    $link = $allLinks->eq($i)->attr('href');
                                }
                            }
                        }

                        if ($name) {
                            // Convert relative URLs to absolute if link exists
                            if ($link && !filter_var($link, FILTER_VALIDATE_URL)) {
                                $link = $this->makeAbsoluteUrl($dataSource->source_url, $link);
                            }
                            
                            $companies[] = [
                                'name' => $name,
                                'source_url' => $link ?? $dataSource->source_url,
                                'website' => $link ? $this->guessWebsiteFromLink($link, $name) : null,
                            ];
                            
                            Log::debug("Extracted company: {$name} with link: " . ($link ?? 'none'));
                        }
                    } catch (\Exception $e) {
                        Log::warning("Failed to extract company at index {$i}", ['error' => $e->getMessage()]);
                    }
                });
            }
        } catch (\Exception $e) {
            Log::error("Failed to extract companies from page", ['error' => $e->getMessage()]);
        }

        Log::info("Extracted " . count($companies) . " companies using selectors - Name: '{$nameSelector}', Link: '{$linkSelector}'");
        return $companies;
    }

    /**
     * Convert relative URL to absolute URL
     */
    private function makeAbsoluteUrl(string $baseUrl, string $relativeUrl): string
    {
        // If already absolute, return as is
        if (filter_var($relativeUrl, FILTER_VALIDATE_URL)) {
            return $relativeUrl;
        }
        
        $base = parse_url($baseUrl);
        
        // Handle protocol-relative URLs
        if (str_starts_with($relativeUrl, '//')) {
            return ($base['scheme'] ?? 'http') . ':' . $relativeUrl;
        }
        
        // Handle absolute path
        if (str_starts_with($relativeUrl, '/')) {
            return ($base['scheme'] ?? 'http') . '://' . $base['host'] . 
                   (isset($base['port']) ? ':' . $base['port'] : '') . $relativeUrl;
        }
        
        // Handle relative path
        $basePath = dirname($base['path'] ?? '/');
        if ($basePath === '.') $basePath = '/';
        
        return ($base['scheme'] ?? 'http') . '://' . $base['host'] . 
               (isset($base['port']) ? ':' . $base['port'] : '') . 
               rtrim($basePath, '/') . '/' . ltrim($relativeUrl, '/');
    }

    /**
     * Fallback method for dt structure extraction
     */
    private function extractDtStructureFallback(Crawler $crawler, string $nameSelector, ?string $linkSelector, DataSource $dataSource): array
    {
        $companies = [];
        
        $crawler->filter('dt')->each(function (Crawler $dtNode, $i) use (&$companies, $nameSelector, $linkSelector, $dataSource) {
            try {
                // Get company name from the strong tag within this dt
                $nameNode = $dtNode->filter('strong')->first();
                if ($nameNode->count() === 0) {
                    return; // Skip if no strong tag found
                }
                
                $name = trim($nameNode->text());
                
                // Get link from the a tag within the same dt
                $link = null;
                if ($linkSelector) {
                    // First try to find any a tag within this dt
                    $linkNode = $dtNode->filter('a')->first();
                    if ($linkNode->count() > 0) {
                        $link = $linkNode->attr('href');
                        Log::debug("Found link within dt: {$link} for company: {$name}");
                    } else {
                        // Fallback: try to find link in next sibling or parent
                        $parentNode = $dtNode->parents()->first();
                        if ($parentNode->count() > 0) {
                            $linkNode = $parentNode->filter('a')->first();
                            if ($linkNode->count() > 0) {
                                $link = $linkNode->attr('href');
                                Log::debug("Found link in parent: {$link} for company: {$name}");
                            }
                        }
                    }
                }

                if ($name) {
                    // Convert relative URLs to absolute if link exists
                    if ($link && !filter_var($link, FILTER_VALIDATE_URL)) {
                        $link = $this->makeAbsoluteUrl($dataSource->source_url, $link);
                    }
                    
                    // Validate that we have meaningful data
                    if (strlen($name) > 1) { // Company name should be at least 2 characters
                        $companies[] = [
                            'name' => $name,
                            'source_url' => $link ?? $dataSource->source_url,
                            'website' => $link ? $this->guessWebsiteFromLink($link, $name) : null,
                        ];
                        
                        Log::debug("Extracted company from dt (fallback): {$name} with link: " . ($link ?? 'none'));
                    } else {
                        Log::debug("Skipped company with invalid name: '{$name}'");
                    }
                }
            } catch (\Exception $e) {
                Log::warning("Failed to extract company from dt at index {$i} (fallback)", ['error' => $e->getMessage()]);
            }
        });
        
        return $companies;
    }

    /**
     * Enhanced parsing for Japanese company directory structures
     * Handles complex dt/dd structures where company names and links might be nested
     */
    private function extractJapaneseCompanyStructure(Crawler $crawler, DataSource $dataSource): array
    {
        $companies = [];
        
        try {
            // Method 1: Direct dt structure with strong and a tags
            $crawler->filter('dt')->each(function (Crawler $dtNode, $i) use (&$companies, $dataSource) {
                try {
                    $companyData = $this->extractFromDtNode($dtNode, $dataSource);
                    if ($companyData) {
                        $companies[] = $companyData;
                        Log::debug("Method 1 - Extracted: {$companyData['name']}");
                    }
                } catch (\Exception $e) {
                    Log::debug("Method 1 failed for dt {$i}: " . $e->getMessage());
                }
            });
            
            // Method 2: If no companies found, try alternative structure
            if (empty($companies)) {
                // Try finding strong tags first, then find corresponding links
                $strongNodes = $crawler->filter('strong');
                $linkNodes = $crawler->filter('a[href]');
                
                $strongNodes->each(function (Crawler $strongNode, $i) use (&$companies, $linkNodes, $dataSource) {
                    try {
                        $name = trim($strongNode->text());
                        if (strlen($name) > 1) {
                            // Try to find corresponding link
                            $link = $this->findCorrespondingLink($strongNode, $linkNodes, $name);
                            
                            if ($link) {
                                $link = $this->makeAbsoluteUrl($dataSource->source_url, $link);
                            }
                            
                            $companies[] = [
                                'name' => $name,
                                'source_url' => $link ?? $dataSource->source_url,
                                'website' => $link ? $this->guessWebsiteFromLink($link, $name) : null,
                            ];
                            
                            Log::debug("Method 2 - Extracted: {$name} with link: " . ($link ?? 'none'));
                        }
                    } catch (\Exception $e) {
                        Log::debug("Method 2 failed for strong {$i}: " . $e->getMessage());
                    }
                });
            }
            
        } catch (\Exception $e) {
            Log::error("Japanese structure extraction failed", ['error' => $e->getMessage()]);
        }
        
        return $companies;
    }
    
    /**
     * Extract company data from a single dt node
     */
    private function extractFromDtNode(Crawler $dtNode, DataSource $dataSource): ?array
    {
        // Get company name from strong tag
        $strongNode = $dtNode->filter('strong')->first();
        if ($strongNode->count() === 0) {
            return null;
        }
        
        $name = trim($strongNode->text());
        if (strlen($name) <= 1) {
            return null;
        }
        
        // Get link from a tag within the same dt
        $link = null;
        $linkNode = $dtNode->filter('a[href]')->first();
        if ($linkNode->count() > 0) {
            $link = $linkNode->attr('href');
            $link = $this->makeAbsoluteUrl($dataSource->source_url, $link);
        }
        
        return [
            'name' => $name,
            'source_url' => $link ?? $dataSource->source_url,
            'website' => $link ? $this->guessWebsiteFromLink($link, $name) : null,
        ];
    }
    
    /**
     * Find corresponding link for a company name
     */
    private function findCorrespondingLink(Crawler $strongNode, Crawler $linkNodes, string $companyName): ?string
    {
        // Strategy 1: Find link within the same parent element
        $parentNode = $strongNode->parents()->first();
        if ($parentNode->count() > 0) {
            $nearbyLink = $parentNode->filter('a[href]')->first();
            if ($nearbyLink->count() > 0) {
                return $nearbyLink->attr('href');
            }
        }
        
        // Strategy 2: Find link that contains company name in href or title
        $foundLink = null;
        $linkNodes->each(function (Crawler $linkNode) use (&$foundLink, $companyName) {
            if ($foundLink) return; // Already found
            
            $href = $linkNode->attr('href');
            $title = $linkNode->attr('title') ?? '';
            $linkText = trim($linkNode->text());
            
            // Check if link contains company name or vice versa
            if (
                stripos($href, $companyName) !== false ||
                stripos($title, $companyName) !== false ||
                stripos($linkText, $companyName) !== false ||
                stripos($companyName, $linkText) !== false
            ) {
                $foundLink = $href;
            }
        });
        
        return $foundLink;
    }

    /**
     * Enhanced waiting strategy for complete page load with JavaScript rendering
     */
    private function waitForCompletePageLoad(string $url, $webDriver = null): void
    {
        // Use provided webDriver or fall back to pantherClient
        $driver = $webDriver ?: ($this->pantherClient ? $this->pantherClient->getWebDriver() : null);
        
        if (!$driver) {
            Log::warning("No WebDriver available for page load waiting");
            return;
        }
        
        $maxWaitTime = 45; // Tăng thời gian đợi tối đa
        $checkInterval = 0.5; // Kiểm tra thường xuyên hơn
        $startTime = time();
        
        Log::info("🕐 Đang đợi page load hoàn toàn", [
            'url' => $url,
            'max_wait_time' => $maxWaitTime,
        ]);

        // Step 1: Đợi cấu trúc page cơ bản
        try {
            $wait = new WebDriverWait($driver, 15);
            $wait->until(WebDriverExpectedCondition::presenceOfElementLocated(
                WebDriverBy::tagName('body')
            ));
            Log::info("✅ Cấu trúc page cơ bản đã load");
        } catch (\Exception $e) {
            Log::warning("⚠️ Timeout đợi body, tiếp tục anyway");
        }

        // Step 2: Đợi DOM ready
        $this->waitForDOMReady($driver);

        // Step 3: Advanced content detection với nhiều chiến lược
        $previousLength = 0;
        $stableCount = 0;
        $requiredStableChecks = 5; // Tăng số lần kiểm tra ổn định
        $lastSignificantChange = time();

        while ((time() - $startTime) < $maxWaitTime) {
            try {
                // Kiểm tra loading indicators
                $loadingIndicators = $this->checkForLoadingIndicators($driver);
                if ($loadingIndicators['hasLoading']) {
                    Log::info("🔄 Đang có loading indicators: " . implode(', ', $loadingIndicators['indicators']));
                    $lastSignificantChange = time();
                    sleep($checkInterval);
                    continue;
                }

                // Kiểm tra network activity (nếu có thể)
                $this->waitForNetworkIdle($driver);

                // Phân tích content
                $currentHtml = $driver->getPageSource();
                $currentLength = strlen($currentHtml);
                $contentMetrics = $this->analyzePageContent($currentHtml);
                
                // Kiểm tra content stability
                $lengthDifference = abs($currentLength - $previousLength);
                
                if ($lengthDifference < 100) { // Chấp nhận thay đổi nhỏ (< 100 ký tự)
                    $stableCount++;
                } else {
                    $stableCount = 0;
                    $previousLength = $currentLength;
                    $lastSignificantChange = time();
                }

                Log::info("📊 Phân tích content", [
                    'length' => $currentLength,
                    'length_diff' => $lengthDifference,
                    'stable_count' => $stableCount,
                    'text_length' => $contentMetrics['textLength'],
                    'element_count' => $contentMetrics['elementCount'],
                    'time_elapsed' => time() - $startTime
                ]);

                // Điều kiện để coi như page đã load xong
                $hasMinimalContent = $contentMetrics['textLength'] > 200 && $contentMetrics['elementCount'] > 5;
                $isStable = $stableCount >= $requiredStableChecks;
                $noRecentChanges = (time() - $lastSignificantChange) >= 3; // 3 giây không có thay đổi lớn

                if ($hasMinimalContent && ($isStable || $noRecentChanges)) {
                    Log::info("✅ Page đã load hoàn toàn", [
                        'reason' => $isStable ? 'content_stable' : 'no_recent_changes',
                        'stable_count' => $stableCount,
                        'no_change_duration' => time() - $lastSignificantChange
                    ]);
                    break;
                }

                // Kiểm tra business page content cụ thể
                if ($this->checkForBusinessPageContent($driver)) {
                    Log::info("✅ Business page content đã được detect");
                    sleep(2); // Đợi thêm 2 giây để đảm bảo
                    break;
                }

                sleep($checkInterval);

            } catch (\Exception $e) {
                Log::warning("Lỗi trong quá trình kiểm tra content: " . $e->getMessage());
                sleep($checkInterval);
            }
        }

        $totalWaitTime = time() - $startTime;
        Log::info("🏁 Hoàn thành đợi page load", [
            'total_wait_time' => $totalWaitTime,
            'max_wait_time' => $maxWaitTime,
            'final_content_length' => strlen($driver->getPageSource() ?? ''),
            'timeout' => $totalWaitTime >= $maxWaitTime
        ]);
    }

    /**
     * Đợi DOM ready state
     */
    private function waitForDOMReady($webDriver): void
    {
        try {
            // Chờ document.readyState === 'complete'
            $wait = new \Facebook\WebDriver\WebDriverWait($webDriver, 10);
            $wait->until(function() use ($webDriver) {
                return $webDriver->executeScript('return document.readyState === "complete"');
            });
            Log::info("✅ DOM ready state: complete");
        } catch (\Exception $e) {
            try {
                // Fallback: chờ document.readyState === 'interactive'
                $wait = new \Facebook\WebDriver\WebDriverWait($webDriver, 5);
                $wait->until(function() use ($webDriver) {
                    return $webDriver->executeScript('return document.readyState === "interactive"');
                });
                Log::info("✅ DOM ready state: interactive");
            } catch (\Exception $e2) {
                Log::warning("⚠️ Không thể đợi DOM ready, tiếp tục anyway");
            }
        }
    }

    /**
     * Đợi network activity idle (không có request đang chạy)
     */
    private function waitForNetworkIdle($webDriver): void
    {
        try {
            // Thực thi JavaScript để kiểm tra network activity
            $script = "
                return new Promise((resolve) => {
                    let idleTime = 0;
                    const checkIdle = () => {
                        // Kiểm tra XMLHttpRequest đang active
                        const activeRequests = window.performance.getEntriesByType('resource')
                            .filter(entry => entry.transferSize === 0).length;
                        
                        if (activeRequests === 0) {
                            idleTime += 100;
                            if (idleTime >= 1000) { // 1 giây idle
                                resolve(true);
                                return;
                            }
                        } else {
                            idleTime = 0;
                        }
                        
                        setTimeout(checkIdle, 100);
                    };
                    
                    setTimeout(checkIdle, 100);
                    setTimeout(() => resolve(false), 5000); // Timeout sau 5 giây
                });
            ";
            
            $result = $webDriver->executeAsyncScript($script);
            if ($result) {
                Log::info("✅ Network idle detected");
            }
        } catch (\Exception $e) {
            // Không critical, chỉ log
            Log::debug("Network idle check failed: " . $e->getMessage());
        }
    }

    /**
     * Check for loading indicators on the page
     */
    private function checkForLoadingIndicators($webDriver): array
    {
        $indicators = [];
        $hasLoading = false;

        try {
            // Common loading selectors (mở rộng danh sách)
            $loadingSelectors = [
                '.loading', '.spinner', '.loader', '#loading',
                '[class*="loading"]', '[class*="spinner"]', '[class*="preloader"]',
                '.elementor-loading', '.wp-block-loading', '.skeleton',
                '.placeholder', '.shimmer', '.loading-animation',
                '.fa-spinner', '.fas.fa-spinner', '.loading-dots',
                '[aria-label*="loading"]', '[aria-label*="Loading"]',
                '.progress', '.progress-bar'
            ];

            foreach ($loadingSelectors as $selector) {
                try {
                    $elements = $webDriver->findElements(WebDriverBy::cssSelector($selector));
                    if (count($elements) > 0) {
                        foreach ($elements as $element) {
                            try {
                                // Kiểm tra element có hiển thị không
                                if ($element->isDisplayed()) {
                                    $indicators[] = $selector;
                                    $hasLoading = true;
                                    break 2; // Break khỏi cả 2 loops
                                }
                            } catch (\Exception $e) {
                                // Element có thể không accessible, skip
                            }
                        }
                    }
                } catch (\Exception $e) {
                    // Ignore individual selector errors
                }
            }

            // Kiểm tra loading bằng JavaScript
            try {
                $jsLoadingCheck = $webDriver->executeScript("
                    // Kiểm tra các loading indicators phổ biến
                    const loadingElements = document.querySelectorAll('[class*=\"loading\"], [class*=\"spinner\"]');
                    for (let el of loadingElements) {
                        const style = window.getComputedStyle(el);
                        if (style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0') {
                            return true;
                        }
                    }
                    
                    return false;
                ");
                
                if ($jsLoadingCheck) {
                    $indicators[] = 'javascript_loading_check';
                    $hasLoading = true;
                }
            } catch (\Exception $e) {
                Log::debug("JavaScript loading check failed: " . $e->getMessage());
            }

        } catch (\Exception $e) {
            Log::warning("Error checking loading indicators: " . $e->getMessage());
        }

        return [
            'hasLoading' => $hasLoading,
            'indicators' => array_unique($indicators)
        ];
    }

    /**
     * Analyze page content for completeness
     */
    private function analyzePageContent(string $html): array
    {
        $crawler = new \Symfony\Component\DomCrawler\Crawler($html);
        
        $textLength = 0;
        $elementCount = 0;

        try {
            $textLength = strlen(trim($crawler->filter('body')->text()));
            $elementCount = $crawler->filter('*')->count();
        } catch (\Exception $e) {
            Log::warning("Error analyzing page content: " . $e->getMessage());
        }

        return [
            'textLength' => $textLength,
            'elementCount' => $elementCount
        ];
    }

    /**
     * Check for business page specific content với logic nâng cao (với WebDriver parameter)
     */
    private function checkForBusinessPageContent($webDriver): bool
    {
        try {
            // Kiểm tra business selectors cơ bản
            $basicBusinessSelectors = [
                'h1', 'h2', 'h3', // Headlines
                '.content', '.main-content', '#main', '#content', // Main content areas
                'article', 'main', 'section[class*="content"]', // Semantic content
            ];

            $basicContentFound = false;
            foreach ($basicBusinessSelectors as $selector) {
                try {
                    $elements = $webDriver->findElements(WebDriverBy::cssSelector($selector));
                    if (count($elements) > 0) {
                        $basicContentFound = true;
                        break;
                    }
                } catch (\Exception $e) {
                    // Continue với selector tiếp theo
                }
            }

            if (!$basicContentFound) {
                return false;
            }

            // Kiểm tra company-specific content
            $companySelectors = [
                '[class*="company"]', '[class*="about"]', '[class*="profile"]',
                '[class*="corporate"]', '[class*="organization"]', '[class*="business"]',
                '[id*="company"]', '[id*="about"]', '[id*="profile"]',
                // Japanese business terms
                '[class*="kaisha"]', '[class*="kigyou"]', '[class*="gaiyou"]',
            ];

            $companyContentFound = false;
            foreach ($companySelectors as $selector) {
                try {
                    $elements = $webDriver->findElements(WebDriverBy::cssSelector($selector));
                    if (count($elements) > 0) {
                        $companyContentFound = true;
                        Log::debug("Business content found with selector: {$selector}");
                        break;
                    }
                } catch (\Exception $e) {
                    // Continue
                }
            }

            // Kiểm tra bằng JavaScript để tìm text patterns
            try {
                $jsBusinessCheck = $webDriver->executeScript("
                    const bodyText = document.body.innerText || document.body.textContent || '';
                    const businessPatterns = [
                        '会社', 'Company', 'About', '代表', 'CEO', 'President',
                        '設立', 'Founded', 'Established', '概要', 'Overview'
                    ];
                    
                    for (let pattern of businessPatterns) {
                        if (bodyText.includes(pattern)) {
                            return true;
                        }
                    }
                    return false;
                ");
                
                if ($jsBusinessCheck) {
                    Log::debug("Business content found via JavaScript text analysis");
                    $companyContentFound = true;
                }
            } catch (\Exception $e) {
                Log::debug("JavaScript business check failed: " . $e->getMessage());
            }

            return $basicContentFound && $companyContentFound;

        } catch (\Exception $e) {
            Log::warning("Error checking business page content: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Log HTML content to file for debugging purposes
     */
    private function logHtmlToFile(string $html, string $url, string $method): void
    {
        try {
            $domain = parse_url($url, PHP_URL_HOST) ?? 'unknown';
            $timestamp = now()->format('Y-m-d_H-i-s');
            $filename = "html-fetch_{$domain}_{$method}_{$timestamp}.html";
            $logDir = storage_path('app/private/html-fetch');
            
            // Ensure directory exists
            if (!file_exists($logDir)) {
                mkdir($logDir, 0755, true);
            }
            
            $filePath = $logDir . '/' . $filename;
            
            // Prepare log content with metadata
            $logContent = "<!--\n";
            $logContent .= "URL: {$url}\n";
            $logContent .= "Method: {$method}\n";
            $logContent .= "Timestamp: " . now()->toISOString() . "\n";
            $logContent .= "Content Length: " . strlen($html) . " bytes\n";
            $logContent .= "-->\n\n";
            $logContent .= $html;
            
            file_put_contents($filePath, $logContent);
            
            Log::info("HTML content logged to file", [
                'url' => $url,
                'method' => $method,
                'file_path' => $filePath,
                'content_length' => strlen($html)
            ]);
            
        } catch (\Exception $e) {
            Log::warning("Failed to log HTML to file: " . $e->getMessage());
        }
    }

    /**
     * Clean up resources with tab management (destructor)
     */
    public function __destruct()
    {
        // Only cleanup current tab, not entire browser
        $this->cleanupPantherClient();
    }
    
    /**
     * Static method to cleanup all Chrome when application shuts down
     */
    public static function shutdown(): void
    {
        self::forceCleanupAllChrome();
    }
}
